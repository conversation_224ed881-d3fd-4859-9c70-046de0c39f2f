---
import type { Props } from '../props';

const { banner } = Astro.props.entry.data;
---

{banner && <div class="sl-banner" set:html={banner.content} />}

<style>
	.sl-banner {
		--__sl-banner-text: var(--sl-color-banner-text, var(--sl-color-text-invert));
		padding: var(--sl-nav-pad-y) var(--sl-nav-pad-x);
		background-color: var(--sl-color-banner-bg, var(--sl-color-bg-accent));
		color: var(--__sl-banner-text);
		line-height: var(--sl-line-height-headings);
		text-align: center;
		text-wrap: balance;
		box-shadow: var(--sl-shadow-sm);
	}
	.sl-banner :global(a) {
		color: var(--__sl-banner-text);
	}
</style>
