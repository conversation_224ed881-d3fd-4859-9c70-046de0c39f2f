---
title: 플러그인 및 통합
description: Starlight를 확장하는 플러그인 및 통합과 같은 커뮤니티 도구를 찾아보세요!
sidebar:
  order: 1
---

:::tip[직접 추가하세요!]
Starlight용 플러그인이나 도구를 구축하셨나요?
이 페이지에 링크를 추가하여 PR을 열어보세요!
:::

## 플러그인

[플러그인](/ko/reference/plugins/)은 Starlight 구성, UI 및 동작을 사용자 정의하는 동시에 쉽게 공유하고 재사용할 수 있습니다.
Starlight 팀이 지원하는 공식 플러그인과 Starlight 사용자가 관리하는 커뮤니티 플러그인으로 사이트를 확장하세요.

### 공식 플러그인

<CardGrid>
	<LinkCard
		href="/ko/guides/site-search/#algolia-docsearch"
		title="Algolia DocSearch"
		description="기본 검색 공급자인 Pagefind를 Algolia DocSearch로 바꾸세요."
	/>
</CardGrid>

### 커뮤니티 플러그인

<CardGrid>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-links-validator"
		title="starlight-links-validator"
		description="Starlight 페이지에 끊어진 링크가 있는지 확인하세요."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-typedoc"
		title="starlight-typedoc"
		description="TypeDoc을 사용하여 TypeScript로 Starlight 페이지를 생성하세요."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-blog"
		title="starlight-blog"
		description="문서 사이트에 블로그를 추가하세요."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-openapi"
		title="starlight-openapi"
		description="OpenAPI/Swagger 사양에서 문서 페이지를 만드세요."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-obsidian"
		title="starlight-obsidian"
		description="Starlight 사이트에 Obsidian Vault를 게시하세요."
	/>
	<LinkCard
		href="https://astro-ghostcms.xyz/intro/starlight/install/"
		title="starlight-ghostcms"
		description="Starlight 문서와 함께 GhostCMS 블로그 게시물을 추가하세요."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-image-zoom"
		title="starlight-image-zoom"
		description="문서 이미지에 확대/축소 기능을 추가하세요."
	/>
	<LinkCard
		href="https://github.com/lorenzolewis/starlight-utils"
		title="starlight-utils"
		description="일반적인 유틸리티 모음으로 Starlight를 확장하세요."
	/>
	<LinkCard
		href="https://github.com/trueberryless/starlight-view-modes"
		title="starlight-view-modes"
		description="문서 웹사이트에 다양한 보기 모드 기능을 추가하세요."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-versions"
		title="starlight-versions"
		description="Starlight 문서 페이지 버전을 지정하세요."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-theme-rapide"
		title="starlight-theme-rapide"
		description="Visual Studio Code Vitesse 테마에서 영감을 받은 Starlight 테마입니다."
	/>
</CardGrid>

## 커뮤니티 도구 및 통합

import { CardGrid, LinkCard } from '@astrojs/starlight/components';

이러한 커뮤니티 도구 및 통합을 사용하여 Starlight 사이트에 기능을 추가할 수 있습니다.

<CardGrid>
	<LinkCard
		href="https://www.feelback.dev/blog/new-astro-starlight-integration/"
		title="FeelBack"
		description="문서 페이지에 사용자 피드백 시스템을 추가하세요."
	/>
	<LinkCard
		href="https://github.com/val-town/notion-to-astro"
		title="notion-to-astro"
		description="Notion 내보내기를 Astro Starlight 문서로 변환하세요"
	/>
	<LinkCard
		href="https://github.com/mattjennings/astro-live-code"
		title="astro-live-code"
		description="MDX 코드 블록을 대화형 컴포넌트로 렌더링하세요"
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-i18n"
		title="starlight-i18n"
		description="Starlight 페이지를 번역하는 데 도움이 되는 Visual Studio Code 확장입니다."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-package-managers"
		title="starlight-package-managers"
		description="여러 패키지 관리자에 대한 npm 관련 명령을 빠르게 표시합니다."
	/>
	<LinkCard
		href="https://github.com/HiDeoo/starlight-showcases"
		title="starlight-showcases"
		description="쇼케이스 페이지를 작성하기 위한 Starlight 컴포넌트의 집합입니다."
	/>
</CardGrid>
