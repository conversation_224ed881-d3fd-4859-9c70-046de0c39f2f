---
title: <PERSON><PERSON><PERSON><PERSON> Konfigurasi
description: Ringkasan dari semua opsi konfigurasi yang didukung oleh Starlight.
---

## Mengonfigurasi integrasi `starlight`

Starlight adalah integrasi yang dibangun di atas web framework [Astro](https://astro.build). Anda dapat mengonfigurasi proyek Anda di dalam file konfigurasi `astro.config.mjs`:

```js
// astro.config.mjs
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'Website dokumentasi yang memukau',
		}),
	],
});
```

Anda dapat meneruskan opsi-opsi berikut ke integrasi starlight.

### `title` (wajib)

**type:** `string`

Atur judul untuk website Anda. Akan digunakan dalam metadata dan di judul tab browser.

### `description`

**type:** `string`

Atur deskripsi untuk website Anda. Digunakan dalam metadata yang dibagikan dengan mesin pencari di tag `<meta name="description">` jika `description` tidak diatur dalam frontmatter halaman.

### `logo`

**type:** [`LogoConfig`](#logoconfig)

Atur gambar logo untuk ditampilkan di bilah navigasi bersamaan dengan atau sebagai pengganti judul website. Anda dapat mengatur properti `src` tunggal atau mengatur sumber gambar terpisah untuk mode `light` dan `dark`.

```js
starlight({
	logo: {
		src: './src/assets/logo-saya.svg',
	},
});
```

#### `LogoConfig`

```ts
type LogoConfig = { alt?: string; replacesTitle?: boolean } & (
	| { src: string }
	| { light: string; dark: string }
);
```

### `tableOfContents`

**type:** `false | { minHeadingLevel?: number; maxHeadingLevel?: number; }`  
**default:** `{ minHeadingLevel: 2; maxHeadingLevel: 3; }`

Konfigurasi daftar isi yang ditampilkan di sebelah kanan setiap halaman. Secara default, judul `<h2>` dan `<h3>` akan dimasukkan dalam daftar isi ini.

### `editLink`

**type:** `{ baseUrl: string }`

Aktifkan tautan "Edit halaman ini" dengan mengatur base URL yang harus digunakan. Tautan akhir akan menjadi `editLink.baseUrl` + path halaman saat ini. Sebagai contoh, untuk mengaktifkan pengeditan halaman di repositori `withastro/starlight` di GitHub:

```js
starlight({
	editLink: {
		baseUrl: 'https://github.com/withastro/starlight/edit/main/',
	},
});
```

Dengan konfigurasi ini, halaman `/introduction` akan memiliki tautan untuk mengedit yang mengarah ke `https://github.com/withastro/starlight/edit/main/src/content/docs/introduction.md`.

### `sidebar`

**type:** [`SidebarItem[]`](#sidebaritem)

Konfigurasikan item navigasi sidebar website Anda.

Sidebar adalah array dari tautan dan grup tautan.
Setiap item harus memiliki `label` dan salah satu properti berikut:

- `link` — tautan tunggal ke URL tertentu, misalnya `'/home'` atau `'https://example.com'`.

- `items` — array yang berisikan lebih banyak tautan sidebar dan sub-grup.

- `autogenerate` — objek yang menentukan direktori mana di website dokumentasi Anda yang akan digunakan untuk secara otomatis menghasilkan grup tautan.

```js
starlight({
	sidebar: [
		// Item tautan tunggal dengan label "Beranda".
		{ label: 'Home', link: '/' },
		// Grup dengan label "Mulai dari Sini" yang berisi dua tautan.
		{
			label: 'Mulai dari Sini',
			items: [
				{ label: 'Pengantar', link: '/intro' },
				{ label: 'Langkah Selanjutnya', link: '/next-steps' },
			],
		},
		// Sebuah grup yang menghubungkan ke semua halaman di direktori referensi.
		{
			label: 'Referensi',
			autogenerate: { directory: 'reference' },
		},
	],
});
```

#### Urutan

Grup sidebar yang dihasilkan secara otomatis diurutkan berdasarkan nama file secara alfabetis.
Sebagai contoh, halaman yang dihasilkan dari `astro.md` akan muncul di atas halaman untuk `starlight.md`.

#### Grup yang bisa dilipat

Grup tautan dilebarkan secara default. Anda dapat mengubah perilaku ini dengan mengatur properti `collapsed` dari grup tersebut menjadi `true`.

Sub-grup yang dihasilkan secara otomatis akan mengikuti properti `collapsed` dari parent group-nya secara default. Atur properti `autogenerate.collapsed` untuk mengganti pengaturan ini.

```js
sidebar: [
  // Grup yang terlipat
  {
    label: 'Grup yang Terlipat',
    collapsed: true,
    items: [
      { label: 'Pengantar', link: '/intro' },
      { label: 'Langkah Selanjutnya', link: '/next-steps' },
    ],
  },
  // Grup yang dilebarkan yang berisi autogenerated sub-grup yang terlipat.
  {
    label: 'Referensi',
    autogenerate: {
      directory: 'reference',
      collapsed: true,
    },
  },
],
```

#### Menerjemahkan label

Jika website Anda mendukung banyak bahasa, `label` dari setiap item dianggap berada dalam bahasa default. Anda dapat mengatur properti `translations` untuk menyediakan label dalam bahasa-bahasa lain yang didukung:

```js
sidebar: [
  // Contoh sidebar dengan label-label yang diterjemahkan ke Bahasa Portugis Brasil.
  {
    label: 'Mulai dari Sini',
    translations: { 'pt-BR': 'Comece Aqui' },
    items: [
      {
        label: 'Pengantar',
        translations: { 'pt-BR': 'Introdução' },
        link: '/getting-started',
      },
      {
        label: 'Struktur Proyek',
        translations: { 'pt-BR': 'Estrutura de Projetos' },
        link: '/structure',
      },
    ],
  },
],
```

#### `SidebarItem`

```ts
type SidebarItem = {
	label: string;
	translations?: Record<string, string>;
	badge?: string | BadgeConfig;
} & (
	| {
			link: string;
			attrs?: Record<string, string | number | boolean | undefined>;
	  }
	| { items: SidebarItem[]; collapsed?: boolean }
	| {
			autogenerate: { directory: string; collapsed?: boolean };
			collapsed?: boolean;
	  }
);
```

#### `BadgeConfig`

```ts
interface BadgeConfig {
	text: string;
	variant: 'note' | 'tip' | 'caution' | 'danger' | 'success' | 'default';
}
```

### `locales`

**type:** <code>\{ \[dir: string\]: [LocaleConfig](#localeconfig) \}</code>

[Mengonfigurasi internasionalisasi (i18n)](/id/guides/i18n/) untuk website Anda dengan mengatur `locales` yang mana yang didukung.

Setiap entri harus menggunakan direktori di mana file-file bahasa tersebut disimpan sebagai nama propertinya.

```js
// astro.config.mjs
import { defineConfig } from 'astro/config';
import starlight from '@astrojs/starlight';

export default defineConfig({
	integrations: [
		starlight({
			title: 'My Site',
			// Tetapkan Bahasa Inggris sebagai bahasa default untuk website ini.
			defaultLocale: 'en',
			locales: {
				// Dokumentasi berbahasa Inggris di `src/content/docs/en/`
				en: {
					label: 'English',
				},
				// Dokumentasi berbahasa Cina (yang disederhanakan) di `src/content/docs/zh-cn/`
				'zh-cn': {
					label: '简体中文',
					lang: 'zh-CN',
				},
				// Dokumentasi berbahasa Arab di `src/content/docs/ar/`
				ar: {
					label: 'العربية',
					dir: 'rtl',
				},
			},
		}),
	],
});
```

#### `LocaleConfig`

```ts
interface LocaleConfig {
	label: string;
	lang?: string;
	dir?: 'ltr' | 'rtl';
}
```

Anda dapat mengatur opsi berikut untuk setiap bahasa:

##### `label` (wajib)

**type:** `string`

Label untuk bahasa ini yang ditampilkan kepada pengguna, misalnya di pemilih bahasa. Biasanya, Anda ingin menggunakan nama bahasa ini seperti yang diharapkan oleh pengguna yang menggunakan bahasa tersebut, contohnya `"English"`, `"العربية"`, atau `"简体中文"`.

##### `lang`

**type:** `string`

Tag BCP-47 untuk bahasa saat ini, misalnya `"en"`, `"ar"`, atau `"zh-CN"`. Jika tidak diatur, nama direktori bahasa akan digunakan secara default. Tag bahasa dengan subtag regional (misalnya `"pt-BR"` atau `"en-US"`) akan menggunakan terjemahan UI bawaan untuk bahasa dasarnya jika tidak ada terjemahan khusus untuk wilayah ditemukan.

##### `dir`

**type:** `'ltr' | 'rtl'`

Arah penulisan bahasa ini; `"ltr"` untuk kiri-ke-kanan (default) atau `"rtl"` untuk kanan-ke-kiri.

#### Bahasa utama

Anda dapat serve bahasa default tanpa direktori `/lang/` dengan mengatur bahasa `root`:

```js
starlight({
	locales: {
		root: {
			label: 'English',
			lang: 'en',
		},
		fr: {
			label: 'Français',
		},
	},
});
```

Sebagai contoh, ini memungkinkan Anda untuk serve `/getting-started/` sebagai path untuk Bahasa Inggris dan menggunakan `/fr/getting-started/` untuk halaman yang sama dalam Bahasa Prancis.

### `defaultLocale`

**type:** `string`

Menetapkan bahasa yang menjadi default untuk website ini.
Nilainya harus cocok dengan salah satu properti objek [`locales`](#locales) Anda.
(Jika bahasa default Anda adalah [bahasa utama](#bahasa-utama) Anda, Anda dapat melewatkannya.)

Bahasa default akan digunakan untuk memberikan konten cadangan ketika terjemahan tidak tersedia.

### `social`

import SocialLinksType from '~/components/social-links-type.astro';

**type:** <SocialLinksType />

Rincian opsional tentang akun media sosial untuk website ini. Menambahkan salah satu dari ini akan menampilkan mereka sebagai tautan ikon di header website.

```js
starlight({
	social: {
		codeberg: 'https://codeberg.org/knut/examples',
		discord: 'https://astro.build/chat',
		github: 'https://github.com/withastro/starlight',
		gitlab: 'https://gitlab.com/delucis',
		linkedin: 'https://www.linkedin.com/company/astroinc',
		mastodon: 'https://m.webtoo.ls/@astro',
		threads: 'https://www.threads.net/@nmoodev',
		twitch: 'https://www.twitch.tv/bholmesdev',
		twitter: 'https://twitter.com/astrodotbuild',
		'x.com': 'https://x.com/astrodotbuild',
		youtube: 'https://youtube.com/@astrodotbuild',
	},
});
```

### `customCss`

**type:** `string[]`

Sediakan file CSS untuk menyesuaikan tampilan dan nuansa website Starlight Anda.

Mendukung file CSS lokal yang bersifat relatif terhadap path proyek Anda, misalnya `'./src/custom.css'`, dan CSS yang Anda instal sebagai modul npm, misalnya `'@fontsource/roboto'`.

```js
starlight({
	customCss: ['./src/custom-styles.css', '@fontsource/roboto'],
});
```

### `head`

**type:** [`HeadConfig[]`](#headconfig)

Tambahkan tag kustom ke `<head>` website Starlight Anda.
Bisa berguna untuk menambahkan script analitik dan skrip pihak ketiga lainnya.

```js
starlight({
	head: [
		// Contoh: menambahkan tag skrip analitik Fathom.
		{
			tag: 'script',
			attrs: {
				src: 'https://cdn.usefathom.com/script.js',
				'data-site': 'MY-FATHOM-ID',
				defer: true,
			},
		},
	],
});
```

#### `HeadConfig`

```ts
interface HeadConfig {
	tag: string;
	attrs?: Record<string, string | boolean | undefined>;
	content?: string;
}
```

### `lastUpdated`

**type:** `boolean`  
**default:** `false`

Atur apakah footer menampilkan kapan halaman terakhir diperbarui.

Secara default, fitur ini mengandalkan histori Git repositori Anda dan mungkin tidak akurat pada beberapa deployment platform yang melakukan [shallow clones](https://git-scm.com/docs/git-clone#Documentation/git-clone.txt---depthltdepthgt). Halaman dapat mengesampingkan pengaturan ini atau tanggal Git-based menggunakan kolom [`lastUpdated` pada frontmatter](/id/reference/frontmatter/#lastupdated).

### `pagination`

**type:** `boolean`  
**default:** `true`

Tentukan apakah footer harus menampilkan tautan ke halaman sebelumnya dan halaman berikutnya.

Halaman dapat mengesampingkan pengaturan ini atau teks tautan dan/atau URL menggunakan kolom [`prev`](/id/reference/frontmatter/#prev) dan [`next`](/id/reference/frontmatter/#next) pada frontmatter.

### `favicon`

**type:** `string`  
**default:** `'/favicon.svg'`

Atur path favicon default untuk website Anda yang harus berada di direktori `public/` dan merupakan file ikon yang valid (`.ico`, `.gif`, `.jpg`, `.png`, atau `.svg`).

```js
starlight({
  favicon: '/images/favicon.svg',
}),
```

Jika Anda perlu mengatur varian tambahan atau favicon cadangan, Anda dapat menambahkan tag menggunakan opsi [`head`](#head):

```js
starlight({
  favicon: '/images/favicon.svg'.
  head: [
    // Tambahkan favicon ICO cadangan untuk Safari.
    {
      tag: 'link',
      attrs: {
        rel: 'icon',
        href:'/images/favicon.ico',
        sizes: '32x32',
      },
    },
  ],
});
```

### `titleDelimiter`

**type:** `string`  
**default:** `'|'`

Atur pemisah antara judul halaman dan judul website di tag `<title>` halaman, yang ditampilkan pada tab browser.

Secara default, setiap halaman memiliki `<title>` berupa `Judul Halaman | Judul Website`.
Sebagai contoh, halaman ini berjudul "Referensi Konfigurasi" dan website ini berjudul "Starlight", sehingga `<title>` untuk halaman ini adalah "Referensi Konfigurasi | Starlight".

### `components`

**type:** `Record<string, string>`

Berikan path ke komponen untuk mengganti implementasi default Starlight.

```js
starlight({
	components: {
		SocialLinks: './src/components/MySocialLinks.astro',
	},
});
```

Lihat [Referensi Penggantian](/id/reference/overrides/) untuk rincian semua komponen yang dapat Anda ganti.
