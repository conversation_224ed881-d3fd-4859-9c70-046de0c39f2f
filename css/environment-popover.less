
#mathlive-environment-popover.is-visible {
  visibility: visible;
}

#mathlive-environment-popover {
  --_environment-panel-height: var(--environment-panel-height, 70px);
  --_accent-color: var(--accent-color, #aaa);
  --_background: var(--environment-panel-background, #fff);
  --_button-background: var(--environment-panel-button-background, white);
  --_button-background-hover: var(--environment-panel-button-background-hover, #f5f5f7);
  --_button-background-active: var(--environment-panel-button-background-active, #f5f5f7);
  --_button-text: var(--environment-panel-button-text,  #e3e4e8);

  position: absolute;
  width: calc(var(--_environment-panel-height) * 2);
  height: var(--_environment-panel-height);
  border-radius: 4px;
  border: 1.5px solid var(--_accent-color);
  background-color: var(--_background);
  box-shadow: 0 0 30px 0 var(--environment-shadow, rgba(0, 0, 0, 0.4));
  pointer-events: all;
  visibility: hidden;
  
  .MLEP__array-buttons {
    height: calc(var(--_environment-panel-height) * 5/4);
    width: calc(var(--_environment-panel-height) * 5/4);
    margin-left: calc(0px - var(--_environment-panel-height) * 0.16);
    margin-top: calc(0px - var(--_environment-panel-height) * 0.19);

    .font {
      fill: white;
    }
    
    circle {
      fill: #7f7f7f;
      transition: fill 300ms;
    }

    .MLEP__array-insert-background {
      fill-opacity: 1;
      fill: var(--_background);
      stroke: var(--_accent-color);
      stroke-width: 3px;
    }

    line {
      stroke: var(--_accent-color);
      stroke-opacity: 0;
      stroke-width: 40;
      pointer-events: none;
      transition: stroke-opacity 300ms;
      stroke-linecap: round;
    }

    g[data-command]:hover {
      circle {
        fill: var(--_accent-color);
      }
      line {
        stroke-opacity: 1;
      }
    }
  }

  .MLEP__environment-delimiter-controls {
    height: 100%;
    width: 50%;
    .MLEP__array-delimiter-options {
      width: var(--_environment-panel-height);
      height: var(--_environment-panel-height);
      display: flex;
      flex-wrap: wrap;
      flex-direction: row;
      justify-content: space-around;
      svg {
        pointer-events: all;
        margin-top: 2px;
        width: calc(var(--_environment-panel-height) / 3 * 28 / 24);
        height: calc(var(--_environment-panel-height) / 3 - 2px);
        border-radius: calc(var(--_environment-panel-height) / 25);
        &:hover {
          background-color: var(--_button-background-hover);
        }
        background-color: var(--_button-background);

        path, line {
          stroke: var(--_button-text);
          stroke-width: 2;
          stroke-linecap: round;
        }
        rect, path {
          fill-opacity: 0;
        }
        &.active {
          pointer-events: none;
          background-color: var(--_button-background-active);
          path, line {
            stroke: var(--_accent-color);
          }
          circle {
            fill: var(--_accent-color);
          }
        }
      }
    }
  }
  
}
