map $http_upgrade $connection_upgrade {
    default upgrade;
    `` close;
}

map $http_user_agent $is_social_bot {
    default 0;
    "~*facebookexternalhit" 1;
    "~*facebookcatalog"     1;
    "~*Twitterbot"          1;
    "~*GoogleImageProxy"    1;
    "~*GmailImageProxy"     1;
    "~*bingbot"             1;
    "~*_zbot"               1;
}

server {
    listen $NGINX_CONNECT_PORT_SSL ssl;
    listen [::]:$NGINX_CONNECT_PORT_SSL ssl;
    listen $NGINX_CONNECT_PORT;

    server_name viclass.vn staging.viclass.vn devlocal.viclass.vn *********** *********** 127.0.0.1 host.docker.internal;

    etag on;
    gzip on;

    gzip_types text/plain application/xml application/javascript;
    gzip_proxied no-cache no-store private expired auth;
    gzip_min_length 1000;

    ssl_certificate $SSL_PATH/fullchain.pem;
    ssl_certificate_key $SSL_PATH/privkey.pem;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2;
    ssl_ciphers HIGH:!aNULL:!MD5;

    root "$DEPLOYMENT_ROOT/ui/viclass/portal.homepage";
    index index.html;

    # Skip beta authentication (whether ENABLE_BETA is true or false)
    include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/portal.support.conf;
    include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/static.conf;
    location ^~ /modules { # Using ^~ to set this location prior to /
        include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/ww.conf;
        include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/themes.conf;
        include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/mfe.conf;
    }
    include $NGINX_CONF_PATH/viclass.vn.conf.d/social.preview.conf;

    # This location block matches requests for static files of portal.homepage
    # that are not in the /modules or /classrooms directories.
    location ~* ^(?!\/(modules|classrooms|static|lsessions|support)\/).*\.(js|css|png|jpg|jpeg|gif|ico|mp4|webm|webp|svg|woff|woff2|ttf|eot|otf|map)$ {}

    <% if (process.env.ENABLE_BETA == "true") { %>
        # If beta authentication is enabled, then include prior locations which skip auth
        include $NGINX_CONF_PATH/viclass.vn.conf.d/beta.allowed.locations.conf;

        location @coming_soon {
            internal;
            return 302 https://$http_host/beta/coming-soon;
        }
    <% } %>

    location / {
        <% if (process.env.ENABLE_BETA == "true") { %>
            auth_request /api/beta/verify-invitation-code;
            error_page 401 @coming_soon;
        <% } %>

        include $NGINX_CONF_PATH/viclass.vn.conf.d/backend/*.conf;
        include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/lsessions.conf;
        include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/portal.classrooms.conf;
        include $NGINX_CONF_PATH/viclass.vn.conf.d/frontend/portal.homepage.conf;
    }
}
