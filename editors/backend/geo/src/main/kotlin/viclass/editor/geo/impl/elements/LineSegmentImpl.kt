package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import org.apache.commons.geometry.euclidean.threed.line.Line3D
import org.apache.commons.geometry.euclidean.threed.line.Lines3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dimension
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.constructor.DEFAULT_PRECISION
import viclass.editor.geo.impl.constructor.Distances
import viclass.editor.geo.math.MFunc
import kotlin.reflect.KClass

open class LineSegmentImpl constructor(
    doc: GeoDoc, name: String?, private var _p1: Point, private var _p2: Point, _parallelVector: Vector3D? = null
) : LineSegment, LineImpl(doc, name, _p1, _parallelVector ?: createVectorByEl(doc, _p1, _p2), _p2) {
    override val clazz: KClass<out LineSegment> = LineSegment::class

    override val p1: Point
        get() = _p1
    override val p2: Point
        get() = _p2

    override fun line(): Line3D {
        return Lines3D.fromPoints(p1.coordinates(), p2.coordinates(), DEFAULT_PRECISION)
    }

    override fun length(): Double {
        return Distances.of(p1, p2)
    }

    override fun vertices(): List<Point> {
        return listOf(p1, p2)
    }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(LineSegment::class, name)) return false
        if (this.name == name) return true
        return false
    }

    override fun parametricFunc(dim: Dimension): MFunc {
        TODO("Not yet implemented")
    }

    override fun mergeFrom(other: Element) {
        if (other !is LineSegment) return
        super<LineImpl>.mergeFrom(other)

    }

    override fun orderedVector(): Vector3D {
        return createVectorByEl(doc, p1, p2)
    }
}
