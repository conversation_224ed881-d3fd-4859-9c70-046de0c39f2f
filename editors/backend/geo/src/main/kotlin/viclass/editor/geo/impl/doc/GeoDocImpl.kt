package viclass.editor.geo.impl.doc

import common.libs.logger.Logging
import io.ktor.util.reflect.*
import java.util.concurrent.locks.ReentrantReadWriteLock
import kotlin.math.max
import kotlin.reflect.KClass
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.persistentMapOf
import org.koin.core.annotation.Singleton
import viclass.editor.geo.constructor.Construction
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.ElConstructionResult
import viclass.editor.geo.dbentity.GeoDocument
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Element
import viclass.editor.geo.exceptions.ConstructionResultException
import viclass.editor.geo.exceptions.DeserializerException
import viclass.editor.geo.exceptions.ElementExistInDocumentException
import viclass.editor.geo.exceptions.ElementNotExistInDocumentException
import viclass.editor.geo.exceptions.InvalidDocumentException
import viclass.editor.geo.impl.constructor.ConstructionResultImpl
import viclass.editor.geo.impl.elements.deserializers
import viclass.editor.geo.impl.transformer.TransformMapping
import viclass.editor.geo.render.RenderDoc

@Singleton
class GeoDocImpl constructor(val cmder: GeoDocCommanderImpl, override val numDim: Int, override val id: String) : GeoDoc, Logging {
    private val cloneLock = ReentrantReadWriteLock()

    /** List of elements inside the document, including the inferred elements */
    override var elements: List<Element> = mutableListOf()
        get() {
            try {
                cloneLock.readLock().lock()
                return field
            } finally {
                cloneLock.readLock().unlock()
            }
        }

    override var constructions: List<Construction> = persistentListOf()
        get() {
            try {
                cloneLock.readLock().lock()
                return field
            } finally {
                cloneLock.readLock().unlock()
            }
        }
        set(value) {
            field = persistentListOf<Construction>() + value
        }

    override lateinit var renderDoc: RenderDoc

    override lateinit var docDefaultElRenderProp: DocDefaultElRenderProp

    /**
     * This list contains the full dependency graph among elements. Each element of this list contains the set of DIRECT
     * DEPENDENCY of the corresponding element.
     */
    private var deps: List<List<Int>> = persistentListOf()
    private var props: List<ElementProp> = persistentListOf()
    private var indexes: Map<Element, Int> = persistentMapOf()

    fun copyFrom(order: GeoDocImpl) {
        if (id != order.id) throw InvalidDocumentException("id not match")
        if (numDim != order.numDim) throw InvalidDocumentException("num Dim not match")
        try {
            cloneLock.writeLock().lock()
            elements = order.elements
            constructions = order.constructions
            deps = order.deps
            indexes = order.indexes
            renderDoc = order.renderDoc
            docDefaultElRenderProp = order.docDefaultElRenderProp
            props = order.props
        } finally {
            cloneLock.writeLock().unlock()
        }
    }

    override fun elementsFromConstruction(index: Int): List<Element> {
        return props.filter { it.cIndex == index }.map { it.el }
    }

    override fun hasElement(e: Element): Boolean {
        return elements.contains(e)
    }

    @Throws(ElementNotExistInDocumentException::class)
    override fun dependencies(e: Element): List<Element> {
        val idx = indexes[e] ?: throw ElementNotExistInDocumentException("Supplied element doesn't exist within the document")
        return deps[idx].map { elements[it] }
    }

    @Throws(ElementNotExistInDocumentException::class)
    override fun isInferred(e: Element): Boolean {
        val idx = indexes[e] ?: throw ElementNotExistInDocumentException("Supplied element doesn't exist within the document")
        return props[idx].isInferred
    }

    // TODO a better implementation is not iterating through the list of element
    // but have an index of element names and use that to retrieve element
    @Suppress("UNCHECKED_CAST")
    override fun <T : Element> findElementByName(
        name: String?, 
        elType: KClass<T>, 
        ctIdx: Int?, 
        includeSameCtIdx: Boolean?,
        nameExtractor: ((T) -> String)?
    ): T? {
        name ?: return null
        
        // 1. Always try original logic first (using element.isNameMatch)
        for (el in elements) {
            if (el.clazz == elType && el.isNameMatch(name) && el.valid && el.usable && el.deleted != true) {
               val elCtIdx = getConstructionIndex(el) ?: continue
                if (ctIdx == null || ctIdx < 0 || elCtIdx < ctIdx || (elCtIdx == ctIdx && includeSameCtIdx == true))
                    return el as T
            }
        }
        
        // 2. If not found and nameExtractor is provided, try custom name extraction logic
        if (nameExtractor != null && ctIdx != null) {
            val matches = elements
                .filterIsInstance(elType.java)
                .map { it as T }
                .filter { el ->
                    nameExtractor(el) == name &&
                            el.valid &&
                            el.usable &&
                            el.deleted != true
                }
                .filter { el ->
                    // Apply construction index filtering
                    val elCtIdx = getConstructionIndex(el)
                    elCtIdx != null && (elCtIdx < ctIdx || (elCtIdx == ctIdx && includeSameCtIdx == true))
                }

            // Return element only if exactly one match found
            return when (matches.size) {
                0 -> null
                1 -> matches.first()
                else -> throw ElementExistInDocumentException(
                    "Found ${matches.size} elements of type ${elType.simpleName} with name '$name'. " +
                    "Expected exactly one match. Multiple elements exist with the same identifier."
                )
            }
        }

        return null
    }

    @Throws(RuntimeException::class)
    override fun mergeConstructionResult(cr: ConstructionResult<out Element>, construction: Construction) {
        cr.result() ?: throw ConstructionResultException("Cannot merge an empty construction result.")

        if (construction.elName.isNullOrBlank()) {
            construction.entity.name = cr.result()!!.name
        }

        constructions += construction
        cr.ctIdx = constructions.size - 1
        construction.entity.ctIdx = cr.ctIdx

        addConstructionElements(cr, construction)
    }

    override fun reMergeConstructionResult(
        cr: ConstructionResult<out Element>, construction: Construction
    ): Set<Element> {
        return mergeConstructionElements(cr, construction)
    }

    private fun mergeConstructionElements(
        result: ConstructionResult<out Element>, construction: Construction
    ): Set<Element> {
        if (construction.entity.result == null) return emptySet()

        val oldResult = ConstructionResultImpl<Element>().initData(
                construction.ctIdx,
                elements[construction.entity.result!!.result],
                construction.entity.result!!.resultDeps,
                construction.entity.result!!.nodes.map { elements[it] },
                construction.entity.result!!.edges.map { it.toList() })

        val mapEls = HashMap<Element, Element>()

        val oldElements = oldResult.elements()
        val newElements = result.elements()
        val newDependencies = result.deps() // the full dependencies graph
        val updatedElements = mutableSetOf<Element>()

        if (result.result() == null) {
            oldResult.result()?.let { rsEl ->
                rsEl.valid = false
                val ctIdx = getConstructionIndex(rsEl)!!
                val els = elementsFromConstruction(ctIdx).map {
                    it.valid = false
                    it
                }
                return setOf(rsEl, *els.toTypedArray())
            }
            return emptySet()
        }

        for (i: Int in 0..max(oldElements.size - 1, newElements.size - 1)) {

            if (newElements.size <= i && i < oldElements.size) {
                oldElements[i]?.let {
                    it.valid = false
                    updatedElements.add(it)
                }
                continue
            }

            if (oldElements.size <= i && i < newElements.size) {
                newElements[i]?.let { anInferred ->
                    // if the element is not added to the document before
                    if (getConstructionIndex(anInferred) != construction.ctIdx && !indexes.containsKey(anInferred)) {
                        // get all the direct dependencies of element i
                        val deps = newDependencies[i].mapNotNull { j: Int -> newElements[j] }

                        // here note that, because the deps are elements inside the inferredElements
                        // array that has index < index
                        // of the anInferred element, so they must have been added earlier
                        addElWithDeps(anInferred, deps, construction.ctIdx, i != newElements.size - 1)
                        updatedElements.add(anInferred)
                        mapEls[anInferred] = anInferred
                    }
                }
                continue
            }

            val oldEl = oldElements.getOrNull(i)
            val newEl = newElements.getOrNull(i)

            if (oldEl != null && newEl != null) {
                oldEl.mergeFrom(newEl)
                mapEls[newEl] = oldEl
                updatedElements.add(oldEl)
            } else if (oldEl != null) {
                oldEl.valid = false
                mapEls[oldEl] = oldEl
                updatedElements.add(oldEl)
            } else if (newEl != null) {
                // if the element is not added to the document before
                if (getConstructionIndex(newEl) != construction.ctIdx && !indexes.containsKey(newEl)) {
                    // get all the direct dependencies of element i
                    val deps = newDependencies[i].mapNotNull { j: Int -> newElements[j] }

                    // here note that, because the deps are elements inside the inferredElements
                    // array that has index < index
                    // of the anInferred element, so they must have been added earlier
                    addElWithDeps(newEl, deps, construction.ctIdx, i != newElements.size - 1)
                    mapEls[newEl] = newEl
                    updatedElements.add(newEl)
                }
            }
        }

        construction.entity.result!!.resultDeps = result.resultDeps()
        construction.entity.result!!.edges = result.edges().map { it.toIntArray() }.toTypedArray()
        construction.entity.result!!.nodes = result.nodes().mapNotNull { mapEls[it]?.let { it1 -> getIndex(it1) } }

        return updatedElements
    }

    /**
     * Add the elements inside a construction result into a document. For each element, including the result element
     * itself, not added before, the element will be added and its dependency relationship will be established within
     * the document.
     *
     * All added elements, except for the main result element, are marked as inferred
     *
     * @param result construction result whose elements to be added
     * @param construction the construction corresponding to the elements added
     */
    private fun addConstructionElements(result: ConstructionResult<out Element>, construction: Construction) {
        val resultEls = result.elements()
        val dependencies = result.deps() // the full dependencies graph

        /** add the inferred elements of a construction result into the document first. */
        for (i in resultEls.indices) {
            resultEls[i]?.let { anInferred ->
                if (!indexes.containsKey(anInferred)) { // if the element is not added to the document before
                    val deps = dependencies[i].mapNotNull { j: Int ->
                        resultEls[j]
                    } // get all the direct dependencies of element i

                    // here note that, because the deps are elements inside the inferredElements
                    // array that has index < index
                    // of the anInferred element, so they must have been added earlier
                    addElWithDeps(anInferred, deps, construction.ctIdx, i != resultEls.size - 1)
                }
            }
        }

        construction.entity.result =
            ElConstructionResult(
                getIndex(result.result()!!)!!, result.resultDeps(), result.nodes().mapNotNull { getIndex(it) }, result.edges().map { it.toIntArray() }.toTypedArray()
            )
    }

    /**
     * This method add an element (which retrieved from a construction result) and initialize its dependencies
     * relationship within the document. Note that, for an element to be added, all of its dependencies must be added
     * beforehand. The dependencies must be the direct dependency only.
     *
     * @param element the inferred element of the construction
     * @param elDeps the inferred element's dependencies graph
     * @param constructionIndex index of the construction that result in this inferred element
     * @param isInferred whether this element is an inferred element (e.g. not created by user's intent)
     * @return the index at which the element is added
     */
    private fun addElWithDeps(
        element: Element, elDeps: List<Element>, constructionIndex: Int, isInferred: Boolean
    ): Int {

        // add to the list of element
        elements += element

        val elIndex = elements.size - 1
        indexes = indexes + (element to elIndex)

        // ds contains the direct dependencies for the inferred element
        val ds: MutableList<Int> = mutableListOf()
        deps = deps + listOf(ds)

        val prop = ElementProp(element, elIndex, constructionIndex, isInferred)
        props = props + prop

        for (e1 in elDeps) {
            val i = indexes[e1] ?: throw ConstructionResultException(
                "A dependency of an inferred element has not been added before."
            )
            ds.add(i)
        }
        return elIndex
    }

    @Throws(ElementNotExistInDocumentException::class)
    override fun getConstructionIndex(el: Element): Int? {
        val idx = this.indexes[el] ?: return null

        val prop = props[idx]

        return prop.cIndex
    }

    override fun getIndex(el: Element): Int? = indexes[el]

    /**
     * Utility function that convert element data from GeoDocument to the GeoDoc. In this process, the elements will be
     * deserialized from GeometryData, their dependency graph, property will be restored
     */
    @Throws(RuntimeException::class)
    fun copyElementsFromEntity(bsonDoc: GeoDocument) {
        bsonDoc.elData.forEach {
            val dName = it.geometry!!::class.simpleName!!
            val deserializer = deserializers[dName] ?: throw DeserializerException("Cannot find deserializer for $dName")

            val el = deserializer(this, it.geometry!!)
            it.transformer?.let { t -> el.transformer = TransformMapping.fromName(t) }
            it.transformData?.let { d -> el.transformData = d }
            it.movementPath = el.movementPath
            el.usable = it.usable
            el.deleted = it.deleted
            el.valid = it.valid
            el.name = it.name
            elements += el
            val prop = ElementProp(el, it.elIdx, it.cIdx, it.inferred)
            props = props + prop
            indexes = indexes + (el to it.elIdx)
            deps = deps + listOf(it.deps)
        }
    }

    override fun updateElsValid(idxes: Set<Int>, valid: Boolean) {
        idxes.map { this.elements[it] }.forEach { it.valid = valid }
    }

    override fun updateElsDeleted(idxes: Set<Int>, deleted: Boolean?) {
        idxes.map { this.elements[it] }.forEach { it.deleted = deleted }
    }

    override fun updateElsUsable(idxes: Set<Int>, usable: Boolean) {
        idxes.map { this.elements[it] }.forEach { it.usable = usable }
    }

    override fun updateConstructionsUsable(idxes: Set<Int>, usable: Boolean) {
        idxes.map { this.constructions[it] }.forEach { it.entity.usable = usable }
    }

    override fun clearUnusableElements() {
        val invalidEls = this.elements.filter { !it.usable }
        val invalidConstructionIdx: List<Int> = invalidEls.mapNotNull { this.getConstructionIndex(it) }
        val invalidElsProp: List<ElementProp> = invalidConstructionIdx.map { idx -> this.props.filter { it.cIndex == idx } }.flatten()
        this.props -= invalidElsProp
        this.indexes -= invalidEls
        this.elements -= invalidEls
    }

    override fun clearUnusableConstruction() {
        val invalidConstruction = this.constructions.filter { !it.usable }
        this.constructions -= invalidConstruction
    }
}
