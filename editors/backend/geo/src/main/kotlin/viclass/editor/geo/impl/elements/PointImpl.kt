package viclass.editor.geo.impl.elements

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.movement.path.MovementPath
import viclass.editor.geo.dbentity.transformdata.TransformData
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Dim
import viclass.editor.geo.elements.Element
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.InvalidElementException
import viclass.editor.geo.transformer.Transformer
import kotlin.reflect.KClass

class PointImpl constructor(
    override var doc: GeoDoc, override var name: String?,
    private var _x: Double, private var _y: Double, private var _z: Double,
) : Point {
    override val clazz: KClass<Point> = Point::class
    override var usable: Boolean = true
    override var deleted: Boolean? = null
    override var valid: Boolean = true

    override val x: Double
        get() = _x
    override val y: Double
        get() = _y
    override val z: Double
        get() = _z

    /**
     * Initialize the point in 2D
     * @param x
     * @param y
     */
    constructor(doc: GeoDoc, x: Double, y: Double) : this(doc, null, x, y, 0.0)
    constructor(doc: GeoDoc, name: String?, x: Double, y: Double) : this(doc, name, x, y, 0.0)

    /**
     * Initialize the point in 3D
     * @param x
     * @param y
     * @param z
     */
    constructor(doc: GeoDoc, x: Double, y: Double, z: Double) : this(doc, null, x, y, z) {
        if (doc.numDim == 3) this._z = z else this._z = 0.0 // force z = 0 if only 2 dimension
    }

    constructor(doc: GeoDoc, name: String?, coord: Vector3D) : this(doc, name, coord.x, coord.y, coord.z)

    override fun coord(dim: Dim): Double {
        return when {
            dim === Dim.X -> x
            dim === Dim.Y -> y
            dim === Dim.Z -> z
            else -> throw InvalidElementException("Dimension not supported")
        }
    }

    override fun vertices(): List<Point> {
        return listOf(this)
    }

    override var transformData: TransformData? = null
        get() = field
        set(value) { field = value}

    override var movementPath: MovementPath? = null
        get() = field
        set(value) { field = value }

    override var transformer: Transformer<TransformData>? = null
        get() = field
        set(value) { field = value }

    override fun isNameMatch(name: String): Boolean {
        if (!NamePattern.isNameValid(Point::class, name)) return false

        return name == this.name
    }

    override fun coordinates(): Vector3D {
        return Vector3D.of(x, y, z)
    }

    override fun mergeFrom(other: Element) {
        if (other !is Point) return
        super.mergeFrom(other)

        _x = other.x
        _y = other.y
        _z = other.z
    }
}
