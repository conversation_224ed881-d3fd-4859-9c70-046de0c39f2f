package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.abs

/**
 *
 * <AUTHOR>
 */
object Ellipses {
    fun calculatePointOnEllipseWithRadian(doc: GeoDoc, name: String?, ellipse: Ellipse, alpha: Double): Point {
        val pC = PointImpl(doc, null, ellipse.center)
        val vec = ellipse.center.vectorTo(ellipse.f2.coordinates().rotate(alpha, ellipse.center))
        val line = LineImpl(doc, null, pC, vec)
        val intersections = Intersections.of(ellipse, line)!!
        val ordered = Orders.pointsOnParallelVector(vec, intersections[0], intersections[1])
        return PointImpl(doc, name, ordered[1])
    }

    fun isOnEllipse(ellipse: Ellipse, v: Vector3D): Boolean {
        val center = ellipse.center
        val a = ellipse.a
        val b = ellipse.b
        val angle = ellipse.rotate

        // Transform point to ellipse coordinate system
        val dx = v.x - center.x
        val dy = v.y - center.y

        // Rotate to align with ellipse axes
        val cosAngle = kotlin.math.cos(-angle)
        val sinAngle = kotlin.math.sin(-angle)
        val x = dx * cosAngle - dy * sinAngle
        val y = dx * sinAngle + dy * cosAngle

        // Check if point satisfies ellipse equation: (x/a)² + (y/b)² = 1
        val result = (x * x) / (a * a) + (y * y) / (b * b)
        return kotlin.math.abs(result - 1.0) < 1e-10
    }

    fun tangentAt(ellipse: Ellipse, at: Point): LineVi? {
        if (!isOnEllipse(ellipse, at.coordinates())) return null

        val center = ellipse.center
        val a = ellipse.a
        val b = ellipse.b
        val angle = ellipse.rotate

        // Transform point to ellipse coordinate system
        val dx = at.coordinates().x - center.x
        val dy = at.coordinates().y - center.y

        // Rotate to align with ellipse axes
        val cosAngle = kotlin.math.cos(-angle)
        val sinAngle = kotlin.math.sin(-angle)
        val x = dx * cosAngle - dy * sinAngle
        val y = dx * sinAngle + dy * cosAngle

        // Calculate tangent slope in ellipse coordinate system
        val tangentSlope = -(b * b * x) / (a * a * y)

        // Transform tangent direction back to world coordinates
        val tangentDx = cosAngle - tangentSlope * sinAngle
        val tangentDy = sinAngle + tangentSlope * cosAngle

        val parallelVector = Vector3D.of(tangentDx, tangentDy, 0.0).normalize()
        return LineImpl(ellipse.doc, null, at, parallelVector)
    }

    fun tangentThroughPoint(ellipse: Ellipse, through: Point): List<LineVi>? {
        // For now, return null - this is a complex calculation that would require
        // solving a quartic equation. Can be implemented later if needed.
        return null
    }
}
