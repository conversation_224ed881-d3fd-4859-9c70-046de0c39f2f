package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.constructor.ConstructionResult
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.abs


/**
 *
 * <AUTHOR>
 */
object Points {
    fun calculateCenterPoint(doc: GeoDoc, name: String?, point1: Point, point2: Point): Point {
        val x = (point1.x + point2.x) / 2
        val y = (point1.y + point2.y) / 2
        val z = (point1.z + point2.z) / 2

        return PointImpl(doc, name, x, y, z)
    }

    fun calculateCenterPoint(point1: Point, point2: Point): Vector3D {
        val x = (point1.x + point2.x) / 2
        val y = (point1.y + point2.y) / 2
        val z = (point1.z + point2.z) / 2

        return Vector3D.of(x, y, z)
    }

    fun isBetweenTwoPoints(ref: Vector3D, v1: Vector3D, v2: Vector3D): Boolean {
        val distanceRefV1 = Distances.of(ref, v1)
        val distanceRefV2 = Distances.of(ref, v2)
        val distanceV1V2 = Distances.of(v1, v2)

        return abs(distanceRefV1 + distanceRefV2 - distanceV1V2) < DEFAULT_TOLERANCE
    }

    fun calculateCenterPoint(vertices: List<Point>): Vector3D {
        var x = 0.0
        var y = 0.0
        var z = 0.0
        for (p in vertices) {
            x += p.x
            y += p.y
            z += p.z
        }
        x /= vertices.size.toDouble()
        y /= vertices.size.toDouble()
        z /= vertices.size.toDouble()
        return Vector3D.of(x, y, z)
    }
}
