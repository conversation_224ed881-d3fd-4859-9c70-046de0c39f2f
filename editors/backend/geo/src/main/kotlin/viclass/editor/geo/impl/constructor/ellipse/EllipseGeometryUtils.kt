package viclass.editor.geo.impl.utils

import kotlin.math.abs
import kotlin.math.sqrt
import viclass.editor.geo.elements.Point
import viclass.editor.geo.exceptions.ConstructionException
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.entity.ParamKind
import viclass.editor.geo.impl.elements.PointImpl
import viclass.editor.geo.impl.transformer.PointOnLineWithLengthTransformer
import viclass.editor.geo.impl.transformer.TransformMapping
import viclass.editor.geo.dbentity.transformdata.PointOnLineWithLengthTransformData
import viclass.editor.geo.dbentity.movement.path.MovementLinePath
import viclass.editor.geo.impl.constructor.generatePointName

/**
 * Utility class containing shared ellipse geometry calculations
 * Used by both EllipseEC and EllipseImpl to avoid code duplication
 */
object EllipseGeometryUtils {
    
    /**
     * Calculate major and minor axis lengths from center and two vector points
     * @param center Center point of the ellipse
     * @param vectorA First vector point
     * @param vectorB Second vector point
     * @return Pair of (major axis length, minor axis length)
     */
    fun calculateAxisLengths(center: Point, vectorA: Point, vectorB: Point): Pair<Double, Double> {
        val aVecX = vectorA.x - center.x
        val aVecY = vectorA.y - center.y
        val bVecX = vectorB.x - center.x
        val bVecY = vectorB.y - center.y
        
        val aLength = sqrt(aVecX * aVecX + aVecY * aVecY)
        val bLength = sqrt(bVecX * bVecX + bVecY * bVecY)
        
        return Pair(maxOf(aLength, bLength), minOf(aLength, bLength))
    }
    
    /**
     * Calculate focal distance from major and minor axis lengths
     * @param a Major axis length
     * @param b Minor axis length
     * @return Focal distance c where c² = a² - b²
     */
    fun calculateFocalDistance(a: Double, b: Double): Double {
        return sqrt(a * a - b * b)
    }
    
    /**
     * Calculate focus points from center, major axis vector, and focal distance
     * @param center Center point
     * @param majorAxisVector Major axis vector as DoubleArray [x, y]
     * @param focalDistance Focal distance c
     * @return Pair of focus points as DoubleArray pairs: (f1[x,y], f2[x,y])
     */
    fun calculateFocusPoints(
        center: Point,
        majorAxisVector: DoubleArray,
        focalDistance: Double
    ): Pair<DoubleArray, DoubleArray> {
        val majorLength = sqrt(majorAxisVector[0] * majorAxisVector[0] + majorAxisVector[1] * majorAxisVector[1])
        val ux = majorAxisVector[0] / majorLength
        val uy = majorAxisVector[1] / majorLength
        
        val f1 = doubleArrayOf(
            center.x + ux * focalDistance,
            center.y + uy * focalDistance
        )
        val f2 = doubleArrayOf(
            center.x - ux * focalDistance,
            center.y - uy * focalDistance
        )
        
        return Pair(f1, f2)
    }
    
    /**
     * Calculate distance between two points
     * @param p1 First point coordinates [x, y]
     * @param p2 Second point coordinates [x, y]
     * @return Distance between the points
     */
    fun calculateDistance(p1: DoubleArray, p2: DoubleArray): Double {
        return sqrt((p1[0] - p2[0]) * (p1[0] - p2[0]) + (p1[1] - p2[1]) * (p1[1] - p2[1]))
    }
    
    /**
     * Calculate distance from a point to focus points
     * @param point Point coordinates [x, y]
     * @param f1 First focus point coordinates [x, y]
     * @param f2 Second focus point coordinates [x, y]
     * @return Sum of distances from point to both foci
     */
    fun calculateFocalDistanceSum(point: DoubleArray, f1: DoubleArray, f2: DoubleArray): Double {
        return calculateDistance(point, f1) + calculateDistance(point, f2)
    }
    
    /**
     * Validate ellipse geometry parameters
     * @param a Major axis length
     * @param b Minor axis length
     * @throws ConstructionException if parameters are invalid
     */
    fun validateEllipseParameters(a: Double, b: Double) {
        if (a <= b || b <= 0) {
            throw ConstructionException("Invalid ellipse parameters: major axis (a=$a) must be greater than minor axis (b=$b) and both must be positive")
        }
    }
    
    /**
     * Validate that a point lies on the ellipse using focal distance property
     * @param point Point coordinates [x, y]
     * @param f1 First focus point coordinates [x, y]
     * @param f2 Second focus point coordinates [x, y]
     * @param expectedSum Expected sum of focal distances (2a)
     * @param tolerance Numerical tolerance for comparison
     * @param pointName Name of the point for error message
     * @throws ConstructionException if point doesn't lie on ellipse
     */
    fun validatePointOnEllipse(
        point: DoubleArray,
        f1: DoubleArray,
        f2: DoubleArray,
        expectedSum: Double,
        tolerance: Double = 1e-10,
        pointName: String = "Point"
    ) {
        val actualSum = calculateFocalDistanceSum(point, f1, f2)
        if (abs(actualSum - expectedSum) > tolerance) {
            throw ConstructionException("$pointName does not lie on the ellipse: focal distance sum $actualSum != expected $expectedSum")
        }
    }
    
    /**
     * Calculate perpendicular unit vector from a given vector
     * @param vector Input vector [x, y]
     * @return Perpendicular unit vector [x, y]
     */
    fun calculatePerpendicularUnitVector(vector: DoubleArray): DoubleArray {
        val length = sqrt(vector[0] * vector[0] + vector[1] * vector[1])
        return doubleArrayOf(-vector[1] / length, vector[0] / length)
    }
    
    /**
     * Calculate two possible positions for a point based on perpendicular direction
     * @param center Center point
     * @param distance Distance from center
     * @param perpendicularUnitVector Perpendicular unit vector
     * @return Pair of possible positions as DoubleArray pairs
     */
    fun calculatePerpendicularPositions(
        center: Point,
        distance: Double,
        perpendicularUnitVector: DoubleArray
    ): Pair<DoubleArray, DoubleArray> {
        val option1 = doubleArrayOf(
            center.x + distance * perpendicularUnitVector[0],
            center.y + distance * perpendicularUnitVector[1]
        )
        val option2 = doubleArrayOf(
            center.x - distance * perpendicularUnitVector[0],
            center.y - distance * perpendicularUnitVector[1]
        )
        return Pair(option1, option2)
    }
    
    /**
     * Create a PointImpl with transformation capabilities for ellipse construction
     * @param doc GeoDoc instance
     * @param name Point name (nullable)
     * @param coords Point coordinates [x, y]
     * @param center Center point for transformation
     * @param minorAxisUnitVector Unit vector for minor axis direction
     * @param lengthParamIdx Parameter index for length
     * @param nthParamIdx Parameter index for nth result
     * @return PointImpl with transformation setup
     */
    fun createTransformablePoint(
        doc: GeoDoc,
        name: String?,
        coords: DoubleArray,
        center: Point,
        minorAxisUnitVector: DoubleArray,
        lengthParamIdx: Int,
        nthParamIdx: Int
    ): PointImpl {
        val centerArray = doubleArrayOf(center.x, center.y, 0.0)
        val minorAxisUnitVector3D = doubleArrayOf(minorAxisUnitVector[0], minorAxisUnitVector[1], 0.0)
        
        return PointImpl(doc, name, coords[0], coords[1]).apply {
            transformer = TransformMapping.fromClazz(PointOnLineWithLengthTransformer::class)
            transformData = PointOnLineWithLengthTransformData(
                lengthParamIdx = lengthParamIdx,
                lengthParamKind = ParamKind.PK_Value,
                nthParamIdx = nthParamIdx,
                root = centerArray,
                unitVector = minorAxisUnitVector3D
            )
            movementPath = MovementLinePath(centerArray, minorAxisUnitVector3D)
        }
    }
    
    /**
     * Calculate ellipse parameters from center, pA point and semi-minor axis length
     * @param center Center point
     * @param pA Point A on major axis
     * @param semiMinorAxisLength Length of semi-minor axis
     * @return Triple of (major axis length, minor axis length, major axis vector)
     */
    fun calculateEllipseParametersFromCenterVectors(
        center: Point,
        pA: Point,
        semiMinorAxisLength: Double
    ): Triple<Double, Double, DoubleArray> {
        val majorAxisVector = doubleArrayOf(pA.x - center.x, pA.y - center.y)
        val majorAxisLength = sqrt(majorAxisVector[0] * majorAxisVector[0] + majorAxisVector[1] * majorAxisVector[1])
        
        val a = maxOf(majorAxisLength, semiMinorAxisLength)
        val b = minOf(majorAxisLength, semiMinorAxisLength)
        
        return Triple(a, b, majorAxisVector)
    }
    
    /**
     * Validate ellipse construction with focus points validation
     * @param center Center point
     * @param pA Point A coordinates
     * @param pBCoords Point B coordinates
     * @param majorAxisVector Major axis vector
     * @param a Major axis length
     * @param b Minor axis length
     * @param tolerance Numerical tolerance
     */
    fun validateEllipseConstruction(
        center: Point,
        pA: Point,
        pBCoords: DoubleArray,
        majorAxisVector: DoubleArray,
        a: Double,
        b: Double,
        tolerance: Double = 1e-10
    ) {
        val c = calculateFocalDistance(a, b)
        val (f1, f2) = calculateFocusPoints(center, majorAxisVector, c)
        
        val pACoords = doubleArrayOf(pA.x, pA.y)
        val expectedSum = 2 * a
        
        validatePointOnEllipse(pACoords, f1, f2, expectedSum, tolerance, "Point A")
        validatePointOnEllipse(pBCoords, f1, f2, expectedSum, tolerance, "Point B")
    }
}