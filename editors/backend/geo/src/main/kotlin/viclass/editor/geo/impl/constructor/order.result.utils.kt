package viclass.editor.geo.impl.constructor

import org.apache.commons.geometry.euclidean.threed.Vector3D
import viclass.editor.geo.doc.GeoDoc
import viclass.editor.geo.elements.Circle
import viclass.editor.geo.elements.Ellipse
import viclass.editor.geo.elements.LineVi
import viclass.editor.geo.elements.Point
import viclass.editor.geo.impl.elements.LineSegmentImpl
import viclass.editor.geo.impl.elements.PointImpl
import kotlin.math.PI


/**
 *
 * <AUTHOR>
 */

object Orders {

    /**
     * Determines the direction of a point relative to a root point along a given parallel vector.
     *
     * @param parallel The parallel vector defining the direction.
     * @param root The root point.
     * @param p The point to determine the direction for.
     * @return -1 if the point is in the direction of the vector, 1 otherwise.
     */
    fun directionOfPointOnParallelVector(parallel: Vector3D, root: Vector3D, p: Vector3D): Int {
        val vecUnit = parallel.normalize()
        val k1 = root.dot(vecUnit)
        val k2 = p.dot(vecUnit)
        return if (k1 < k2) -1 else 1
    }

    /**
     * Sorts points on a line based on a parallel vector.
     *
     * This function assumes all points lie on the line defined by the parallel vector.  It does
     * not perform any checks to ensure this condition is met.
     *
     * @param parallel The vector parallel to the line.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted along the line.
     */
    fun pointsOnParallelVector(parallel: Vector3D, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points

        val vecUnit = parallel.normalize()
        return points.sortedBy { it.dot(vecUnit) }
    }

    /**
     * Sorts points on a line based on a parallel vector.
     *
     * This function assumes all points lie on the line defined by the parallel vector. It does
     * not perform any checks to ensure this condition is met.
     *
     * @param parallel The vector parallel to the line.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted along the line.
     */
    fun pointsOnParallelVector(parallel: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsOnParallelVector(parallel, points.toList())
    }

    /**
     * Sorts points on a circle based on their angle relative to a reference point.
     *
     * This function assumes all points lie on the circle.  It does not check this condition.
     *
     * @param refCircle The circle on which the points lie.
     * @param refPoint A point on the circle used as a reference for angle calculation.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the circle.
     */
    fun pointsOnCircle(refCircle: Circle, refPoint: Vector3D, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points

        val doc = refCircle.doc

        val center = refCircle.centerPoint.coordinates()
        val p = PointImpl(doc, null, refPoint)
        val line = LineSegmentImpl(doc, null, refCircle.centerPoint, p)
        val ref = Intersections.of(line, refCircle)!!.first { Points.isBetweenTwoPoints(it, center, refPoint) }

        val angleRef = Circles.angleOfPoint(center, refCircle.radius, ref)

        return points.sortedBy {
            var angle = Circles.angleOfPoint(center, refCircle.radius, it)
            if (angle >= angleRef) angle = angle - angleRef
            else angle = 2 * PI - angleRef + angle
            angle
        }
    }

    /**
     * Sorts points on a circle based on their angle relative to a reference point.
     *
     * This function assumes all points lie on the circle. It does not check this condition.
     *
     * @param refCircle The circle on which the points lie.
     * @param refPoint A point on the circle used as a reference for angle calculation.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the circle.
     */
    fun pointsOnCircle(refCircle: Circle, refPoint: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsOnCircle(refCircle, refPoint, points.toList())
    }

    /**
     * Sorts points on an ellipse based on their angle relative to the vector from the center to the second focus (f2).
     *
     * This method assumes that all points lie on the Ellipse, but does not explicitly check.
     *
     * @param refEllipse The ellipse on which the points lie.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the ellipse.
     */
    fun pointsOnEllipse(refEllipse: Ellipse, points: List<Vector3D>): List<Vector3D> {
        if (points.size < 2) return points
        val center = refEllipse.center
        val f2 = refEllipse.f2.coordinates()
        val vecCF2 = center.vectorTo(f2)
        return points.sortedBy {
            val vec = center.vectorTo(it)
            vecCF2.angleTo(vec)
        }
    }

    /**
     * Sorts points on an ellipse based on their angle relative to the vector from the center to the second focus (f2).
     *
     * This method assumes that all points lie on the Ellipse, but does not explicitly check.
     *
     * @param refEllipse The ellipse on which the points lie.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle on the ellipse.
     */
    fun pointsOnEllipse(refEllipse: Ellipse, vararg points: Vector3D): List<Vector3D> {
        return pointsOnEllipse(refEllipse, points.toList())
    }

    /**
     * Sorts points based on their angle relative to a reference vector and a "role" vector.
     *
     * @param vec The reference vector.
     * @param role The "role" vector, potentially defining an origin or orientation.
     * @param points Vararg of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle.
     */
    fun pointsByRotation(vec: Vector3D, role: Vector3D, vararg points: Vector3D): List<Vector3D> {
        return pointsByRotation(vec, role, points.toList())
    }

    /**
     * Sorts points based on their angle relative to a reference vector and a "role" vector.
     *
     * @param vec The reference vector.
     * @param role The "role" vector, potentially defining an origin or orientation.
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their angle.
     */
    fun pointsByRotation(vec: Vector3D, role: Vector3D, points: List<Vector3D>): List<Vector3D> {
        return points.sortedBy { vec.angleTo(role.vectorTo(it)) }
    }

    /**
     * Orders two points based on their angle relative to a parallel vector and a "role" vector.
     *
     * This function appears designed to order points that could be mirror images across a line, using
     * angles to disambiguate their order.  If the points are identical, they are returned in a list.
     * If the angles formed by the points with the parallel and role vectors place them on opposite
     * sides of PI radians, they are returned in a specific order. Otherwise, an empty list is returned,
     * implying the ordering cannot be determined by this method.
     *
     * @param parallel The parallel vector defining the main direction.
     * @param role The "role" vector providing a reference point or orientation.
     * @param v1 The first point.
     * @param v2 The second point.
     * @return A list containing v1 and v2 in a determined order, or an empty list if the order is ambiguous.
     */
    fun pointByParallelVector(parallel: Vector3D, role: Vector3D, v1: Vector3D, v2: Vector3D): List<Vector3D> {
        if (v1 == v2) return listOf(v1, v2)
        val angle1 = parallel.angleTo(role.vectorTo(v1))
        val angle2 = parallel.angleTo(role.vectorTo(v2))
        if (angle1 < PI && angle2 > PI) return listOf(v1, v2)
        if (angle2 < PI && angle1 > PI) return listOf(v2, v1)
        return emptyList()
    }

    /**
     * Orders a list of `Point` elements based on their creation order within a `GeoDoc`.
     *
     * It utilizes the `getIndex` method of the `GeoDoc` to determine the creation order.
     *
     * @param doc The `GeoDoc` containing the points.
     * @param points Vararg of `Point` elements to be sorted.
     * @return A list of `Point` elements sorted by their creation order.
     */
    fun pointByCreateTime(doc: GeoDoc, vararg points: Point): List<Point> {
        return points.sortedBy { doc.getIndex(it) }
    }

    /**
     * Orders two points based on their counter-clockwise angle relative to a reference line.
     *
     * This function sorts two points, v1 and v2, based on the counter-clockwise angle formed by
     * rotating from the reference line's direction vector to the vector pointing from an
     * arbitrary point on the line to each of the input points.
     *
     * If the points are identical, they are returned in a list. If the points are collinear
     * with the reference line or the ordering cannot be determined unambiguously, an empty
     * list is returned.
     *
     * @param refLine The reference line.
     * @param v1 The first point.
     * @param v2 The second point.
     * @return A list containing v1 and v2 in counter-clockwise order, or an empty list if ambiguous.
     */
    fun pointsBaseLineReference(refLine: LineVi, v1: Vector3D, v2: Vector3D): List<Vector3D> {
        if (v1 == v2) return listOf(v1, v2)

        // Calculate the angle from the line's direction vector to the vector from
        // an arbitrary point on the line (here, p1) to each of the input points.
        val p1OnLine = refLine.p1.coordinates()
        val refVector = refLine.parallelVector

        val angle1 = refVector.angleTo(p1OnLine.vectorTo(v1))
        val angle2 = refVector.angleTo(p1OnLine.vectorTo(v2))

        // Order points by increasing counter-clockwise angle.
        return if (angle1 < angle2) {
            listOf(v1, v2)
        } else if (angle1 > angle2) {
            listOf(v2, v1)
        } else {
            // Points are collinear with the reference line, or ordering is ambiguous.
            emptyList()
        }
    }

    /**
     * Orders points in the document coordinate system from left to right, then top to bottom.
     *
     * This overloaded function sorts a list of Vector3D points based on their x and y coordinates.
     * Points are primarily ordered by their x-coordinate (leftmost first), and points with the same
     * x-coordinate are further ordered by their y-coordinate (topmost first).
     *
     * @param points A list of Vector3D representing the points to sort.
     * @return A list of Vector3D representing the points sorted by their document coordinates.
     */
    @JvmName("pointsInDocumentVector3D")
    fun pointsInDocument(points: List<Vector3D>): List<Vector3D> {
        return points.sortedWith { o1, o2 ->
            if (o1.x < o2.x) 1
            else if (o1.x > o2.x) -1
            else if (o1.y < o2.y) 1
            else if (o1.y > o2.y) -1
            else 0
        }
    }

    /**
     * Orders points in the document coordinate system from left to right, then top to bottom.
     *
     * This overloaded function sorts a list of Point objects based on their x and y coordinates.
     * Points are primarily ordered by their x-coordinate (leftmost first), and points with the same
     * x-coordinate are further ordered by their y-coordinate (topmost first).
     *
     * @param points A list of Point objects to sort.
     * @return A list of Point objects sorted by their document coordinates.
     */
    @JvmName("pointsInDocumentPoint")
    fun pointsInDocument(points: List<Point>): List<Point> {
        return points.sortedWith { o1, o2 ->
            val p1 = o1.coordinates()
            val p2 = o2.coordinates()
            if (p1.x < p2.x) 1
            else if (p1.x > p2.x) -1
            else if (p1.y < p2.y) 1
            else if (p1.y > p2.y) -1
            else 0
        }
    }
}