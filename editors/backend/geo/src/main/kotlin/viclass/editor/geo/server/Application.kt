package viclass.editor.geo.server

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.core.util.DefaultIndenter
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.SerializerProvider
import com.fasterxml.jackson.databind.deser.std.StdDeserializer
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.databind.ser.std.StdSerializer
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.KotlinModule
import common.libs.logger.Logging
import io.ktor.http.*
import io.ktor.serialization.jackson.*
import io.ktor.server.application.*
import io.ktor.server.netty.*
import io.ktor.server.plugins.callloging.*
import io.ktor.server.plugins.compression.*
import io.ktor.server.plugins.contentnegotiation.*
import io.ktor.server.plugins.cors.routing.*
import io.ktor.server.plugins.statuspages.*
import io.ktor.server.response.*
import io.ktor.server.routing.*
import kotlinx.coroutines.runBlocking
import org.koin.core.KoinApplication
import org.koin.core.context.GlobalContext.getKoinApplicationOrNull
import org.koin.core.context.GlobalContext.startKoin
import org.slf4j.event.Level
import viclass.editor.geo.impl.controller.ConstructionController
import viclass.editor.geo.impl.controller.DocumentController
import viclass.editor.geo.impl.controller.TemplateController
import viclass.editor.geo.impl.koin.koinApplication
import kotlin.reflect.KClass
import kotlin.system.exitProcess

/**
 * setup ktor server
 * setup application
 *
 * <AUTHOR>
 */

val logger = (object : Logging {
    override val loggerName = "Application"
}).logger

fun runKtorServer(args: Array<String>) {
    logger.info("Ktor server starting...")
    try {
        EngineMain.main(args)
        logger.info("Ktor server stopped")
    } catch (t: Throwable) {
        logger.error("Ktor server start failed!!!...", t)
        exitProcess(1)
    }
}

fun Application.module() {
    startKoin(koinApplication)
    setupApplication()
    install(CORS) {
        allowHost("*", listOf("http", "https"))
        allowOrigins {
            it in listOf(
                "http://localhost",
                "https://viclass.vn",
                "https://devlocal.viclass.vn",
                "https://staging.viclass.vn",
                "*"
            )
        }
        allowMethod(HttpMethod.Get)
        allowMethod(HttpMethod.Post)
        allowMethod(HttpMethod.Options)
        allowCredentials = true
        allowHeader(HttpHeaders.ContentType)
    }
    install(Compression)
    configureRouting()
    configureStatusPages()
    configureSerialization()
    configureLogging()
}

val Application.appKoin: KoinApplication
    get() {
        return getKoinApplicationOrNull()!!
    }

fun Application.setupApplication() = runBlocking {
    appKoin.koin.get<TemplateController>().reloadMappingTextCache()
}

private fun Application.configureStatusPages() {
    install(StatusPages) {
        status(HttpStatusCode.NotFound) { call, status ->
            call.respondText(text = "404: Page Not Found", status = status)
        }
        exception<Throwable> { call, cause ->
            logger.error("unknown exception... ", cause)
            call.respondText(text = "500: InternalServerError", status = HttpStatusCode.InternalServerError)
        }
    }
}

private fun Application.configureRouting() {

    routing {
        constructorTemplateRouting(application.appKoin.koin.get<TemplateController>())
        constructionRouting(application.appKoin.koin.get<ConstructionController>())
        documentRouting(application.appKoin.koin.get<DocumentController>())
    }
}

private fun Application.configureLogging() {
    install(CallLogging) {
        level = Level.DEBUG
    }
}

private fun Application.configureSerialization() {
    install(ContentNegotiation) {
        jackson {
            setDefaultPrettyPrinter(DefaultPrettyPrinter().apply {
                indentArraysWith(DefaultPrettyPrinter.FixedSpaceIndenter.instance)
                indentObjectsWith(DefaultIndenter("  ", "\n"))
            })
            registerModule(JavaTimeModule())  // support java.time.* types
            registerModule(
                KotlinModule.Builder().build().registerKClass()
            ) // register KClassSerializer, KClassDeserializer
        }
    }
}

/**
 * Define KClass kotlin module for jackson
 */
private fun KotlinModule.registerKClass(): SimpleModule {
    class KClassSerializer : StdSerializer<KClass<*>>(KClass::class.java) {
        override fun serialize(value: KClass<*>, gen: JsonGenerator, provider: SerializerProvider) {
            gen.writeString(value.qualifiedName)
        }
    }

    class KClassDeserializer : StdDeserializer<KClass<*>>(KClass::class.java) {
        override fun deserialize(p: JsonParser, ctxt: DeserializationContext?): KClass<*> {
            val qualifiedName = p.text
            return Class.forName(qualifiedName).kotlin
        }
    }

    return this
        .addSerializer(KClass::class.java, KClassSerializer())
        .addDeserializer(KClass::class.java, KClassDeserializer())
}
