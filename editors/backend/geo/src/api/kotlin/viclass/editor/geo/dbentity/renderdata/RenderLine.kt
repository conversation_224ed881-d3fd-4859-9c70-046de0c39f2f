package viclass.editor.geo.dbentity.renderdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import viclass.editor.geo.NamePattern
import viclass.editor.geo.dbentity.DocDefaultElRenderProp
import viclass.editor.geo.dbentity.RenderProp
import viclass.editor.geo.dbentity.buildLineRenderProp
import viclass.editor.geo.elements.LineSegment
import viclass.editor.geo.elements.LineVi

@BsonDiscriminator(key = "renderElementType")
data class RenderLine @BsonCreator constructor(
    @BsonProperty("renderProp") override var renderProp: RenderProp,
    @BsonProperty("elType") override val elType: String = LineVi::class.simpleName!!
) : RenderElement {

    @BsonProperty("elIndexes")
    override var elIndexes: MutableSet<Int> = mutableSetOf()

    @BsonProperty("relIndex")
    override var relIndex: Int = -1

    @BsonProperty("name")
    override var name: String = ""

    @BsonProperty("length")
    var length: Double = 0.0

    @BsonProperty("usable")
    override var usable: Boolean = true

    @BsonProperty("deleted")
    override var deleted: Boolean? = null

    @BsonProperty("valid")
    override var valid: Boolean = true

    @BsonProperty("startPointIdx")
    var startPointIdx: Int = -1

    @BsonProperty("endPointIdx")
    var endPointIdx: Int? = null

    @BsonProperty("vector")
    var vector: DoubleArray = doubleArrayOf(0.0, 0.0, 0.0)

    @BsonProperty("vertexRelIdxes")
    var vertexRelIdxes: MutableSet<Int> = mutableSetOf()

    fun orderedVector(): DoubleArray {
        val hasEnd = endPointIdx != null && endPointIdx != -1
        val needReverse = if (hasEnd) {
            startPointIdx > endPointIdx!!
        } else {
            false
        }

        return if (needReverse) {
            doubleArrayOf(-vector[0], -vector[1], -vector[2])
        } else {
            vector
        }
    }

    override fun isNameMatch(_name: String): Boolean {
        if (!NamePattern.isNameValid(LineVi::class, name)) return false
        if (!NamePattern.isNameValid(LineVi::class, _name)) return false
        if (name == _name) return true
        NamePattern[LineSegment::class]!![0].find(_name)?.let {
            if (it.groupValues.size == 3) {
                val p1Name = it.groupValues[1]
                val p2Name = it.groupValues[2]

                if (p1Name == p2Name) // not a valid name
                    return false

                if ((p2Name + p1Name) == name) return true
            }
        }
        return false
    }

    override fun clone(): RenderElement {
        return copy().also {
            cloneBase(it)
            it.length = length
            it.startPointIdx = startPointIdx
            it.endPointIdx = endPointIdx
            it.vector = vector.clone()
            it.vertexRelIdxes = vertexRelIdxes.toMutableSet()
        }
    }

    override fun setDefaultProp(): RenderProp {
        renderProp = buildLineRenderProp(DocDefaultElRenderProp())
        return renderProp
    }
}
