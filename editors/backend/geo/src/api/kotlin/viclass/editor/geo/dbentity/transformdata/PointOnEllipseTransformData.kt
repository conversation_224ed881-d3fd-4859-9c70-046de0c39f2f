package viclass.editor.geo.dbentity.transformdata

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty

/**
 * Simplified transform data for point on ellipse with angle transformation.
 * Contains only the essential properties needed for angle calculation.
 */
@BsonDiscriminator(key = "type")
data class PointOnEllipseTransformData @BsonCreator constructor(
    @BsonProperty("targetParamIdx") val targetParamIdx: Int,
    @BsonProperty("paramKind") val paramKind: String,
    @BsonProperty("center") val center: DoubleArray,
    @BsonProperty("f2") val f2: DoubleArray
) : TransformData {
     override fun clone(): TransformData {
        return copy()
    }
}