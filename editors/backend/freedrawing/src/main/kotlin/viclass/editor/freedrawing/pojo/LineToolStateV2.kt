package viclass.editor.freedrawing.pojo

import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonProperty

/**
 * Tool state for LineV2Tool with enhanced features.
 * This is separate from CommonToolState to allow future extensions like arrows, curves, etc.
 */
data class LineV2ToolState @BsonCreator constructor(
    @BsonProperty("lineWidth")
    val lineWidth: Int,
    
    @BsonProperty("stroke")
    val stroke: String? = "#000000",

    @BsonProperty("startArrow")
    val startArrow: Boolean = false,

    @BsonProperty("endArrow")
    val endArrow: Boolean = false
)