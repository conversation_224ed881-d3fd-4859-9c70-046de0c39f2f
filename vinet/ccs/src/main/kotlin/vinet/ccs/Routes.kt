package vinet.ccs

import io.ktor.server.application.*
import io.ktor.server.routing.*
import vinet.ccs.controllers.*
import vinet.ccs.utility.logLongExecution

private const val API_TIMEOUT_MS: Long = 5_000

fun Route.manageRoutes(manageCtrl: ManageCtrl) {
    get("/manage/roominfo/{id}") { logLongExecution("manage/roominfo", API_TIMEOUT_MS, { manageCtrl.fetchRoom(call) }) }
    get("/manage/registerSyncPeer") { logLongExecution("manage/registerSyncPeer", API_TIMEOUT_MS, { manageCtrl.registerSyncPeer(call) }) }
    get("/manage/registerUserPeer") { logLongExecution("manage/registerUserPeer", API_TIMEOUT_MS, { manageCtrl.registerUserPeer(call) }) }
}

fun Route.conferenceRoutes(confCtrl: ConferenceCtrl) {

    /**
     * get the Jitsi JWT token to join the
     * video call
     */
    post("/conference/jitsi/jwtToken") { logLongExecution("conference/jitsi/jwtToken", API_TIMEOUT_MS, { confCtrl.generateJitsiJwtToken(call) }) }
}

fun Route.signalRoutes(signalCtrl: SignalCtrl) {
    post("/signal/disconnect/{peerId}") { logLongExecution("signal/disconnect", API_TIMEOUT_MS, { signalCtrl.disconnect(call) }) }
    get("/signal/poll/{peerId}") { logLongExecution("signal/poll", API_TIMEOUT_MS, { signalCtrl.poll(call) }) }
    get("/signal/join/{roomId}") { logLongExecution("signal/join", API_TIMEOUT_MS, { signalCtrl.join(call) }) }
    post("/signal/send") { logLongExecution("signal/send", API_TIMEOUT_MS, { signalCtrl.send(call) }) }
    post("/signal/request") { logLongExecution("signal/request", API_TIMEOUT_MS, { signalCtrl.request(call) }) }
    post("/signal/reply") { logLongExecution("signal/reply", API_TIMEOUT_MS, { signalCtrl.reply(call) }) }
    post("/signal/report") { logLongExecution("signal/report", API_TIMEOUT_MS, { signalCtrl.report(call) }) }
}

fun Route.coordinatorStateRoutes(coordinatorStateCtrl: CoordinatorStateCtrl) {
    post("/coordinatorstate/create") { logLongExecution("coordinatorstate/create", API_TIMEOUT_MS, { coordinatorStateCtrl.createCoordinatorState(call) }) }
    post("/coordinatorstate/delete") { logLongExecution("coordinatorstate/delete", API_TIMEOUT_MS, { coordinatorStateCtrl.deleteCoordinatorState(call) }) }
    post("/coordinatorstate/rename") { logLongExecution("coordinatorstate/rename", API_TIMEOUT_MS, { coordinatorStateCtrl.renameCoordinatorState(call) }) }
    post("/coordinator/cmd") { logLongExecution("coordinator/cmd", API_TIMEOUT_MS, { coordinatorStateCtrl.receiveCmd(call) }) }
    post("/coordinatorstate/pin") { logLongExecution("coordinatorstate/pin", API_TIMEOUT_MS, { coordinatorStateCtrl.pinCoordinatorState(call) }) }
    post("/coordinatorstate/unpin") { logLongExecution("coordinatorstate/unpin", API_TIMEOUT_MS, { coordinatorStateCtrl.unpinCoordinatorState(call) }) }
    post("/coordinatorstate/request-pin-tab") { logLongExecution("coordinatorstate/request-pin-tab", API_TIMEOUT_MS, { coordinatorStateCtrl.requestPinTab(call) }) }
    post("/coordinatorstate/cancel-request-pin-tab") { logLongExecution("coordinatorstate/cancel-request-pin-tab", API_TIMEOUT_MS, { coordinatorStateCtrl.cancelRequestPinTab(call) }) }
    post("/coordinatorstate/approve-request-pin-tab") { logLongExecution("coordinatorstate/approve-request-pin-tab", API_TIMEOUT_MS, { coordinatorStateCtrl.approveRequestPinTab(call) }) }
    post("/coordinatorstate/reject-request-pin-tab") { logLongExecution("coordinatorstate/reject-request-pin-tab", API_TIMEOUT_MS, { coordinatorStateCtrl.rejectRequestPinTab(call) }) }
    get("/coordinatorstate/{id}") { logLongExecution("coordinatorstate/getById", API_TIMEOUT_MS, { coordinatorStateCtrl.getCoordStateById(call) }) }
    post("/coordinatorstate/ids") { logLongExecution("coordinatorstate/getByIds", API_TIMEOUT_MS, { coordinatorStateCtrl.getCoordStatesByIds(call) }) }
    get("/coordinatorstate/fetchByUser") { logLongExecution("coordinatorstate/fetchByUser", API_TIMEOUT_MS, { coordinatorStateCtrl.getCoordStateByUser(call) }) }
    post("/coordinatorstate/add-docs-mapping-and-layers") {
        logLongExecution(
            "coordinatorstate/add-docs-mapping-and-layers",
            API_TIMEOUT_MS,
            { coordinatorStateCtrl.addMultipleDocMappingAndLayers(call) })
    }
    post("/coordinatorstate/remove-multiple-doc-mapping-and-layers") {
        logLongExecution(
            "coordinatorstate/remove-multiple-doc-mapping-and-layers",
            API_TIMEOUT_MS,
            { coordinatorStateCtrl.removeMultipleDocMappingAndLayers(call) })
    }
    post("/coordinatorstate/duplicate") { logLongExecution("coordinatorstate/duplicate", API_TIMEOUT_MS, { coordinatorStateCtrl.duplicateCoordState(call) }) }
    post("/coordinatorstate/presentCoordState") { logLongExecution("coordinatorstate/presentCoordState", API_TIMEOUT_MS, { coordinatorStateCtrl.presentCoordState(call) }) }
    post("/coordinatorstate/update-layer-position") { logLongExecution("coordinatorstate/update-layer-position", API_TIMEOUT_MS, { coordinatorStateCtrl.updateLayer(call) }) }
    post("/coordinatorstate/remove-layer") { logLongExecution("coordinatorstate/remove-layer", API_TIMEOUT_MS, { coordinatorStateCtrl.removeLayerInfo(call) }) }
    get("/document/fetchByGlobalId") { logLongExecution("document/fetchByGlobalId", API_TIMEOUT_MS, { coordinatorStateCtrl.getDocumentByGlobalId(call) }) }
    get("/document/fetchByLocalId") { logLongExecution("document/fetchByLocalId", API_TIMEOUT_MS, { coordinatorStateCtrl.getDocumentByLocalId(call) }) }
}

fun Route.localContentRoutes(localContentCtrl: LocalContentCtrl) {
    get("/localcontent/{id}") { logLongExecution("localcontent/getById", API_TIMEOUT_MS, { localContentCtrl.getLocalContentStateById(call) }) }
    post("/localcontent/update") { logLongExecution("localcontent/update", API_TIMEOUT_MS, { localContentCtrl.updateLocalContentState(call) }) }
}

fun Route.metadataRoutes(metadataDocCtrl: MetadataDocCtrl) {
    post("/document/create-doc-info") { logLongExecution("document/create-doc-info", API_TIMEOUT_MS, { metadataDocCtrl.createMetadataDocs(call) }) }
    post("/document/update-doc-info") { logLongExecution("document/update-doc-info", API_TIMEOUT_MS, { metadataDocCtrl.updateMetadataDoc(call) }) }
    post("/document/mark-doc-valid") { logLongExecution("document/mark-doc-valid", API_TIMEOUT_MS, { metadataDocCtrl.markValidMetadataDoc(call) }) }
    post("/document/mark-docs-valid") { logLongExecution("document/mark-docs-valid", API_TIMEOUT_MS, { metadataDocCtrl.markValidMetadataDocs(call) }) }
    post("/document/load-doc-info") { logLongExecution("document/load-doc-info", API_TIMEOUT_MS, { metadataDocCtrl.loadMetadataDoc(call) }) }
    post("/document/load-doc-infos") { logLongExecution("document/load-doc-infos", API_TIMEOUT_MS, { metadataDocCtrl.loadMetadataDocs(call) }) }
}

fun Route.classroomRoutes(classroomCtrl: ClassroomCtrl) {
    post("/classroom/start") { logLongExecution("classroom/start", API_TIMEOUT_MS, { classroomCtrl.startClass(call) }) }
    post("/classroom/stop") { logLongExecution("classroom/stop", API_TIMEOUT_MS, { classroomCtrl.stopClass(call) }) }
    post("/classroom/leave") { logLongExecution("classroom/leave", API_TIMEOUT_MS, { classroomCtrl.leaveClass(call) }) }
    post("/classroom/new-question") { logLongExecution("classroom/new-question", API_TIMEOUT_MS, { classroomCtrl.newQuestion(call) }) }
    post("/classroom/stop-question") { logLongExecution("classroom/stop-question", API_TIMEOUT_MS, { classroomCtrl.stopQuestion(call) }) }
    post("/classroom/request-presentation") { logLongExecution("classroom/request-presentation", API_TIMEOUT_MS, { classroomCtrl.requestPresentation(call) }) }
    post("/classroom/cancel-request-presentation") { logLongExecution("classroom/cancel-request-presentation", API_TIMEOUT_MS, { classroomCtrl.cancelRequestPresentation(call) }) }
    post("/classroom/accept-presentation") { logLongExecution("classroom/accept-presentation", API_TIMEOUT_MS, { classroomCtrl.acceptRequestPresentation(call) }) }
    post("/classroom/reject-presentation") { logLongExecution("classroom/reject-presentation", API_TIMEOUT_MS, { classroomCtrl.rejectPresentation(call) }) }
    post("/classroom/stop-presentation") { logLongExecution("classroom/stop-presentation", API_TIMEOUT_MS, { classroomCtrl.stopPresentation(call) }) }
    post("/classroom/req-share-screen") { logLongExecution("classroom/req-share-screen", API_TIMEOUT_MS, { classroomCtrl.reqShareScreen(call) }) }
    post("/classroom/accept-share-screen") { logLongExecution("classroom/accept-share-screen", API_TIMEOUT_MS, { classroomCtrl.acceptShareScreen(call) }) }
    post("/classroom/reject-share-screen") { logLongExecution("classroom/reject-share-screen", API_TIMEOUT_MS, { classroomCtrl.rejectShareScreen(call) }) }
    post("/classroom/cancel-share-screen") { logLongExecution("classroom/cancel-share-screen", API_TIMEOUT_MS, { classroomCtrl.cancelShareScreen(call) }) }
    post("/classroom/raise-hand") { logLongExecution("classroom/raise-hand", API_TIMEOUT_MS, { classroomCtrl.raiseHand(call) }) }
    post("/classroom/accept-raise-hand") { logLongExecution("classroom/accept-raise-hand", API_TIMEOUT_MS, { classroomCtrl.acceptRaiseHand(call) }) }
    post("/classroom/reject-raise-hand") { logLongExecution("classroom/reject-raise-hand", API_TIMEOUT_MS, { classroomCtrl.rejectRaiseHand(call) }) }
    post("/classroom/cancel-raise-hand") { logLongExecution("classroom/cancel-raise-hand", API_TIMEOUT_MS, { classroomCtrl.cancelRaiseHand(call) }) }
    get("/classroom/roominfo/{id}") { logLongExecution("classroom/roominfo", API_TIMEOUT_MS, { classroomCtrl.fetchRoom(call) }) }
}
