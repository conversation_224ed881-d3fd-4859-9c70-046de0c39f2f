package vinet.ccs.actor

import common.libs.logger.Logging
import org.koin.core.annotation.Singleton
import java.util.concurrent.ConcurrentHashMap

@Singleton
class ActorManager : Logging {

    // Thread-safe map for storing actors, keyed by "roomId.userId"
    private val actors = ConcurrentHashMap<String, UserClassroomActor>()
    // Thread-safe map for storing room-specific checkers, keyed by roomId
    private val classroomCheckers = ConcurrentHashMap<String, ClassroomChecker>()

    /**
     * Adds or replaces a UserClassroomActor.
     * If an actor with the same key (roomId.userId) exists, its destroy() method
     * will be called asynchronously *after* it has been replaced in the map.
     * This operation is thread-safe.
     *
     * @param actor The actor to add or replace.
     */
    suspend fun addActor(actor: UserClassroomActor) { // Make this function suspend
        var oldActorToDestroy: UserClassroomActor? = null

        // Atomically update the map and capture the old value if it exists
        actors.compute(actor.actorName) { key, existingValue ->
            if (existingValue != null) {
                // An actor with the same key exists. Mark it for destruction.
                // Avoid marking for destruction if replacing with the exact same instance
                if (existingValue !== actor) {
                    logger.warn("Replacing existing actor instance for {}. Old instance will be destroyed.", key)
                    oldActorToDestroy = existingValue // Capture the old actor reference
                } else {
                    logger.debug("Attempting to replace actor {} with the same instance. No destruction needed.", key)
                }
            } else {
                // New actor added.
                logger.info("Actor added: {}", key)
            }
            actor // Return the new actor instance to be stored in the map
        }

        // Call destroy() on the old actor *outside* the compute block
        // This can now be done because addActor is a suspend function
        oldActorToDestroy?.let { oldActor ->
            try {
                logger.debug("Calling destroy() on the replaced actor instance: {}", oldActor.actorName)
                oldActor.destroy() // Call the suspend function
                logger.info("Successfully destroyed replaced actor instance: {}", oldActor.actorName)
            } catch (e: Exception) {
                // Log error during destruction, but the map is already updated.
                logger.error("Error destroying replaced actor instance {}: {}", oldActor.actorName, e.message, e)
            }
        }

        // Notify ClassroomChecker that an actor joined
        getClassroomChecker(actor.roomId)?.onActorJoined()
    }

    /**
     * Retrieves a UserClassroomActor by roomId and userId.
     *
     * @param roomId The ID of the room.
     * @param userId The ID of the user.
     * @return The actor if found, otherwise null.
     */
    fun getActor(roomId: String, userId: String): UserClassroomActor? {
        // ConcurrentHashMap.get is thread-safe
        return actors["$roomId.$userId"]
    }

    /**
     * Removes a specific UserClassroomActor instance if it's currently mapped to its key.
     * Calls the actor's destroy() method if the specific instance was removed,
     * or if the removal attempt failed but the passed instance should still be cleaned up.
     * This operation is thread-safe and atomic for the removal check.
     *
     * @param actor The specific actor instance to remove.
     */
    suspend fun removeActor(actor: UserClassroomActor) {
        val actorName = actor.actorName
        // Atomically remove the actor *only if* the key is mapped to this specific instance
        val removed = actors.remove(actorName, actor)

        if (removed) {
            logger.info("Removed actor instance: {}", actorName)
            // Check if the room became empty and notify checker
            // This check should be efficient.
            val remainingInRoom = getActors(actor.roomId) // This iterates, consider optimizing if very high frequency
            if (remainingInRoom.isEmpty()) {
                logger.info("Room {} became empty after removing actor {}.", actor.roomId, actor.userId)
                getClassroomChecker(actor.roomId)?.onRoomBecameEmpty()
            }
        } else {
            // The actor wasn't in the map, or a different instance was present.
            // The original code destroyed the passed instance even if it wasn't the one in the map.
            // Preserve this behavior: ensure destroy is called on the instance passed to this method.
            logger.warn(
                "Attempted to remove actor {}, but it was not found or was a different instance in the map. Destroying the provided instance anyway.",
                actorName
            )
        }
    }

    /**
     * Filters and returns a list of all actors belonging to a specific room.
     * This provides a snapshot based on the concurrent map's weakly consistent iterator.
     *
     * @param roomId The ID of the room to filter by.
     * @return A list of actors in the specified room.
     */
    fun getActors(roomId: String): List<UserClassroomActor> {
        // ConcurrentHashMap's iterator is weakly consistent and thread-safe.
        // Filtering by key prefix is reasonably efficient for moderate map sizes.
        // Consider alternative indexing (e.g., Map<String, Set<String>>) only if this becomes a bottleneck.
        val prefix = "$roomId."
        return actors.filter { it.key.startsWith(prefix) }.values.toList()
    }

    /**
     * Adds or replaces a ClassroomChecker for a given room.
     * If a checker for the room already exists, it is overwritten.
     *
     * @param classroomChecker The checker to add or replace.
     */
    suspend fun addClassroomChecker(classroomChecker: ClassroomChecker) {
        // ConcurrentHashMap.put is atomic. It returns the previous value.
        val previousChecker = classroomCheckers.put(classroomChecker.roomId, classroomChecker)
        if (previousChecker != null) {
            logger.warn("Replaced existing classroom checker for room {}", classroomChecker.roomId)
            // cancel the previous checker if it exists
             previousChecker.cancel()
        } else {
            logger.info("Added classroom checker for room {}", classroomChecker.roomId)
        }
    }

    /**
     * Retrieves the ClassroomChecker for a specific room.
     *
     * @param roomId The ID of the room.
     * @return The checker if found, otherwise null.
     */
    fun getClassroomChecker(roomId: String): ClassroomChecker? = classroomCheckers[roomId]

    /**
     * Removes the ClassroomChecker for the specified room and cancels its operations.
     *
     * @param roomId The ID of the room whose checker should be removed.
     */
    suspend fun removeClassroomChecker(roomId: String) {
        // ConcurrentHashMap.remove is atomic.
        val checker = classroomCheckers.remove(roomId)
        if (checker != null) {
            logger.info("Removing and cancelling ClassroomChecker for room {}", roomId)
            checker.cancel() // Ensure the checker cleans up its resources
        } else {
            // This is not necessarily an error, the checker might have already been removed.
            logger.debug("Attempted to remove ClassroomChecker for room {}, but it was not found.", roomId)
        }
    }
}