{"name": "Contribute to Starlight", "build": {"dockerfile": "Dockerfile"}, "postCreateCommand": "sudo corepack enable pnpm && pnpm config set store-dir /home/<USER>/.pnpm-store && PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=1 pnpm install", "waitFor": "postCreateCommand", "customizations": {"codespaces": {"openFiles": ["CONTRIBUTING.md"]}, "vscode": {"extensions": ["astro-build.astro-vscode", "esbenp.prettier-vscode"]}}}