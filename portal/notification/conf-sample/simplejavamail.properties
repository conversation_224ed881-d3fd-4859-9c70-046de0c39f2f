simplejavamail.smtp.host=smtp-relay.brevo.com
simplejavamail.smtp.port=587
simplejavamail.smtp.username=<EMAIL>
simplejavamail.smtp.password=O7HarkRsV9pY2Tth
simplejavamail.javaxmail.debug=true

simplejavamail.extraproperties.mail.smtp.timeout=30000
#simplejavamail.extraproperties.my.extra.property=value
#simplejavamail.extraproperties.mail.smtp.ssl.socketFactory.class=org.mypackage.MySSLSocketFactory

#simplejavamail.custom.sslfactory.class=org.mypackage.ssl.MySSLSocketFactoryClass
#simplejavamail.transportstrategy=SMTPS
#simplejavamail.proxy.host=proxy.default.com
#simplejavamail.proxy.port=1080
#simplejavamail.proxy.username=username proxy
#simplejavamail.proxy.password=password proxy
#simplejavamail.proxy.socks5bridge.port=1081
#simplejavamail.defaults.content.transfer.encoding=BINARY
#simplejavamail.defaults.subject=Sweet News
#simplejavamail.defaults.from.name=From Default
#simplejavamail.defaults.from.address=<EMAIL>
#simplejavamail.defaults.replyto.name=Reply-To Default
#simplejavamail.defaults.replyto.address=<EMAIL>
#simplejavamail.defaults.bounceto.name=Bounce-To Default
#simplejavamail.defaults.bounceto.address=<EMAIL>
#simplejavamail.defaults.to.name=To Default
#simplejavamail.defaults.to.address=<EMAIL>
#simplejavamail.defaults.cc.name=CC Default
#simplejavamail.defaults.cc.address=<EMAIL>
#simplejavamail.defaults.bcc.name=
#simplejavamail.defaults.bcc.address=<EMAIL>;<EMAIL>
#simplejavamail.defaults.poolsize=10
#simplejavamail.defaults.poolsize.keepalivetime=2000
#simplejavamail.defaults.connectionpool.clusterkey.uuid=38400000-8cf0-11bd-b23e-10b96e4ef00d
#simplejavamail.defaults.connectionpool.coresize=0
#simplejavamail.defaults.connectionpool.maxsize=4
#simplejavamail.defaults.connectionpool.claimtimeout.millis=10000
#simplejavamail.defaults.connectionpool.expireafter.millis=5000
#simplejavamail.defaults.connectionpool.loadbalancing.strategy=ROUND_ROBIN
#simplejavamail.defaults.sessiontimeoutmillis=60000
#simplejavamail.defaults.trustallhosts=false
## following property is ignored when trustallhosts is true:
#simplejavamail.defaults.trustedhosts=*************;mymailserver.com;ix55432y
#simplejavamail.defaults.verifyserveridentity=true
#simplejavamail.transport.mode.logging.only=true
#simplejavamail.opportunistic.tls=false
## following properties are used as defaults on Mailer level
#simplejavamail.smime.signing.keystore=my_keystore.pkcs12
#simplejavamail.smime.signing.keystore_password=keystore_password
#simplejavamail.smime.signing.key_alias=key_alias
#simplejavamail.smime.signing.key_password=key_password
#simplejavamail.smime.encryption.certificate=x509inStandardPEM.crt
#simplejavamail.dkim.signing.private_key_file_or_data=my_dkim_key.der # or key as base64
#simplejavamail.dkim.signing.selector=dkim1
#simplejavamail.dkim.signing.signing_domain=your-domain.com
#simplejavamail.dkim.signing.excluded_headers_from_default_signing_list=From
#simplejavamail.embeddedimages.dynamicresolution.enable.dir=true
#simplejavamail.embeddedimages.dynamicresolution.enable.url=false
#simplejavamail.embeddedimages.dynamicresolution.enable.classpath=true
#simplejavamail.embeddedimages.dynamicresolution.base.dir=/var/opt/static
#simplejavamail.embeddedimages.dynamicresolution.base.url=
#simplejavamail.embeddedimages.dynamicresolution.base.classpath=/static
#simplejavamail.embeddedimages.dynamicresolution.outside.base.dir=true
#simplejavamail.embeddedimages.dynamicresolution.outside.base.classpath=false
#simplejavamail.embeddedimages.dynamicresolution.outside.base.url=false
#simplejavamail.embeddedimages.dynamicresolution.mustbesuccesful=true
