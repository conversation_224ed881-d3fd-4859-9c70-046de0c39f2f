package portal.notification.pojo.notidata

import org.bson.BsonType
import org.bson.codecs.pojo.annotations.BsonCreator
import org.bson.codecs.pojo.annotations.BsonDiscriminator
import org.bson.codecs.pojo.annotations.BsonProperty
import org.bson.codecs.pojo.annotations.BsonRepresentation

@BsonDiscriminator(value = "ReqShareScreenND", key = "entityType")
data class ReqShareScreenND @BsonCreator constructor(
    @BsonProperty("lsId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val lsId: String,

    @BsonProperty("callingUserId")
    @BsonRepresentation(BsonType.OBJECT_ID)
    val callingUserId: String,

): NotificationData
