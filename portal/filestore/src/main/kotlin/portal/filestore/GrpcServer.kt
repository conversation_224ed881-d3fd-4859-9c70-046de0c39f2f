package portal.filestore

import common.libs.logger.Logging
import io.grpc.Server
import io.grpc.ServerBuilder
import io.ktor.server.application.*
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.koin.core.annotation.Single
import org.koin.ktor.ext.inject
import portal.filestore.configs.GrpcServerConfig
import portal.filestore.services.GrpcFileStoreServiceImpl
import java.io.IOException

@Single
class GrpcServer(
    private val serverConfig: GrpcServerConfig,
    private val fileStoreService: GrpcFileStoreServiceImpl,
): Logging{
    private val server: Server = ServerBuilder.forPort(serverConfig.port)
        .addService(fileStoreService)
        .build()

    @Throws(IOException::class)
    fun start() {
        logger.debug("FILE STORE GRPC SERVER STARTING")
        server.start()
        logger.debug("File Store GRPC Server started, listening on {}", serverConfig.port)
    }


    /**
     * Await termination on the main thread since the grpc library uses daemon threads.
     */
    @Throws(InterruptedException::class)
    fun blockUntilShutdown() {
        server.awaitTermination()
    }

    /**
     * Initiates an orderly shutdown in which preexisting calls continue but new calls are rejected.
     * After this call returns, this server has released the listening socket(s) and may be reused by
     * another server.
     *
     * <p>Note that this method will not wait for preexisting calls to finish before returning.
     * {@link #awaitTermination()} or {@link #awaitTermination(long, TimeUnit)} needs to be called to
     * wait for existing calls to finish.
     */
    fun shutdown() {
        server.shutdown()
        logger.info("Shutdown Server stopped")
    }

    /**
     * Initiates a forceful shutdown in which preexisting and new calls are rejected. Although
     * forceful, the shutdown process is still not instantaneous; {@link #isTerminated()} will likely
     * return {@code false} immediately after this method returns. After this call returns, this
     * server has released the listening socket(s) and may be reused by another server.
     */
    fun shutdownNow() {
        server.shutdownNow()
        logger.info("ShutdownNow Server stopped")
    }
}


fun Application.startGrpcServer() {
    CoroutineScope(Dispatchers.IO).launch {
        val grpcServer by inject<GrpcServer>()
        grpcServer.start()

        //Add a shutdown hook so that if the JVM is stopped the os process is also terminated
        Runtime.getRuntime().addShutdownHook(Thread {
            grpcServer.shutdown()
        })

        // Wait for the server to be stopped
        grpcServer.blockUntilShutdown()
    }
}