package portal.lsession

import common.libs.logger.Logging
import io.reactivex.rxjava3.plugins.RxJavaPlugins
import org.koin.core.context.GlobalContext.getKoinApplicationOrNull
import org.koin.core.context.GlobalContext.startKoin
import portal.lsession.koin.koinApplication
import portal.lsession.server.Server


/**
 *
 * <AUTHOR>
 */
object Launcher : Logging {
    @JvmStatic
    fun main(args: Array<String>) {
        logger.info("Launching learning session service")

        RxJavaPlugins.setErrorHandler {
            logger.error("Uncaught error... ", it)
        }

        startKoin(koinApplication)
        val server = getKoinApplicationOrNull()!!.koin.get<Server>()

        // start account manager server
        server.start()

        //Add a shutdown hook so that if the JVM is stopped, the os process is also terminated
        Runtime.getRuntime().addShutdownHook(Thread {
            logger.info("Shutting down learning session service")
            server.shutdown()
            logger.info("Shutdown learning session service")
        })

        server.blockUntilShutdown()
    }
}
