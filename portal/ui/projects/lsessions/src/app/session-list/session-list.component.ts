import {Component, Inject, OnInit} from '@angular/core';
import {UserProfile, USER_PROFILE, MemberService, LsessionSummary, MemberRegistrationAction,
  LSessionSearchItem, SearchSessionFormData, LSessionService} from '@viclass/portal.common';
import {Router} from "@angular/router";
import {Subscription, timer} from "rxjs";


@Component({
  selector: 'ls-session-list',
  templateUrl: './session-list.component.html',
  styleUrls: ['./session-list.component.sass']
})
export class SessionListComponent implements OnInit {
  lsContainers: LSessionSearchItem[]
  now: number

  private timer: Subscription;

  constructor(
    private router: Router,
    @Inject(USER_PROFILE) public userProfile : UserProfile,
    private memService : MemberService,
    private lsService: LSessionService
  ) {
  }

  ngOnInit(): void {
    this.timer = timer(0, 1000).subscribe((seconds) => {
      this.now = Date.now()
    })
  }


  ngOnDestroy(): void {
    this.timer.unsubscribe();
  }

  submitSearchSession(data: SearchSessionFormData) {
    data.userId = this.userProfile?.id
    this.lsService.sessionSummaryList(data).subscribe(res => {
      this.lsContainers = res
    })
  }

  sendRegistrationLSession(event : MemberRegistrationAction, summary: LsessionSummary) {
    this.router.navigate([`/lsession-details/${summary.id}`], {queryParams: {reg: event}})
  }

}
