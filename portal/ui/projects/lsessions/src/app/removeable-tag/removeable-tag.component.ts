import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { SearchbarComponent } from '../searchbar/searchbar.component';

@Component({
  selector: 'ls-removeable-tag',
  templateUrl: './removeable-tag.component.html',
  styleUrls: ['./removeable-tag.component.sass']
})
export class RemoveableTagComponent implements OnInit {

  @Input()
  label : string = ""

  @Input()
  index : number = 0

  @Output()
  onRemove : EventEmitter<RemoveableTagComponent> = new EventEmitter<RemoveableTagComponent>()

  constructor() { }

  ngOnInit(): void {
  }

  removeMe() {
    this.onRemove.next(this)
  }

}
