import { Component, EventEmitter, OnInit, Output } from "@angular/core";
import { UntypedFormBuilder } from "@angular/forms";
import { Router } from "@angular/router";
import {
  FormBuildingResult,
  SearchSessionFormData,
  FormCreator,
  FormFlowSubmitEvent,
  FormFlowDirective,
  FormFlowNavigationData,
} from "@viclass/portal.common";
import { RemoveableTagComponent } from "../removeable-tag/removeable-tag.component";

@Component({
  selector: "ls-searchbar",
  templateUrl: "./searchbar.component.html",
  styleUrls: ["./searchbar.component.sass"],
})
export class SearchbarComponent implements OnInit {
  @Output()
  submit = new EventEmitter<SearchSessionFormData>();

  initialData: SearchSessionFormData = {
    keywords: ["Đang diễn ra", "Sắp diễn ra", "20 - 30"],
  };

  tags: Array<string> = [];
  isFilterOpen: boolean = false;

  constructor(private fb: UntypedFormBuilder, private router: Router) {}

  ngOnInit(): void {
    this.submit.emit(this.initialData);
  }

  removeTag(tag: RemoveableTagComponent) {
    this.tags.splice(tag.index, 1);
  }

  buildForm = (data?: SearchSessionFormData): FormBuildingResult => {
    this.tags = [];

    data = data || this.initialData;
    this.tags.push(...data.keywords);

    // init the form with empty keywords
    let emptyData = {
      keywords: "",
    };
    let result = new FormCreator(this.fb, emptyData).build();

    return result;
  };

  submitSearch = (e: FormFlowSubmitEvent) => {
    let searchData = e.data as SearchSessionFormData;
    this.submit.emit(searchData);
  };

  processNavigation = (data: FormFlowNavigationData) => {
    this.router.navigate(["/"], { queryParams: data.params });
  };

  checkEnter(event: KeyboardEvent, formFlow: FormFlowDirective) {
    if (event.key == "Enter") {
      let el = event.target as HTMLInputElement;
      if (el.value && el.value != "") {
        this.tags.push(el.value);
        el.value = "";
      } else {
        let data: SearchSessionFormData = {
          keywords: [...this.tags],
        };
        formFlow.startSearchProcess(data);
      }
    }
  }

  clearTags() {
    this.tags.splice(0, this.tags.length);
  }
}
