package portal.user.server

import com.google.rpc.Status
import common.libs.logger.Logging
import io.grpc.Status.Code
import io.lettuce.core.api.sync.RedisCommands
import kotlinx.coroutines.rx3.await
import org.koin.core.annotation.Singleton
import portal.user.dbgateway.UserProfileGateway
import portal.user.dbgateway.UserRegistrationGateway
import portal.user.pojo.UserProfilePojo
import portal.user.pojo.UserRegistrationPojo
import portal.user.pojo.toAddressProto
import portal.user.utils.buildWithUserData
import portal.user.utils.isRegVerified
import proto.portal.user.UserMessage
import proto.portal.user.UserServiceGrpcKt

/**
 *
 * <AUTHOR>
 */
@Singleton
class UserService(
    private val userRegistrationGateway: UserRegistrationGateway,
    private val userProfileGateway: UserProfileGateway,
    private val emailRegistrationService: EmailRegistrationService,
    private val socialRegistrationService: SocialRegistrationService,
    private val userProfileService: UserProfileService,
    private val oneTimeLoginService: OneTimeLoginService,
    private val feedbackService: FeedbackService,
) : UserServiceGrpcKt.UserServiceCoroutineImplBase(), Logging {

    /**
     * Find user by user profile id.
     */
    override suspend fun getUserById(request: UserMessage.GetUserByIdRequest): UserMessage.GetUserResponse {
        val userId = request.userId
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserResponse.newBuilder()

        try {
            val userProfile = userProfileGateway.findUserProfileById(userId).await()
            val userReg = userRegistrationGateway.findRegistrationByEmail(userProfile.email).await()
            code = Code.OK
            builder.buildUser(userReg, userProfile)
        } catch (e: NoSuchElementException) {
            logger.error("get user by id failed, not found user {}", userId)
            code = Code.NOT_FOUND
            message = "Not found user $userId"
        } catch (t: Throwable) {
            logger.error("get user by id {} failed...", userId, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Find user login data by both email or username.
     *
     * !!! Only find the records from the EMAIL registration flow as it mainly used for the form in the login page
     */
    override suspend fun loginByUsernameOrEmail(request: UserMessage.LoginByUsernameOrEmailRequest): UserMessage.LoginByUsernameOrEmailResponse {
        val usernameEmail = request.usernameOrEmail
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.LoginByUsernameOrEmailResponse.newBuilder()

        if (usernameEmail.isNullOrBlank()) {
            code = Code.INVALID_ARGUMENT
            message = "username cannot be null or empty"
            return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
        }

        try {
            val userReg = userRegistrationGateway.findEmailRegistrationByUsernameOrEmail(usernameEmail).await()
            val userProfile = userProfileGateway.findUserProfileByEmail(userReg.email)

            code = Code.OK
            builder.setUser(UserMessage.UserProto.newBuilder().buildWithUserData(userReg, userProfile))
                .setPassword(userReg.password)
        } catch (e: NoSuchElementException) {
            logger.error("get login data failed, not found user {}", usernameEmail)
            code = Code.NOT_FOUND
            message = "Not found user $usernameEmail"
        } catch (t: Throwable) {
            logger.error("get login data {} failed...", usernameEmail, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Retrieves user information based on the provided username.
     *
     * This function fetches the user registration and profile details using the username.
     * If the username is not found, or is null or blank, an appropriate error code and message are returned.
     *
     * @param request The request containing the username of the user to be retrieved.
     * @return A GetUserResponse containing the user details or an error status if the user is not found.
     */
    override suspend fun getUserByUsername(request: UserMessage.GetUserByUsernameRequest): UserMessage.GetUserResponse {
        val username = request.username
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserResponse.newBuilder()

        if (username.isNullOrBlank()) {
            code = Code.INVALID_ARGUMENT
            message = "username cannot be null or empty"
            return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
        }

        try {
            val userReg = userRegistrationGateway.findRegistrationByUsername(username).await()
            val userProfile = userProfileGateway.findUserProfileByEmail(userReg.email)
            code = Code.OK
            builder.buildUser(userReg, userProfile)
        } catch (e: NoSuchElementException) {
            logger.error("get user by username failed, not found user {}", username)
            code = Code.NOT_FOUND
            message = "Not found user $username"
        } catch (t: Throwable) {
            logger.error("get user by username {} failed...", username, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Retrieves user information based on the provided email.
     *
     * This function fetches the user registration and profile details using the email.
     * If the email is not found, or is null or blank, an appropriate error code and message are returned.
     *
     * @param request The request containing the email of the user to be retrieved.
     * @return A GetUserResponse containing the user details or an error status if the user is not found.
     */
    override suspend fun getUserByEmail(request: UserMessage.GetUserByEmailRequest): UserMessage.GetUserResponse {
        val email = request.email
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserResponse.newBuilder()

        if (email.isNullOrBlank()) {
            code = Code.INVALID_ARGUMENT
            message = "email cannot be null or empty"
            return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
        }

        try {
            val userReg = userRegistrationGateway.findRegistrationByEmail(email).await()
            val userProfile = userProfileGateway.findUserProfileByEmail(userReg.email)
            code = Code.OK
            builder.buildUser(userReg, userProfile)
        } catch (e: NoSuchElementException) {
            logger.error("get user by email failed, not found user {}", email)
            code = Code.NOT_FOUND
            message = "Not found user $email"
        } catch (t: Throwable) {
            logger.error("get user by email {} failed...", email, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * @deprecated
     * Retrieves user information based on the provided phone.
     *
     * This function fetches the user registration and profile details using the phone.
     * If the phone is not found, or is null or blank, an appropriate error code and message are returned.
     *
     * @param request The request containing the phone of the user to be retrieved.
     * @return A GetUserResponse containing the user details or an error status if the user is not found.
     */
    override suspend fun getUserByPhone(request: UserMessage.GetUserByPhoneRequest): UserMessage.GetUserResponse {
        val phone = request.phone
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserResponse.newBuilder()

        if (phone.isNullOrBlank()) {
            code = Code.INVALID_ARGUMENT
            message = "phone cannot be null or empty"
            return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
        }

        try {
            val userReg = userRegistrationGateway.findRegistrationByPhone(phone).await()
            val userProfile = userProfileGateway.findUserProfileByEmail(userReg.email)
            code = Code.OK
            builder.buildUser(userReg, userProfile)
        } catch (e: NoSuchElementException) {
            logger.error("get user by phone failed, not found user {}", phone)
            code = Code.NOT_FOUND
            message = "Not found user $phone"
        } catch (t: Throwable) {
            logger.error("get user by phone {} failed...", phone, t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Retrieves user profile information by user ID.
     *
     * This function fetches the user profile details using the user ID.
     * If the user ID is not found, or is null or blank, an appropriate error code and message are returned.
     *
     * @param request The request containing the user ID of the user profile to be retrieved.
     * @return A GetUserProfileResponse containing the user profile details or an error status if the user is not found.
     */
    override suspend fun getUserProfileById(request: UserMessage.GetUserProfileByIdRequest): UserMessage.GetUserProfileResponse {
        val userId = request.userId
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserProfileResponse.newBuilder()

        try {
            val profile = userProfileGateway.findUserProfileById(request.userId).await()
            code = Code.OK
            builder.buildProfile(profile)
        } catch (e: NoSuchElementException) {
            logger.error("get user profile failed, not found user profile {}", userId)
            code = Code.NOT_FOUND
            message = "Not found user profile $userId"
        } catch (t: Throwable) {
            logger.error("get user failed...", t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Retrieves user profile information by username.
     *
     * This function fetches the user profile details using the username.
     * If the username is not found, or is null or blank, an appropriate error code and message are returned.
     *
     * @param request The request containing the username of the user profile to be retrieved.
     * @return A GetUserProfileResponse containing the user profile details or an error status if the user is not found.
     */
    override suspend fun getUserProfileByUsername(request: UserMessage.GetUserProfileByUsernameRequest): UserMessage.GetUserProfileResponse {
        val username = request.username
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserProfileResponse.newBuilder()

        try {
            val profile = userProfileGateway.findUserProfileByUsername(username).await()
            code = Code.OK
            builder.buildProfile(profile)
        } catch (e: NoSuchElementException) {
            logger.error("get user profile failed, not found user profile {}", username)
            code = Code.NOT_FOUND
            message = "Not found user profile $username"
        } catch (t: Throwable) {
            logger.error("get user failed...", t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Retrieves user profile information for a list of user IDs.
     * To be used by the LSession API
     *
     * This function fetches the user profile details for each user ID provided in the request.
     * If any user ID is not found, an error status is returned in the response.
     *
     * @param request The request containing the list of user IDs whose profiles are to be retrieved.
     * @return A GetUserProfileByIdsResponse containing the user profile details for the specified IDs
     *         or an error status if any user ID is not found.
     */
    override suspend fun getUserProfileByIds(request: UserMessage.GetUserProfileByIdsRequest): UserMessage.GetUserProfileByIdsResponse {
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserProfileByIdsResponse.newBuilder()

        try {
            val profiles = userProfileGateway.findUserProfileByIds(request.userIdList).await()

            code = Code.OK
            val profilesProto = profiles.map {
                UserMessage.UserProfileProto.newBuilder().setId(it.id).setUsername(it.username).setEmail(it.email)
                    .setPhone(it.phone).setAvatarUrl(it.avatarUrl).setName(it.name)
                    .setAddress(it.address.toAddressProto()).setGender(UserMessage.Gender.valueOf(it.gender.toString()))
                    .build()
            }
            builder.addAllUserProfile(profilesProto)
        } catch (t: Throwable) {
            logger.error("get user failed...", t)
            message = t.message ?: ""
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    /**
     * Retrieves user profile information for a given username or email.
     * To be used when login with username/email and password
     *
     * This function fetches the user profile details for the specified username or email.
     * If the username or email is not found, an error status is returned in the response.
     *
     * @param request The request containing the username or email of the user whose profile is to be retrieved.
     * @return A GetUserProfileResponse containing the user profile details for the specified username or email
     *         or an error status if the user is not found.
     */
    override suspend fun getUserProfileByUsernameOrEmail(request: UserMessage.GetUserByUsernameRequest): UserMessage.GetUserProfileResponse {
        val userId = request.username
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.GetUserProfileResponse.newBuilder()

        try {
            val profile = userProfileGateway.findUserProfileByUsernameOrEmail(request.username).await()
            code = Code.OK
            builder.buildProfile(profile)
        } catch (e: NoSuchElementException) {
            logger.error("get user profile failed, not found user profile {}", userId)
            code = Code.NOT_FOUND
            message = "Not found user profile $userId"
        } catch (t: Throwable) {
            logger.error("get user failed...", t)
            message = t.message ?: ""
            if (message.startsWith("invalid hexadecimal representation of an ObjectId")) code = Code.NOT_FOUND
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun createOrMergeUserProfile(request: UserMessage.CreateOrMergeUserProfileRequest): UserMessage.CreateOrMergeUserProfileResponse {
        return userProfileService.createOrMergeUserProfile(request)
    }

    override suspend fun checkRegistrationEmailExist(request: UserMessage.CheckRegistrationEmailExistRequest): UserMessage.CheckRegistrationEmailExistResponse {
        return emailRegistrationService.checkRegistrationEmailExist(request)
    }

    override suspend fun registerUserByEmail(request: UserMessage.EmailRegistrationRequest): UserMessage.EmailRegistrationResponse {
        return emailRegistrationService.registerUserByEmail(request)
    }

    override suspend fun verifySocialToken(request: UserMessage.VerifySocialTokenRequest): UserMessage.VerifySocialTokenResponse {
        return socialRegistrationService.verifySocialToken(request)
    }

    override suspend fun addSocialRegistration(request: UserMessage.AddSocialRegistrationRequest): UserMessage.AddSocialRegistrationResponse {
        return socialRegistrationService.addSocialRegistration(request)
    }

    override suspend fun unlinkRelinkSocialRegistration(request: UserMessage.UnlinkRelinkSocialRegistrationRequest): UserMessage.UnlinkRelinkSocialRegistrationResponse {
        return socialRegistrationService.unlinkRelinkSocialRegistration(request)
    }

    override suspend fun getLinkedRegistrations(request: UserMessage.GetLinkedRegistrationsRequest): UserMessage.GetLinkedRegistrationsResponse {
        return socialRegistrationService.getLinkedRegistrations(request)
    }

    override suspend fun sendVerificationEmail(request: UserMessage.SendVerificationEmailRequest): UserMessage.SendVerificationEmailResponse {
        return emailRegistrationService.sendVerificationEmail(request)
    }

    override suspend fun verifyEmail(request: UserMessage.EmailVerificationRequest): UserMessage.EmailVerificationResponse {
        return emailRegistrationService.verifyEmail(request)
    }

    override suspend fun sendEmailResetPassword(request: UserMessage.SendEmailResetPasswordRequest): UserMessage.SendEmailResetPasswordResponse {
        return emailRegistrationService.sendEmailResetPassword(request)
    }

    override suspend fun resetPassword(request: UserMessage.ResetPasswordRequest): UserMessage.ResetPasswordResponse {
        return emailRegistrationService.resetPassword(request)
    }

    override suspend fun checkPassword(request: UserMessage.CheckPasswordRequest): UserMessage.CheckPasswordResponse {
        return emailRegistrationService.checkPassword(request)
    }

    override suspend fun updateUserEmailReg(request: UserMessage.UpdateUserEmailRegRequest): UserMessage.UpdateUserEmailRegResponse {
        return emailRegistrationService.updateUserEmailReg(request)
    }

    override suspend fun getUserLoginInformation(request: UserMessage.GetUserLoginInformationRequest): UserMessage.GetUserLoginInformationResponse {
        return userProfileService.getUserLoginInformation(request)
    }

    override suspend fun updateLastLoginTime(request: UserMessage.UpdateLastLoginTimeRequest): UserMessage.UpdateLastLoginTimeResponse {
        return userProfileService.updateLastLoginTime(request)
    }

    /**
     * Get registration metadata associated with a given registration ID.
     *
     * @param request [UserMessage.RegistrationMetadataRequest] containing the registration ID to retrieve metadata for.
     * @return [UserMessage.RegistrationMetadataResponse] containing the retrieved metadata.
     */
    override suspend fun getRegistrationMetadata(request: UserMessage.RegistrationMetadataRequest): UserMessage.RegistrationMetadataResponse {
        var code = Code.INTERNAL
        var message = ""
        val builder = UserMessage.RegistrationMetadataResponse.newBuilder()

        try {
            val registration = userRegistrationGateway.findRegistrationById(request.registrationId).await()
            builder.setRegistrationId(registration.id)
            builder.setRegistrationType(registration.regType.toString())
            builder.setEmail(registration.email)
            builder.setIsVerified(registration.isRegVerified())

            code = Code.OK
        } catch (e: NoSuchElementException) {
            code = Code.NOT_FOUND
            message = "Registration ${request.registrationId} not found"
        } catch (t: Throwable) {
            message = t.message ?: "Error find registration ${request.registrationId}"
        }

        return builder.setStatus(Status.newBuilder().setCode(code.value()).setMessage(message)).build()
    }

    override suspend fun addMissingSocialEmail(request: UserMessage.AddMissingSocialEmailRequest): UserMessage.AddMissingSocialEmailResponse {
        return socialRegistrationService.addMissingSocialEmail(request)
    }

    override suspend fun updateProfile(request: UserMessage.UpdateProfileRequest): UserMessage.UpdateProfileResponse {
        return userProfileService.updateProfile(request)
    }

    override suspend fun updateAvatar(request: UserMessage.UpdateAvatarRequest): UserMessage.UpdateAvatarResponse {
        return userProfileService.updateAvatar(request)
    }

    override suspend fun unlinkSocial(request: UserMessage.UnlinkSocialRequest): UserMessage.UnlinkSocialResponse {
        return socialRegistrationService.unlinkSocial(request)
    }

    override suspend fun updateUserProfile(request: UserMessage.UpdateUserProfileRequest): UserMessage.UpdateUserProfileResponse {
        return userProfileService.updateUserProfile(request)
    }

    override suspend fun getEmailRegInfo(request: UserMessage.EmailRegInfoRequest): UserMessage.EmailRegInfoResponse {
        return emailRegistrationService.getEmailRegInfo(request)
    }

    override suspend fun createOneTimeLogin(request: UserMessage.CreateOneTimeLoginRequest): UserMessage.CreateOneTimeLoginResponse {
        val builder = UserMessage.CreateOneTimeLoginResponse.newBuilder()
        try {
            val key = oneTimeLoginService.generateLoginDataWithRegistrationId(request.regId)
            return builder.setStatus(Status.newBuilder().setCode(Code.OK.value())).setKey(key).build()
        } catch (t: Throwable) {
            return builder.setStatus(Status.newBuilder().setCode(Code.INTERNAL.value()).setMessage(t.message)).build()
        }
    }

    override suspend fun verifyAndConsumeToken(request: UserMessage.VerifyAndConsumeTokenRequest): UserMessage.VerifyAndConsumeTokenResponse {
        val builder = UserMessage.VerifyAndConsumeTokenResponse.newBuilder()
        try {
            val ok = oneTimeLoginService.verifyAndConsumeToken(request.token)
            if (!ok) {
                throw Exception("verify failed!")
            }
            return builder.setStatus(Status.newBuilder().setCode(Code.OK.value()))
                .setRegId(oneTimeLoginService.getRegistrationIdFromToken(request.token)).build()
        } catch (t: Throwable) {
            return builder.setStatus(Status.newBuilder().setCode(Code.INTERNAL.value()).setMessage(t.message)).build()
        }
    }

    /**
     * Saves a feedback submission to the database
     *
     * @param request The SaveFeedbackRequest containing the feedback data
     * @return A SaveFeedbackResponse indicating success or failure
     */
    override suspend fun saveFeedback(request: UserMessage.SaveFeedbackRequest): UserMessage.SaveFeedbackResponse {
        return feedbackService.saveFeedback(request)
    }
}

fun UserMessage.GetUserResponse.Builder.buildUser(registration: UserRegistrationPojo, profile: UserProfilePojo?) {
    this.setUser(UserMessage.UserProto.newBuilder().buildWithUserData(registration, profile))
}

/**
 * Util function to build a [UserMessage.GetUserProfileResponse] with the user profile information.
 *
 * @param profile The user profile data to be included in the response.
 */
fun UserMessage.GetUserProfileResponse.Builder.buildProfile(profile: UserProfilePojo) {
    val userProfile = UserMessage.UserProfileProto.newBuilder().setId(profile.id).setUsername(profile.username)
        .setEmail(profile.email).setPhone(profile.phone).setAvatarUrl(profile.avatarUrl).setName(profile.name)
        .setAddress(profile.address.toAddressProto()).setGender(UserMessage.Gender.valueOf(profile.gender.toString()))

    profile.dateOfBirth?.let {
        userProfile.setDateOfBirth(it.time)
    }

    this.userProfile = userProfile.build()
}

