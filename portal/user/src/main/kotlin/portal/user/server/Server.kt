package portal.user.server

import common.libs.logger.Logging
import io.grpc.Server
import io.grpc.ServerBuilder
import org.koin.core.annotation.Singleton
import portal.user.configuration.Configuration
import java.io.IOException


/**
 *
 * <AUTHOR>
 */
@Singleton
class Server(
    private val config: Configuration,
    userService: UserService
) : Logging {
    private val server: Server = ServerBuilder.forPort(config.serverConf.port)
        .addService(userService)
        .build()

    @Throws(IOException::class)
    fun start() {
        logger.debug("SERVER STARTING")
        server.start()
        logger.info("Server started, listening on {}", config.serverConf.port)
    }

    /**
     * Await termination on the main thread since the grpc library uses daemon threads.
     */
    @Throws(InterruptedException::class)
    fun blockUntilShutdown() {
        server.awaitTermination()
    }

    /**
     * Initiates an orderly shutdown in which preexisting calls continue but new calls are rejected.
     * After this call returns, this server has released the listening socket(s) and may be reused by
     * another server.
     *
     * <p>Note that this method will not wait for preexisting calls to finish before returning.
     * {@link #awaitTermination()} or {@link #awaitTermination(long, TimeUnit)} needs to be called to
     * wait for existing calls to finish.
     */
    fun shutdown() {
        server.shutdown()
        logger.info("Shutdown Server stopped")
    }

    /**
     * Initiates a forceful shutdown in which preexisting and new calls are rejected. Although
     * forceful, the shutdown process is still not instantaneous; {@link #isTerminated()} will likely
     * return {@code false} immediately after this method returns. After this call returns, this
     * server has released the listening socket(s) and may be reused by another server.
     */
    fun shutdownNow() {
        server.shutdownNow()
        logger.info("ShutdownNow Server stopped")
    }
}
