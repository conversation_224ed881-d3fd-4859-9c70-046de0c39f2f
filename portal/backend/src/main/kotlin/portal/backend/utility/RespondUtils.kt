package portal.backend.utility

import io.ktor.http.*
import io.ktor.server.application.*
import io.ktor.server.response.*


suspend fun ApplicationCall.respondWithErrorStatus(status: com.google.rpc.Status, httpCode: HttpStatusCode = HttpStatusCode.InternalServerError) {
    return this.respond(
        httpCode, mapOf(
            "message" to status.message,
            "backendErrorCode" to status.code
        )
    )
}

suspend fun ApplicationCall.respondWithException(e: Exception, httpCode: HttpStatusCode = HttpStatusCode.InternalServerError) {
    return this.respond(httpCode, message = e.message ?: "Unknown error")
}

suspend fun ApplicationCall.respondWithRedirect(url: String, message: String = "") {
    // use 302 Found as Google, Microsoft, FB all use the same (https://stackoverflow.com/a/72395961/13233459)
    return this.respond(
        HttpStatusCode.Found, mapOf(
            "rURL" to url,
            "message" to message
        )
    )
}