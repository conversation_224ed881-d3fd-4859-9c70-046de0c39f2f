package portal.backend.models

import jayeson.lib.access.datastructure.UserData

class BasicUserData : UserData {
    private var id: String? = null
    private var username: String? = null
    private var password: String? = null
    private var emailVerified: Boolean? = null
    private var registrationId: String? = null

    constructor() : super() {
        username = null
    }

    constructor(id: String?, username: String?, password: String?, emailVerified: Boolean?, registrationId : String?) : super() {
        this.id = id
        this.username = username
        this.password = password
        this.emailVerified = emailVerified
        this.registrationId = registrationId
    }

    override fun hasNoData(): Boolean {
        return username == null
    }

    override fun getUsername(): String? {
        return username
    }

    override fun setUsername(username: String) {
        this.username = username
    }

    override fun getPassword(): String? {
        return password
    }

    override fun setPassword(password: String) {
        this.password = password
    }

    fun getEmailVerified(): Boolean? {
        return this.emailVerified;
    }

    fun getRegistrationId(): String? {
        return this.registrationId
    }

    fun getId(): String? {
        return this.id
    }
}
