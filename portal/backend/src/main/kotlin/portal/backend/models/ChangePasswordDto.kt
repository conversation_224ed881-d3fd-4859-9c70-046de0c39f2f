package portal.backend.models

import kotlinx.serialization.Serializable

@Serializable
data class ChangePasswordDto(
    val password: String,
    val newPassword: String,
)

@Serializable
data class CheckPassword(
    val email: String,
    val password: String,
)

@Serializable
data class UserProfile(
    val email: String,
    val username: String? = null,
    val phone: String? = null,
    val avatarUrl: String? = null,
)

@Serializable
data class UserEmailReg(
    val email: String,
    val username: String? = null,
    val phone: String? = null,
    val password: String? = null,
)



