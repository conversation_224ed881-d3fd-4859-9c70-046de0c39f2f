plugins {
    id "hnct.build"
    id "kotlin"
    id "application"
    id "com.google.devtools.ksp"
    id "kotlinx-serialization"
}

group = 'viclass'
version = '1.0.0'

application {
    mainClass.set("portal.shorturl.Launcher")
}

tasks.withType(AbstractCopyTask) {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}

sourceSets {
    main {
        resources {
            srcDirs += [files("conf")]
        }
    }
}

dependencies {
    internal {
        implementation ":jayeson.lib.access", "jayeson:jayeson.lib.access:$accessVs", false
        implementation(["id": ":jayeson.lib.access", "src": ["ktor"]], "jayeson:jayeson.lib.access-ktor:$accessVs", false)
        implementation(["id": ":jayeson.lib.access", "src": "deprecated"], "jayeson:jayeson.lib.access-deprecated:$accessVs", false)
        implementation ":jayeson.lib.utility", "jayeson:jayeson.lib.utility:2.1.0", false

        implementation ":jayeson.lib.session", "jayeson:jayeson.lib.session:$sessionVs", false
        implementation(['id': ":jayeson.lib.session", "src": "lettuce"], "jayeson:jayeson.lib.session-lettuce:$sessionVs", false)
        implementation(['id': ":jayeson.lib.session", "src": "memory"], "jayeson:jayeson.lib.session-memory:$sessionVs", false)

        implementation([id: ':common.libs', src: ['logger']], "viclass:common.libs-logger:1.0.0", true)
        implementation([id: ':common.libs', src: ['jwt']], "viclass:common.libs-jwt:1.0.0", true)

        implementation([id: ':portal.user', src: ['pojo']], "viclass:portal.user-pojo:1.0.0", true)
        implementation([id: ':portal.grpc', src: ['shorturl']], "viclass:portal.grpc-shorturl:1.0.0", true)
    }

    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$coroutineVs"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-rx3:$coroutineVs"
    implementation "org.mongodb:mongodb-driver-reactivestreams:$mongoRxDriverVs"

    implementation "io.reactivex.rxjava3:rxkotlin:$rxKotlinVs"
    implementation "io.ktor:ktor-server-netty:$ktorVs"

    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin:2.13.1'

    implementation "com.google.api-client:google-api-client:$googleApiClientVs"

    // Koin
    ksp "io.insert-koin:koin-ksp-compiler:$koinKspVs"
    implementation "io.insert-koin:koin-annotations:$koinKspVs"
    implementation "io.insert-koin:koin-core:$koinVs"
    implementation "io.insert-koin:koin-ktor:$koinVs"
    implementation "io.insert-koin:koin-logger-slf4j:$koinVs"
    implementation "io.ktor:ktor-server-core-jvm"
    implementation("io.ktor:ktor-server-content-negotiation:$ktorVs")
    implementation("io.ktor:ktor-server-config-yaml:$ktorVs")
    implementation("io.ktor:ktor-server-jvm:${ktorVs}")
    implementation("io.ktor:ktor-server-freemarker:$ktorVs")
    implementation("io.ktor:ktor-server-cors:$ktorVs")
    // implementation "org.koin:koin-java:2.0.1"

    implementation("io.ktor:ktor-serialization-kotlinx-json:$ktorVs")
    implementation("io.ktor:ktor-serialization-jackson:$ktorVs")

    implementation "io.ktor:ktor-client-content-negotiation:$ktorVs"
    implementation("com.fasterxml.jackson.datatype:jackson-datatype-jsr310:${jacksonVs}")
    implementation "io.ktor:ktor-client-logging:$ktorVs"

    // redis
    implementation("io.lettuce:lettuce-core:$lettuceVs")

    // grpc dependencies
    implementation "com.google.protobuf:protobuf-java:$protobufVs"
    implementation "io.grpc:grpc-kotlin-stub:$grpcKotlinVs"
    implementation "io.grpc:grpc-stub:$grpcVs"
    implementation "io.grpc:grpc-protobuf:$grpcVs"
    implementation "io.grpc:grpc-netty-shaded:$grpcVs"
}