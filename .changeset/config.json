{"$schema": "https://unpkg.com/@changesets/config@2.3.0/schema.json", "changelog": ["@changesets/changelog-github", {"repo": "withastro/starlight"}], "commit": false, "linked": [], "access": "public", "baseBranch": "main", "updateInternalDependencies": "patch", "ignore": ["starlight-docs", "@example/*", "starlight-file-icons-generator", "@e2e/*"], "___experimentalUnsafeOptions_WILL_CHANGE_IN_PATCH": {"onlyUpdatePeerDependentsWhenOutOfRange": true}}