/** @type {import('tailwindcss').Config} */
const baseTail = require('../../../../tailwind.config');
const path = require('path');
const theme = baseTail.theme;

module.exports = {
    content: [path.join(__dirname, 'src/**/*.{html,js,ts,scss}')],
    theme: {
        ...theme,
        screens: {
            xs: '0px',
            sm: '568px',
            md: '1024px',
            lg: '1280px',
            xl: '1440px',
            '2xl': '2560px',
        },
    },
    plugins: [],
};
