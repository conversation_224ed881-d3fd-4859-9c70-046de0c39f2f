import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Inject,
    Injector,
    Input,
    OnDestroy,
    OnInit,
    Output,
    QueryList,
    ViewChildren,
    ViewContainerRef,
} from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder } from '@angular/forms';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RouterModule } from '@angular/router';
import { BaseCoordinator } from '@viclass/editor.coordinator/common';
import { ViewportManager } from '@viclass/editor.core';
import { BehaviorSubject, firstValueFrom, map, Observable, Subscription } from 'rxjs';
import { ButtonClickWaitDirective } from '../../button-click-wait-directive/click-wait.directive';
import { ENVIRONMENT } from '../../environment';
import { FormUtilModule } from '../../formutils';
import { NotificationModule, NotificationService } from '../../notification';
import { DocInProfileMetadataService, ShortUrlService, UserService } from '../../services';
import { SharingFooterSocialComponent } from '../../sharing-dialog/sharing-dialog.component';
import { SharingDialogConfig } from '../model';

@Component({
    selector: 'app-sharing',
    standalone: true,
    imports: [
        CommonModule,
        FormUtilModule,
        ReactiveFormsModule,
        NotificationModule,
        RouterModule,
        ButtonClickWaitDirective,
        MatSlideToggleModule,
        FormsModule,
        SharingFooterSocialComponent,
    ],
    templateUrl: './sharing.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SharingComponent implements OnInit, OnDestroy, AfterViewInit {
    @Input() previewBlob: Blob | null = null;
    @Input() inputData: SharingDialogConfig;
    @Output() redirectSaveDocument = new EventEmitter();

    shareLink: string;
    isShortening = false;

    isShared$: Observable<boolean>;
    isSaved$: Observable<boolean>;
    readonly isPreviewing$ = new BehaviorSubject<boolean>(false);

    readonly previewDocLoaded$ = new BehaviorSubject<boolean>(false);
    readonly processingCopy$ = new BehaviorSubject<boolean>(false);
    readonly processingPdf$ = new BehaviorSubject<boolean>(false);

    @ViewChildren('previewArea', { read: ViewContainerRef })
    previewArea: QueryList<ViewContainerRef>;
    private previewAreaSubscription: Subscription;

    private coord?: BaseCoordinator;
    private viewport?: ViewportManager;

    /**
     * We need the coord and viewport to RENDERED in the page AT THE TIME we create preview image/print PDF.
     * If there is initially no coord and viewport then we need the preview page to be rendered
     */
    protected needPreviewViewport: boolean = false;

    get docGlobalId() {
        return this.inputData.docGlobalId;
    }

    constructor(
        public readonly userService: UserService,
        public readonly fb: UntypedFormBuilder,
        private readonly metadataService: DocInProfileMetadataService,
        private readonly notificationService: NotificationService,
        private readonly shortUrlService: ShortUrlService,
        private readonly injector: Injector,
        private readonly cdr: ChangeDetectorRef,
        @Inject(ENVIRONMENT) private env: any
    ) {}

    async ngOnInit() {
        this.shareLink = `https://${this.env.domain}/user/doc/${this.inputData.edType}/${this.docGlobalId}`;
        this.isSaved$ = this.inputData.docInfo?.pipe(map(info => !!info?.details?.savedToProfile));
        this.isShared$ = this.inputData.docInfo?.pipe(map(info => !!info?.details?.shared));
        if (this.inputData.docDisplay) {
            this.coord = this.inputData.docDisplay.coord;
            this.viewport = this.inputData.docDisplay.viewport;
        }
        this.needPreviewViewport = !this.coord || !this.viewport;
        this.isShortening = true;
        try {
            this.shareLink = await firstValueFrom(
                this.shortUrlService
                    .shortenUrl(`/user/doc/${this.inputData.edType}/${this.docGlobalId}`)
                    .pipe(map(shortUrl => (shortUrl ? shortUrl['short_url'] : this.shareLink)))
            );
            this.cdr.markForCheck();
        } finally {
            this.isShortening = false;
            this.cdr.markForCheck();
        }
    }

    ngAfterViewInit(): void {
        this.previewArea.changes.subscribe(list => {
            this.previewArea.map(async (vref, index, containers) => {
                this.previewDocLoaded$.next(false);

                await this.createPreviewInArea(vref);
                this.previewDocLoaded$.next(true);
            });
        });
    }

    ngOnDestroy(): void {
        if (this.previewAreaSubscription) this.previewAreaSubscription.unsubscribe();
    }

    async onToggleChange(event: MatSlideToggleChange) {
        const docInfo = await this.metadataService.toggleSharing(this.inputData.edType, this.docGlobalId);
        this.inputData.docInfo.next(docInfo);
    }

    async _copyImageToClipboard(blob) {
        // Kiểm tra quyền ghi clipboard
        const permission = await navigator.permissions.query({ name: 'clipboard-write' as any });
        if (permission.state === 'granted' || permission.state === 'prompt') {
            const item = new ClipboardItem({ 'image/png': blob });
            await navigator.clipboard.write([item]);
        } else {
            throw new Error(
                'Clipboard write permission denied. Please allow clipboard access in your browser settings.'
            );
        }
    }

    async shareImage() {
        if (this.processingCopy$.value) return;

        if (this.needPreviewViewport && !this.isPreviewing$.value) {
            this.isPreviewing$.next(true);
            return;
        }

        this.processingCopy$.next(true);
        let previewBlob = this.previewBlob;

        try {
            if (!previewBlob) {
                switch (this.inputData.captureType) {
                    case 'document':
                        previewBlob = await this.coord.captureDocumentPreview(
                            this.inputData.edType,
                            this.viewport.id,
                            this.inputData.docGlobalId
                        );
                        break;
                    case 'viewport':
                        previewBlob = await this.coord.captureViewportPreview(this.viewport.id);
                        break;
                }
            }

            await this._copyImageToClipboard(previewBlob);
            this.notificationService.showNotification({
                message: 'Sao chép hình ảnh thành công',
                status: 'success',
            });
        } catch (error: any) {
            console.error('Failed to copy image to clipboard:', error);
            const isLoseFocusError = error?.name === 'NotAllowedError' && error?.message?.includes('focus');
            const isPermissionError = error?.message?.includes('permission denied');
            const msg = isLoseFocusError
                ? 'Vui lòng giữ tab đang mở để thực hiện thao tác này.'
                : isPermissionError
                  ? 'Vui lòng cấp quyền truy cập clipboard cho trình duyệt.'
                  : 'Sao chép thất bại, xin vui lòng thử lại';
            this.notificationService.showNotification({
                message: msg,
                status: 'error',
            });
        } finally {
            this.processingCopy$.next(false);
        }
    }

    async printPDF() {
        if (this.needPreviewViewport && !this.isPreviewing$.value) {
            this.isPreviewing$.next(true);
            return;
        }

        if (!this.docGlobalId) await this.coord.printPDF(this.viewport.id);
        else await this.coord.printDocPDF(this.inputData.edType, this.viewport.id, this.docGlobalId);
    }

    async copyToClipboard(text: string) {
        if (this.isShortening) return;
        try {
            await navigator.clipboard.writeText(text);
            this.notificationService.showNotification({
                message: 'Sao chép liên kết thành công',
                status: 'success',
            });
        } catch (error) {
            console.error('Async: Could not copy text: ', error);
        }
    }

    /**
     *
     * Create the preview in the preview area through calling back
     */
    private async createPreviewInArea(vref: ViewContainerRef) {
        if (this.inputData.previewCb) {
            const { viewport, coord } = await this.inputData.previewCb(this.inputData, vref, this.injector);
            this.viewport = viewport;
            this.coord = coord;
        }
    }
}
