import { EditorType } from '@viclass/editor.core';

export type SourceType = 'doc-ownership' | 'classroom-doc';
export type DocType = 'doc-in-profile' | 'doc-share-with-me';

export interface SysConfig {
    subject: [{ _id: string; value: string }];
    grade: [{ _id: string; value: string }];
}

export interface UploadTokenRequest {
    allowFileTypes: string[];
    maxFileSize: number;
}

export interface UploadTokenResponse {
    uploadToken: string;
}

export interface UploadResponse {
    fileId: string;
    fileUrl: string;
}

export type LocalStorageDocIds = {
    [key in EditorType]: string;
};

export type DocumentInfoDetail = {
    docGlobalId: string;
    docLocalId: number;
    ownerUserId: string;
    ownerUserName: string;
    docName?: string;
    previewUrl?: string;
    savedToProfile?: boolean;
    savedDate?: number;
    shared?: boolean;
    embedded?: boolean;
    source: SourceType;
};
export type DocumentInfoResponse = {
    docGlobalId: string;
    editorType: EditorType;
    docType: DocType;
    details: DocumentInfoDetail;
};

export type CheckPublicDocResponse = {
    isPublic: boolean;
    validEditorType: boolean;
};

export type LoadSavedDocsOptions = {
    editorType?: EditorType | '';
    textSearch?: string;
    page?: number;
    size?: number;
    startDate?: Date;
    endDate?: Date;
};

export type LoadSavedDocsResponse = {
    documents: DocumentInfoResponse[];
    totalCount: number;
    page: number;
    pageSize: number;
    hasPrevious: boolean;
    hasNext: boolean;
};
