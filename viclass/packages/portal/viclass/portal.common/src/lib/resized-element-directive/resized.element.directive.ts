import { Directive, ElementRef, EventEmitter, Input, OnInit, Output } from '@angular/core';

export type ListenResizeType = 'width' | 'height' | 'both';

/**
 * Element resize listener
 */
@Directive({
    standalone: true,
    selector: '[resized]',
})
export class ResizedElementDirective implements OnInit {
    @Output() resized: EventEmitter<{
        source: HTMLElement;
        width: number;
        height: number;
    }>;
    @Output() resizeWidth: EventEmitter<{
        source: HTMLElement;
        width: number;
    }>;
    @Output() resizedHeight: EventEmitter<{
        source: HTMLElement;
        height: number;
    }>;

    @Input() widthE: HTMLElement;
    @Input() heightE: HTMLElement;
    @Input() listenResized: ListenResizeType;

    @Output() resizedWidthE: EventEmitter<{
        source: HTMLElement;
        target: HTMLElement;
        width: number;
    }>;
    @Output() resizedHeightE: EventEmitter<{
        source: HTMLElement;
        target: HTMLElement;
        height: number;
    }>;

    private;

    constructor(private _elRef: ElementRef<HTMLElement>) {
        this.resized = new EventEmitter();
        this.resizeWidth = new EventEmitter();
        this.resizedHeight = new EventEmitter();
        this.resizedWidthE = new EventEmitter();
        this.resizedHeightE = new EventEmitter();
    }

    ngOnInit(): void {
        const resizedObserver = new ResizeObserver(entries => {
            for (let i = 0; i < entries.length; ++i) {
                const entry = entries[i];
                const sizes = entry.borderBoxSize; // or:  entry.contentBoxSize;
                if (sizes) {
                    for (let j = 0; j < sizes.length; ++j) {
                        var size = sizes[j];
                        this.resized.emit({
                            source: this._elRef.nativeElement,
                            width: size.inlineSize,
                            height: size.blockSize,
                        });
                        this.resizeWidth.emit({
                            source: this._elRef.nativeElement,
                            width: size.inlineSize,
                        });
                        this.resizedHeight.emit({
                            source: this._elRef.nativeElement,
                            height: size.blockSize,
                        });
                    }
                }
            }
        });

        const resizeWObserver = new ResizeObserver(entries => {
            for (let i = 0; i < entries.length; ++i) {
                const entry = entries[i];
                const sizes = entry.borderBoxSize; // or:  entry.contentBoxSize;
                if (sizes) {
                    for (let j = 0; j < sizes.length; ++j) {
                        const size = sizes[j];
                        if (['width', 'all'].includes(this.listenResized))
                            this.resizedWidthE.emit({
                                source: this._elRef.nativeElement,
                                target: this.widthE,
                                width: size.inlineSize,
                            });
                        else this._elRef.nativeElement.style.width = size.inlineSize + 'px';
                    }
                }
            }
        });

        const resizeHObserver = new ResizeObserver(entries => {
            for (let i = 0; i < entries.length; ++i) {
                const entry = entries[i];
                const sizes = entry.borderBoxSize; // or:  entry.contentBoxSize;
                if (sizes) {
                    for (let j = 0; j < sizes.length; ++j) {
                        const size = sizes[j];
                        if (['height', 'all'].includes(this.listenResized))
                            this.resizedHeightE.emit({
                                source: this._elRef.nativeElement,
                                target: this.heightE,
                                height: size.blockSize,
                            });
                        else this._elRef.nativeElement.style.height = size.blockSize + 'px';
                    }
                }
            }
        });

        resizedObserver.observe(this._elRef.nativeElement);
        if (this.widthE) resizeWObserver.observe(this.widthE);
        if (this.heightE) resizeHObserver.observe(this.heightE);
    }
}
