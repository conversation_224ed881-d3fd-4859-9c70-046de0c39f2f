import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { EditorType } from '@viclass/editor.core';
import { Observable, firstValueFrom, map, take } from 'rxjs';
import { createRPC } from '../services/api.service.module';
import { DocOwnershipMetadataController } from '../services/route/backend.route.definition';
import { DocumentInfoResponse } from '../services/models';

@Injectable()
export class DocumentDocMetadataService {
    private readonly server = new DocOwnershipMetadataController();

    constructor(private http: HttpClient) {}

    initialize() {}

    public loadSharedDocumentInfo(editorType: EditorType, globalId: string): Observable<DocumentInfoResponse | null> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };
        return createRPC<DocumentInfoResponse | null>(this.server.loadSharedDocInfo)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    public loadSharedDocumentInfoGuest(
        editorType: EditorType,
        globalId: string
    ): Observable<DocumentInfoResponse | null> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };
        return createRPC<DocumentInfoResponse | null>(this.server.loadSharedDocInfoGuest)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    /**
     *  Get latest doc created by current logged-in user with the provided editor type
     */
    public getDocsByCurrentUser(editorType: EditorType, limit: number = 1): Observable<DocumentInfoResponse[]> {
        const reqBody = {
            editorType: editorType,
            limit: limit,
        };

        return createRPC<DocumentInfoResponse[]>(this.server.docsByUser)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    public loadDocumentInfo(editorType: EditorType, globalId: string): Observable<DocumentInfoResponse | null> {
        const reqBody = {
            docGlobalId: globalId,
            editorType: editorType,
        };

        return createRPC<DocumentInfoResponse | null>(this.server.loadDocInfo)(this.http, {
            body: JSON.stringify(reqBody),
        }).pipe(
            take(1),
            map(res => res.body)
        );
    }

    public async isDocBelongToUser(edType: EditorType, docId: string, userId: string): Promise<boolean> {
        const docInfo = await firstValueFrom(this.loadDocumentInfo(edType, docId));
        return docInfo?.details?.ownerUserId == userId;
    }
}
