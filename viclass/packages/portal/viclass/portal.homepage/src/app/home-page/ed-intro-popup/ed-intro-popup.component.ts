import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { VpCmd } from '../../viewport-manager/viewport-manager.component';
import { CommonModule } from '@angular/common';
import { EditorType } from '@viclass/editor.core';

/**
 * TODO: NEED TO SHOW DIFFERENT INFORMATION FOR DIFFERENT EDITOR TYPE
 */
@Component({
    standalone: true,
    selector: 'ed-intro-popup',
    templateUrl: './ed-intro-popup.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule],
})
export class EdIntroPopupComponent {
    @Input() edType: EditorType;
    @Output() whenClose: EventEmitter<VpCmd>;

    constructor() {
        this.whenClose = new EventEmitter<VpCmd>();
    }

    close() {
        this.whenClose.emit();
    }

    get supportLink(): string {
        switch (this.edType) {
            case 'GeometryEditor':
                return '/support/02-toolbox/02-geo/01-geo-intro/';
            case 'FreeDrawingEditor':
                return '/support/02-toolbox/03-freedrawing/01-fred-intro/';
            case 'WordEditor':
                return '/support/02-toolbox/01-word/01-word-intro/';
            default:
                return '#';
        }
    }
}
