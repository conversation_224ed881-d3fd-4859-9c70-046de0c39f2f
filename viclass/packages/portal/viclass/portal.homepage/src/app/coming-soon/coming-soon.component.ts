import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule, UntypedFormBuilder, UntypedFormGroup, Validators } from '@angular/forms';
import {
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    FormUtilModule,
    UserService,
    CommonModule as viCommon,
} from '@viclass/portal.common';
import { firstValueFrom } from 'rxjs';

@Component({
    standalone: true,
    selector: 'app-coming-soon',
    templateUrl: './coming-soon.component.html',
    styleUrls: ['./coming-soon.component.scss'],
    imports: [viCommon, CommonModule, FormsModule, FormUtilModule, ReactiveFormsModule],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ComingSoonComponent {
    loading = false;
    isError = false;
    isSuccess = false;
    isAlreadyRegistered = false;

    form: UntypedFormGroup;
    formError: ErrorModel;
    emailError: ErrorModel;

    constructor(
        private us: UserService,
        private fb: UntypedFormBuilder,
        private cdr: ChangeDetectorRef
    ) {}

    buildForm = (data?: any): FormBuildingResult => {
        data = data || {
            email: null,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                __fields__: {
                    email: [Validators.pattern(/^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/), Validators.required],
                },
            })
            .build();

        this.form = result.control as UntypedFormGroup;

        return result;
    };

    submitForm = async (data: FormFlowSubmitEvent) => {
        if (this.loading) return;

        this.loading = true;
        this.form.disable();

        this.isError = false;
        this.isSuccess = false;
        this.isAlreadyRegistered = false;

        this.cdr.markForCheck();

        try {
            const response = await firstValueFrom(this.us.userBetaRegister(data.data.email));
            this.isSuccess = true;

            if (response?.status != 0) this.isAlreadyRegistered = true;

            this.form.reset();
        } catch (e: any) {
            this.isError = true;
            console.error(e);
        } finally {
            this.loading = false;
            this.form.enable();
            this.cdr.markForCheck();
        }
    };
}
