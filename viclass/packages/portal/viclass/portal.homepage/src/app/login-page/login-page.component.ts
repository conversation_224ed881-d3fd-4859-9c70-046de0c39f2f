import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, OnDestroy, OnInit } from '@angular/core';
import { FormGroup, ReactiveFormsModule, UntypedFormBuilder, Validators } from '@angular/forms';
import { ActivatedRoute, RouterModule } from '@angular/router';
import {
    decodeReturnUrlFromParams,
    ErrorModel,
    FormBuildingResult,
    FormCreator,
    FormFlowSubmitEvent,
    FormUtilModule,
    LoginData,
    PKEY_RURL,
    ProcessingRequestManager,
    SpinnerLabelComponent,
    UserService,
    CommonModule as viCommon,
} from '@viclass/portal.common';
import { BehaviorSubject, firstValueFrom, Observable, take, tap } from 'rxjs';
import { environment } from '../../environments/environment';
import { AppStateService } from '../app.state.service';
import { MetaService } from '../meta.service';
import { SocialLoginButtonsComponent } from '../social-login-buttons/social-login-buttons.component';

@Component({
    standalone: true,
    templateUrl: './login-page.component.html',
    imports: [
        RouterModule,
        FormUtilModule,
        ReactiveFormsModule,
        CommonModule,
        viCommon,
        SpinnerLabelComponent,
        SocialLoginButtonsComponent,
    ],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LoginPageComponent implements OnDestroy, OnInit {
    _rURL: string;

    usernameError: ErrorModel;
    passwordError: ErrorModel;
    loginError$: BehaviorSubject<ErrorModel> = new BehaviorSubject(null);

    form: FormGroup;

    constructor(
        public appState: AppStateService,
        private fb: UntypedFormBuilder,
        public userService: UserService,
        private route: ActivatedRoute,
        private prm: ProcessingRequestManager,
        private ms: MetaService
    ) {}

    /**
     * Init the page and handle redirect if user is already login
     */
    async ngOnInit() {
        this.ms.initMetaPage();
        this.ms.setTitle('Viclass - Đăng nhập');
        this.ms.setDescription('Đăng nhập vào Viclass để học và chia sẻ kiến thức với cộng đồng.');

        const params = await firstValueFrom(this.route.queryParams);

        this._rURL = params?.[PKEY_RURL] ? params[PKEY_RURL] : '';
        this.userService.curUser$
            .pipe(
                take(1),
                tap(user => {
                    if (user) window.location.href = decodeReturnUrlFromParams(params)?.rURL || '/';
                })
            )
            .subscribe();
    }

    /**
     * Build login form
     */
    buildForm = (data?: LoginData): FormBuildingResult => {
        data = data || {
            username: null,
            password: null,
        };

        const result = new FormCreator(this.fb, data)
            .validators({
                username: [Validators.required],
                password: [Validators.required],
            })
            .validatorMessages({
                username: { required: 'Trường này bắt buộc' },
                password: { required: 'Trường này bắt buộc' },
            })
            .build();

        this.form = result.control as FormGroup;

        return result;
    };

    /**
     * Handle submit login and redirect user to original page when success
     */
    submitLogin = async (data: FormFlowSubmitEvent) => {
        try {
            await firstValueFrom(this.prm.monitor('loggingIn', this.userService.doLogin(data.data).pipe(take(1))));

            var params = await firstValueFrom(this.route.queryParams);

            if (params && params[PKEY_RURL]) {
                window.location.href = JSON.parse(window.atob(decodeURIComponent(params[PKEY_RURL]))).rURL;
            } else {
                window.location.href = environment.authflowConfig.defaultReturnUrl;
            }
        } catch (err) {
            if (err.status == 401) {
                this.loginError$.next({
                    key: 'loginFailed',
                    msg: 'Có lỗi đăng nhập. Hãy kiểm tra lại thông tin bạn đã nhập vào! ',
                });
                firstValueFrom(this.form.valueChanges).then(() => {
                    this.loginError$.next(null);
                });
            }

            if (err.status == 302) {
                window.location.href = err.error.rURL;

                firstValueFrom(this.form.valueChanges).then(() => {
                    this.loginError$.next(null);
                });
            }

            return;
        }
    };

    get loggingIn$(): Observable<boolean> {
        return this.prm.getInprogressObs('loggingIn');
    }

    ngOnDestroy(): void {
        this.prm.clearIndividual('loggingIn');
    }

    /**
     * Show error message when social login fail
     */
    onSocialLoginError(error: ErrorModel) {
        this.loginError$.next(error);

        firstValueFrom(this.form.valueChanges).then(() => {
            this.loginError$.next(null);
        });
    }
}
