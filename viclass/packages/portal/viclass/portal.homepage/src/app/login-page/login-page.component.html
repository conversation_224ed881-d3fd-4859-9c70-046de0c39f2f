<section class="bg-BW7 flex flex-row items-stretch h-full">
    <div class="max-sm:hidden w-[50%] lg:w-[60%] bg-gradient-to-b from-P3/30 to-P3 relative">
        <object
            class="max-w-full max-h-[100%] absolute mx-auto left-0 right-0 my-auto top-0 bottom-0"
            type="image/svg+xml"
            data="assets/img/login-animation.svg">
            <img src="assets/img/login-animation.svg" />
        </object>
    </div>
    <div class="grow flex items-center py-[30px]">
        <div class="md:w-[370px] w-[290px] mx-auto">
            <div class="text-[36px]"><strong>Đăng nhập</strong></div>
            <ng-template [ngIf]="userService.curUser$ | async" [ngIfElse]="hasNoUser">
                <div class="mt-15">
                    <span class="vi-text-error block">! Bạn đã đăng nhập! <PERSON><PERSON><PERSON> đăng xuất trước khi đăng nhập lại!</span>
                </div>
            </ng-template>
            <ng-template #hasNoUser>
                <div class="text-[14px] mt-[15px] mb-[30px]">
                    Bạn chưa có tài khoản?
                    <a class="text-P1" routerLink="/registration" [queryParams]="{ _rURL: _rURL || undefined }"
                        >Đăng ký</a
                    >
                </div>
                <form
                    class="vi-form"
                    *fflow="let fum; by: buildForm; fflow as f; submit: submitLogin; noNav: true"
                    [formGroup]="fum"
                    [ferrcoord]="f">
                    <div class="mb-[20px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_user prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon"
                                formControlName="username"
                                placeholder="Nhập tên đăng nhập / email"
                                [(ferror)]="usernameError" />
                        </div>
                        <span class="vi-text-error block" *ngIf="usernameError">! {{ usernameError.msg }}</span>
                    </div>
                    <div class="mb-[10px]">
                        <div class="vi-icon-input">
                            <i class="vcon-general vcon_general_password prepend-icon"></i>
                            <input
                                class="vi-input prepend-icon append-icon"
                                type="password"
                                #pwinput
                                formControlName="password"
                                placeholder="Nhập mật khẩu"
                                [(ferror)]="passwordError" />
                            <i
                                class="vcon-general vcon_general_preview_view append-icon cursor-pointer"
                                *ngIf="pwinput.type === 'password'"
                                (click)="pwinput.type = 'text'"></i>
                            <i
                                class="vcon-general vcon_general_preview_hide append-icon cursor-pointer"
                                *ngIf="pwinput.type === 'text'"
                                (click)="pwinput.type = 'password'"></i>
                        </div>
                        <span class="vi-text-error block" *ngIf="passwordError">! {{ passwordError.msg }}</span>
                        <div class="text-end">
                            <a class="text-P1 text-[14px]" href="/forgot-password">Quên mật khẩu</a>
                        </div>
                    </div>
                    <span class="vi-text-error block mt-3" *ngIf="loginError$ | async"
                        >! {{ (loginError$ | async).msg }}</span
                    >
                    <button
                        class="mt-[30px] vi-btn vi-btn-normal vi-btn-focus w-full"
                        [disabled]="!f.canSubmit() || (loggingIn$ | async)"
                        [form-flow-submit]="f"
                        [spinner]="loggingIn$">
                        Đăng nhập
                    </button>
                </form>
                <div class="flex mt-[50px] items-center">
                    <hr class="grow opacity-100 border-BW1" />
                    <div class="text-[14px] grow-0 mx-3">hoặc đăng nhập với</div>
                    <hr class="grow opacity-100 border-BW1" />
                </div>
                <app-social-login-buttons (loginError)="onSocialLoginError($event)"></app-social-login-buttons>
            </ng-template>
        </div>
    </div>
</section>
