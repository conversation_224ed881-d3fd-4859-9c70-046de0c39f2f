import { Injectable } from '@angular/core';
import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { Router } from '@angular/router';
import { ClassroomCriticalError, ClassroomNonCriticalError } from '@viclass/editor.coordinator/classroom';

@Injectable()
export class HttpErrorInterceptor implements HttpInterceptor {
    constructor(private router: Router) {}

    intercept(request: HttpRequest<any>, next: <PERSON>ttp<PERSON>and<PERSON>): Observable<HttpEvent<any>> {
        return next.handle(request).pipe(
            catchError((error: HttpErrorResponse) => {
                console.error('HTTP Error Interceptor:', error); // Log the full error
                // --- Server responded with an error status code (non-2xx) ---
                const status = error.status;
                let data: any = error.error; // Use 'any' for flexibility or define a type
                if (typeof data === 'string') {
                    // Check if the error body is a string
                    try {
                        // Attempt to parse it as JSON
                        // If successful, 'data' will become the parsed object (or primitive if the JSON string represented one)
                        data = JSON.parse(data);
                    } catch (e) {
                        // If parsing fails, 'data' remains the original string.
                        // This is useful if the server sends a plain text error message.
                        console.warn('Error body is a string but not valid JSON, using as plain text:', data);
                    }
                }
                const errorCode = data?.errorCode || data?.code; // Look for common error code fields

                switch (status) {
                    case 400: // Bad Request
                        console.error(`Invalid request to ${request.url}. Please check input.`, 'error');
                        // Potentially return a specific error type based on errorCode
                        throw new ClassroomNonCriticalError(
                            { type: 'BAD_REQUEST', data: true },
                            'Lỗi dữ liệu không hợp lệ!',
                            error
                        );
                    case 401: // Unauthorized
                        console.error('Unauthorized access (401). Redirecting to login...');
                        // throw new ClassroomCriticalError(
                        //     { type: 'PEER_KICKED_OUT', data: true },
                        //     'Vui lòng đăng nhập lại tài khoản.',
                        //     error
                        // );
                        break;
                    case 403: // Forbidden
                        console.error('Forbidden access (403).');
                        // Handle specific backend error codes for 403
                        if (errorCode === 'CLASS_NOT_OPENED') {
                            throw new ClassroomCriticalError(
                                { type: 'CLASS_NOT_OPENED', data: true },
                                'Lớp học chưa được mở',
                                error
                            );
                        }
                        if (errorCode === 'CLASS_ENDED') {
                            throw new ClassroomCriticalError(
                                { type: 'CLASS_ENDED', data: true },
                                'Lớp học đã kết thúc',
                                error
                            );
                        }
                        if (errorCode === 'PEER_KICKED_OUT') {
                            throw new ClassroomCriticalError(
                                { type: 'PEER_KICKED_OUT', data: true },
                                'Người dùng đã rời khỏi lớp học',
                                error
                            );
                        }
                        if (errorCode === 'PEER_INVALID') {
                            // This might indicate the peer ID is no longer valid on the server
                            throw new ClassroomCriticalError(
                                { type: 'CONNECTION_ERROR', data: true },
                                'Lỗi kết nối, vui lòng tải lại trang.',
                                error
                            );
                        }
                        if (errorCode === 'OFFLINE_USER') {
                            throw new ClassroomCriticalError(
                                { type: 'OFFLINE_USER', data: true },
                                'Học viên đã rời khỏi lớp',
                                error
                            );
                        }
                        throw new ClassroomNonCriticalError(
                            { type: 'UNCLASSIFIED', data: true },
                            'Bạn không có quyền thực hiện hành động này.',
                            error
                        );

                    case 404: // Not Found
                        const contentType = error.headers.get('Content-Type') || '';
                        const body = error.error;
                        if (contentType.includes('text/plain') && typeof body === 'string') {
                            console.log('404 do backend chủ động trả về:', body);
                            throw new ClassroomNonCriticalError(
                                { type: 'RESOURCE_NOTFOUND', data: true },
                                'Không tìm thấy tài nguyên.',
                                error
                            );
                        } else {
                            console.log('404 do route không tồn tại');
                            throw new ClassroomNonCriticalError(
                                { type: 'RESOURCE_NOTFOUND', data: true },
                                'Đường dẫn không tồn tại!.',
                                error
                            );
                        }
                    case 412: // Precondition Failed
                        console.error('Precondition failed (412).');
                        throw new ClassroomCriticalError(
                            { type: 'UNCLASSIFIED', data: true },
                            'Có lỗi đồng bộ dữ liệu, vui lòng tải lại trang!.',
                            error
                        );

                    case 500: // Internal Server Error
                        console.error(`Server error`, error);
                        throw new ClassroomNonCriticalError(
                            { type: 'UNCLASSIFIED', data: true },
                            'Lỗi hệ thống. Vui lòng tải lại trang.',
                            error
                        );
                    case 502: // Bad Gateway
                    case 503: // Gateway Timeout
                    case 504: // Service Unavailable
                        console.error(`Server error (${error.status}).`);
                        throw new ClassroomNonCriticalError(
                            { type: 'CONNECTION_ERROR', data: true },
                            'Lỗi hệ thống. Vui lòng tải lại trang.',
                            error
                        );

                    // Add more cases as needed for other specific error codes

                    default:
                        // For unhandled server errors or client-side errors that manifest as HttpErrorResponse
                        if (error.status !== 0) {
                            // Status 0 often means network error/CORS/request cancelled
                            throw new ClassroomNonCriticalError(
                                { type: 'CONNECTION_ERROR', data: true },
                                'Lỗi mạng',
                                error
                            );
                        } else {
                            console.error('Network error or request cancelled (status 0).');
                            throw new ClassroomNonCriticalError(
                                { type: 'CONNECTION_ERROR', data: true },
                                'Lỗi kết nối, vui lòng tải lại trang.',
                                error
                            );
                        }
                }

                // IMPORTANT: Re-throw the error so other error handlers (like component-level catchError)
                // or the global ErrorHandler can potentially process it further.
                return throwError(() => error);
            })
        );
    }
}
