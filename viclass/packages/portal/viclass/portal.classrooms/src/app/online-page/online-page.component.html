<ng-template [ngIf]="(initializing$ | async) || !(coordInitialized$ | async)">
    <div class="z-[1000] fixed top-0 left-0 w-[100vw] h-[100vh] bg-BW2/70 flex items-center justify-center">
        <div>
            <img src="assets/img/loading-icon.svg" class="w-[200px]" alt="loading" />
            <div class="text-center text-BW7">
                <span class="text-[16px]">Loading...</span>
            </div>
        </div>
    </div>
</ng-template>

<ng-container *ngIf="onlStateS.confirmViewportSwitchMessagePopup$ | async as d">
    <div [confirm-viewport-switch-message-popup]="d.obs"></div>
</ng-container>

<ng-container *ngIf="onlStateS.confirmPresenterSwitchMessagePopup$ | async as d">
    <div [confirm-presenter-switch-message-popup]="d.obs"></div>
</ng-container>

<ng-container *ngIf="onlStateS.confirmDeletePagePopup$ | async as d">
    <div [confirm-delete-page-popup]="d.obs" [pageName]="d.pageName"></div>
</ng-container>

<ng-container *ngIf="onlStateS.confirmKickOutMemberPopup$ | async as d">
    <div [confirm-kick-out-popup]="d.obs" [member]="d.member"></div>
</ng-container>

<ng-container *ngIf="classroomErrorHandlerListener.joinedInAnotherSession$ | async">
    <joined-in-another-present-popup></joined-in-another-present-popup>
</ng-container>

<ng-container *ngIf="classroomErrorHandlerListener.kickedOut$ | async">
    <kicked-out-popup></kicked-out-popup>
</ng-container>

<ng-container *ngIf="classroomErrorHandlerListener.offlineUser$ | async">
    <offline-user-popup></offline-user-popup>
</ng-container>

<ng-container *ngIf="classroomErrorHandlerListener.connectionError$ | async as err">
    <connection-error-popup [err]="err"></connection-error-popup>
</ng-container>

<ng-container *ngIf="onlStateS.confirmEndClassPopup$ | async as d">
    <div [confirm-end-class-popup]="d"></div>
</ng-container>

<ng-container *ngIf="onlStateS.confirmLeaveClassPopup$ | async as d">
    <div [confirm-out-class-popup]="d"></div>
</ng-container>

<ng-container *ngIf="classroomErrorHandlerListener.classEnded$ | async">
    <class-ended-popup></class-ended-popup>
</ng-container>

<ng-container *ngIf="classroomErrorHandlerListener.classNotStart$ | async">
    <class-notstart-popup></class-notstart-popup>
</ng-container>

<ng-container *ngIf="(onlStateS.isOwner$ | async) && classroomErrorHandlerListener.presenterOffline$ | async as d">
    <div [offline-owner-popup]="d.obs" [member]="d.member"></div>
</ng-container>

<ng-container *ngIf="!(onlStateS.isOwner$ | async)">
    <ng-container *ngIf="classroomErrorHandlerListener.ownerOffline$ | async as d">
        <div [offline-member-popup]="d.obs" [member]="d.member"></div>
    </ng-container>
    <ng-container *ngIf="classroomErrorHandlerListener.presenterOffline$ | async as d">
        <div [offline-member-popup]="d.obs" [member]="d.member"></div>
    </ng-container>
</ng-container>

<ng-container *ngIf="onlStateS.presentationRequestPopup$ | async as d">
    <div [confirm-present-popup]="d"></div>
</ng-container>

<ng-container *ngIf="onlStateS.createQuickQuestionPopup$ | async as d">
    <div [create-quick-question-popup]="d"></div>
</ng-container>

<ng-container *ngIf="onlStateS.confirmQuickQuestionPopup$ | async as d">
    <div [confirm-join-quick-question-popup]="d.obs" [content]="d.content"></div>
</ng-container>

<div #clr class="h-full w-full classroom-screen">
    <ng-container *ngIf="activityStateS.quickQuestionActivity$ | async as a">
        <div
            [quick-question-content]="quickQuestion(a)"
            [boundaryClass]="'classroom-screen'"
            [initRightPadding]="sideBarRef?.nativeElement"></div>
    </ng-container>

    <!-- TOP BAR -->
    <div class="w-full p-[20px] z-[100] absolute v-toolbar-group top left ltr pointer-events-none">
        <div class="v-toolbar">
            <div class="v-tool-group">
                <a href="/" class="v-tool-btn">
                    <span class="vcon vcon-onl vcon_logo-icon text-P1"></span>
                </a>
            </div>
            <span class="v-toolbar-gutter"></span>
            <div class="v-tool-group">
                <button
                    *ngIf="checkOverflow(vpBar)"
                    class="v-tool-btn !w-[20px] !rounded-[0] !p-0 !m-0 !h-full !rounded-l-[10px] shadow-right"
                    (click)="scrollLeft(vpBar)">
                    <span class="vcon vcon-general vcon_arrow_back"></span>
                </button>

                <!-- List of tab for viewports -->
                <div
                    #vpBar
                    class="flex relative self-start top-0 overflow-hidden scroll-smooth gap-2 h-fit pointer-events-none"
                    (wheel)="scroll($event, vpBar)"
                    [ngClass]="{ 'pl-[5px]': !checkOverflow(vpBar) }">
                    <ng-template
                        ngFor
                        let-state
                        [ngForOf]="coordStateS.curStates$ | async"
                        [ngForTrackBy]="trackCoordStateById">
                        <div [id]="'board-tab-' + (state | async).id" [app-board-tab]="state"></div>
                    </ng-template>
                </div>

                <div *ngIf="checkOverflow(vpBar)" class="d-flex flex-row h-full">
                    <button
                        class="v-tool-btn !w-[20px] !rounded-[0] !p-0 !m-0 !h-full shadow-left"
                        (click)="scrollRight(vpBar)">
                        <span class="vcon vcon-general vcon_arrow_next"></span>
                    </button>
                    <div class="v-tool-separation"></div>
                    <button
                        #showVpMenuBtn
                        class="v-tool-btn"
                        [ngClass]="{ active: showViewportMenu$ | async }"
                        (click)="showViewportMenu$.next(!showViewportMenu$.value)">
                        <span class="vcon vcon-onl vcon_wb_menu"></span>
                    </button>
                    <div class="v-tool-separation"></div>
                    <div
                        *ngIf="showViewportMenu$ | async"
                        class="absolute top-[45px] right-[40px] bg-TP3 backdrop-blur-BL1 rounded-[10px] pt-[10px] pb-[10px] overflow-hidden shadow-SH1"
                        click-outside
                        (clickOutside)="onClickOutsideVpMenu($event, showVpMenuBtn)">
                        <div
                            class="flex flex-column max-w-[250px] items-baseline overflow-auto max-h-[700px]"
                            resized
                            [heightE]="clr"
                            [listenResized]="'height'"
                            (resizedHeightE)="changeVpMenuHeight($event)">
                            <table>
                                <ng-template
                                    ngFor
                                    let-state$
                                    [ngForOf]="coordStateS.curStates$ | async"
                                    [ngForTrackBy]="trackCoordStateById">
                                    <tr
                                        class="v-tool-label-btn h-[42px] click-indicator"
                                        *ngIf="state$ | async as state"
                                        [ngClass]="{
                                            'bg-P2': (coordStateS.selected$ | async) === state.id,
                                        }"
                                        (click)="switchViewport(state.id)">
                                        <td class="ps-[10px] pe-[5px]">
                                            <div class="">
                                                <span
                                                    *ngIf="state.default"
                                                    class="w-[16px] h-[16px] leading-[16px] text-[16px] justify-center vcon-onl vcon_whiteboard_default-tab"></span>
                                                <span
                                                    *ngIf="state.presenting"
                                                    class="w-[16px] h-[16px] leading-[16px] text-[16px] justify-center vcon-onl vcon_sidebar_action_play"></span>
                                                <span
                                                    *ngIf="state.pinned"
                                                    class="w-[16px] h-[16px] leading-[16px] text-[16px] justify-center vcon-onl vcon_whiteboard_pinned"></span>
                                            </div>
                                        </td>
                                        <td class="pe-[10px]">
                                            <span>{{ state.title }}</span>
                                        </td>
                                    </tr>
                                </ng-template>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Add new viewport -->
                <button
                    class="v-tool-btn"
                    [spinner]="addingBoard$"
                    [disabled]="addingBoard$ | async"
                    (click)="addNewBoard()">
                    <span #addBoardBtnEl class="vcon vcon-onl vcon_page-bar_ad">
                        <lib-tooltip [toolTipFor]="addBoardBtnEl" [tooltipContent]="'Thêm bảng mới'"></lib-tooltip>
                    </span>
                </button>
            </div>
        </div>
        <span class="v-toolbar-gutter"></span>
        <div #clrControllerBar class="v-toolbar ms-auto">
            <div class="v-tool-group">
                <ng-template [ngIf]="onlStateS.isOwner$ | async" [ngIfElse]="studentActions">
                    <!-- <ng-template
                        [ngIf]="activityStateS.quickQuestionActivity$ | async"
                        [ngIfElse]="createQuickQuestionBtn">
                        <div
                            class="hover-switch-icon"
                            (click)="stopQuickQuestion()">
                            <button
                                class="show-hover bg-P3 vi-btn vi-btn-small m-[5px] !ps-[5px] !pe-[5px] w-[30px] h-[30px]">
                                <i
                                    class="vcon vcon-onl vcon_question-for-all"></i>
                            </button>
                            <button
                                class="show-default bg-P2 vi-btn vi-btn-small m-[5px] !ps-[5px] !pe-[5px] w-[30px] h-[30px]">
                                <img
                                    class=""
                                    src="assets/img/question-for-all-active.svg"
                                    alt="" />
                            </button>
                        </div>
                    </ng-template>
                    <ng-template #createQuickQuestionBtn>
                        <div
                            class="hover-switch-icon"
                            (click)="newQuickQuestion()">
                            <button
                                class="show-default vi-btn vi-btn-small vi-btn-focus m-[5px] !ps-[5px] !pe-[5px] w-[30px] h-[30px]">
                                <i
                                    class="vcon vcon-onl vcon_question-for-all"></i>
                            </button>
                            <button
                                class="show-hover bg-P3 vi-btn vi-btn-small m-[5px] !ps-[5px] !pe-[5px] w-[30px] h-[30px]">
                                <img
                                    class=""
                                    src="assets/img/question-for-all-active.svg"
                                    alt="" />
                            </button>
                        </div>
                    </ng-template> -->
                    <!-- <button class="v-tool-btn">
                        <span class="vcon vcon-onl vcon_page-bar_record"></span>
                    </button> -->
                </ng-template>
                <ng-template #studentActions>
                    <div
                        *ngIf="classroomUserState$ | async as state"
                        [class.pointer-events-none]="state.loadingRaiseHandStatus"
                        [spinner]="of(state.loadingRaiseHandStatus)"
                        (click)="emitRaiseHandControlEvent()">
                        <div *ngIf="state.raiseHandStatus === 'NONE'" class="hover-switch-icon">
                            <button
                                class="show-default vi-btn vi-btn-small vi-btn-focus m-[5px] !ps-[5px] !pe-[5px] w-[40px] h-[30px]">
                                <i class="vcon vcon-onl vcon_sidebar_raise-hand"></i>
                            </button>
                            <button
                                #riseHandEl
                                class="show-hover vi-btn vi-btn-small bg-P3 m-[5px] !ps-[5px] !pe-[5px] w-[40px] h-[30px] justify-items-center">
                                <img src="assets/img/raising-hand-icon.svg" alt="" />
                            </button>
                            <lib-tooltip [toolTipFor]="riseHandEl" [tooltipContent]="'Giơ tay'"></lib-tooltip>
                        </div>
                        <div *ngIf="state.raiseHandStatus === 'RAISE_HAND'" class="hover-switch-icon">
                            <button
                                #riseHandEndEl
                                class="show-hover vi-btn vi-btn-small bg-P3 m-[5px] !ps-[5px] !pe-[5px] w-[40px] h-[30px]">
                                <i class="vcon vcon-onl vcon_user"></i>
                            </button>
                            <lib-tooltip [toolTipFor]="riseHandEndEl" [tooltipContent]="'Bỏ tay xuống'"></lib-tooltip>
                            <button
                                class="show-default vi-btn vi-btn-small bg-P2 m-[5px] !ps-[5px] !pe-[5px] w-[40px] h-[30px] justify-items-center">
                                <img src="assets/img/raising-hand-icon.svg" alt="" />
                            </button>
                        </div>
                        <div *ngIf="state.raiseHandStatus === 'PRESENTING'" class="hover-switch-icon">
                            <button
                                #stopPresentEl
                                class="show-hover vi-btn vi-btn-small bg-P3 m-[5px] !ps-[5px] !pe-[5px] w-[40px] h-[30px]">
                                <i class="vcon vcon-onl vcon_sidebar_action_stop"></i>
                            </button>
                            <lib-tooltip
                                [toolTipFor]="stopPresentEl"
                                [tooltipContent]="'Ngưng thuyết trình'"></lib-tooltip>
                            <button
                                class="show-default vi-btn vi-btn-small bg-P2 m-[5px] !ps-[5px] !pe-[5px] w-[40px] h-[30px] justify-items-center">
                                <img src="assets/img/play-icon.svg" alt="" />
                            </button>
                        </div>
                    </div>
                </ng-template>

                <ng-template [ngIf]="memberStateS.presentingMember$ | async" let-presentingMember="ngIf">
                    <button
                        #presentingUserAvatarBtnEl
                        class="v-tool-btn"
                        [ngClass]="{
                            '!cursor-default': !(canCurMemberStopPresentPresentingMember$ | async),
                        }"
                        (click)="curMemberStopPresentPresentingMember()">
                        <div
                            class="presenting-user-avatar avatar-img"
                            [ngStyle]="
                                presentingMember.profile.avatarUrl?.trim()?.length
                                    ? {
                                          'background-image': 'url(' + presentingMember.profile.avatarUrl + ')',
                                      }
                                    : {}
                            "></div>
                    </button>
                    <lib-tooltip
                        *ngIf="onlStateS.lsDetail$ | async as lsDetail"
                        [toolTipFor]="presentingUserAvatarBtnEl"
                        [tooltipContent]="
                            (canCurMemberStopPresentPresentingMember$ | async)
                                ? 'Dừng phát biểu ' + displayName(presentingMember)
                                : displayName(presentingMember) +
                                  (lsDetail.creatorId === presentingMember.profile.id
                                      ? ' đang giảng'
                                      : ' đang phát biểu')
                        "></lib-tooltip>
                </ng-template>

                <ng-template [ngIf]="!(conferenceS.callNotJoined | async)" [ngIfElse]="conferenceNotJoined">
                    <button
                        #micBtnEl
                        [class.active]="isMicActive | async"
                        class="v-tool-btn relative"
                        (click)="toggleMic()">
                        <button class="v-tool-btn" [disabled]="!(micGranted | async)">
                            <span
                                *ngIf="!(mediaTogglesInProgress.mic | async); else micLoading"
                                class="vcon vcon-onl"
                                [ngClass]="{
                                    vcon_mic_on: isMicActive | async,
                                    vcon_mic_off: !(isMicActive | async),
                                }"></span>
                        </button>

                        <span
                            class="absolute top-0 right-0 z-10 scale-50 origin-top-right"
                            *ngIf="!(micGranted | async)">
                            <span class="vcon vcon-general vcon_general_warning">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </span>
                        </span>

                        <ng-template #micLoading>
                            <span><button class="v-tool-btn" [spinner]="mediaTogglesInProgress.mic"></button></span>
                        </ng-template>
                    </button>
                    <lib-tooltip
                        [toolTipFor]="micBtnEl"
                        [tooltipContent]="
                            !(micGranted | async)
                                ? 'Cấp quyền cho mic'
                                : (isMicActive | async)
                                  ? 'Tắt Microphone'
                                  : 'Bật Microphone'
                        "></lib-tooltip>
                    <button
                        #videoBtnEl
                        [class.active]="isVideoActive | async"
                        class="v-tool-btn relative"
                        (click)="toggleVideo()">
                        <button class="v-tool-btn" [disabled]="!(cameraGranted | async)">
                            <span
                                *ngIf="!(this.mediaTogglesInProgress.camera | async); else camLoading"
                                class="vcon vcon-onl"
                                [ngClass]="{
                                    vcon_camera_on: isVideoActive | async,
                                    vcon_camera_off: !(isVideoActive | async),
                                }"></span>
                        </button>
                        <span
                            class="absolute top-0 right-0 z-10 scale-50 origin-top-right"
                            *ngIf="!(cameraGranted | async)">
                            <span class="vcon vcon-general vcon_general_warning">
                                <span class="path1"></span>
                                <span class="path2"></span>
                                <span class="path3"></span>
                            </span>
                        </span>

                        <ng-template #camLoading>
                            <span>
                                <button class="v-tool-btn" [spinner]="mediaTogglesInProgress.camera"></button>
                            </span>
                        </ng-template>
                    </button>
                    <lib-tooltip
                        [toolTipFor]="videoBtnEl"
                        [tooltipContent]="
                            !(cameraGranted | async)
                                ? 'Cấp quyền cho camera'
                                : (isVideoActive | async)
                                  ? 'Tắt Camera'
                                  : 'Bật Camera'
                        "></lib-tooltip>
                    <button
                        #shareScreenvideoBtnEl
                        [class.active]="isShareScreen$ | async"
                        class="v-tool-btn"
                        (click)="toggleShareScreen()">
                        <span
                            *ngIf="
                                !(this.mediaTogglesInProgress.screenShare | async) &&
                                    !(this.selectingScreenToShare | async);
                                else screenShareLoading
                            "
                            class="vcon vcon-onl vcon_share-screen"></span>
                        <ng-template #screenShareLoading>
                            <span *ngIf="mediaTogglesInProgress.screenShare | async"
                                ><button class="v-tool-btn" [spinner]="mediaTogglesInProgress.screenShare"></button
                            ></span>
                            <span *ngIf="this.selectingScreenToShare | async"
                                ><button class="v-tool-btn" [spinner]="this.selectingScreenToShare"></button
                            ></span>
                        </ng-template>
                        <lib-tooltip
                            [toolTipFor]="shareScreenvideoBtnEl"
                            [tooltipContent]="
                                (isShareScreen$ | async) ? 'Tắt chỉa sẻ màn hình' : 'Bật chia sẻ màn hình'
                            "></lib-tooltip>
                    </button>
                </ng-template>
                <ng-template #conferenceNotJoined>
                    <span #confJoining
                        ><button
                            class="v-tool-btn"
                            *ngIf="conferenceS.callNotJoined | async"
                            [spinner]="conferenceS.callNotJoined"></button
                    ></span>
                    <lib-tooltip [toolTipFor]="confJoining" [tooltipContent]="'Đang chờ mic / camera'"></lib-tooltip>
                </ng-template>

                <button
                    #markerBtnEl
                    [class.active]="markerS.isActiveMarkerTool$ | async"
                    class="v-tool-btn"
                    (click)="markerS.toggleActiveMarkerTool()">
                    <span class="vcon vcon-onl vcon_marker"></span>
                </button>

                <button
                    #settingBtnEl
                    [class.active]="(sidebarTab$ | async) === 'setting'"
                    class="v-tool-btn"
                    (click)="switchSideBarTab('setting')">
                    <span class="vcon vcon-onl vcon_sidebar-setting"></span>
                </button>
                <lib-tooltip [toolTipFor]="settingBtnEl" [tooltipContent]="'Cài đặt'"></lib-tooltip>
                <lib-tooltip [toolTipFor]="markerBtnEl" [tooltipContent]="'Marker Tool'"></lib-tooltip>
            </div>
            <span class="v-toolbar-gutter"></span>
            <div class="v-tool-group">
                <button
                    #studentBtnEl
                    [class.active]="members$ | async"
                    class="v-tool-btn"
                    (click)="switchSideBarTab('confirmed')">
                    <span class="w-[20px] h-[20px]" [badge]="newNotifyMember$">
                        <span class="vcon vcon-onl vcon_user_student"></span>
                    </span>
                </button>
                <lib-tooltip [toolTipFor]="studentBtnEl" [tooltipContent]="'Học viên'"></lib-tooltip>

                <button
                    #documentBtnEl
                    [class.active]="(sidebarTab$ | async) === 'document'"
                    class="v-tool-btn"
                    (click)="switchSideBarTab('document')">
                    <span class="vcon vcon-onl vcon_sidebar-document"></span>
                </button>
                <lib-tooltip [toolTipFor]="documentBtnEl" [tooltipContent]="'Tài liệu'"></lib-tooltip>

                <button #shareBtnEl class="v-tool-btn" (click)="shareClassroom()">
                    <span class="vcon vcon-onl vcon_page-bar_share"></span>
                </button>
                <lib-tooltip [toolTipFor]="shareBtnEl" [tooltipContent]="'Chia sẻ'"></lib-tooltip>

                <!-- <button
                    #notificationBtnEl
                    [class.active]="(sidebarTab$ | async) === 'notification'"
                    class="v-tool-btn"
                    (click)="switchSideBarTab('notification')">
                    <span class="w-[20px] h-[20px]" [badge]="newNotifications$">
                        <span class="vcon vcon-onl vcon_page-bar_notification leading-[20px] text-[20px]"></span>
                    </span>
                </button>
                <lib-tooltip [toolTipFor]="notificationBtnEl" [tooltipContent]="'Thông báo'"></lib-tooltip> -->

                <button #outBtnEl class="v-tool-btn" (click)="emitLeaveEvent()">
                    <span class="vcon vcon-onl vcon_page-bar_out"></span>
                </button>
                <lib-tooltip [toolTipFor]="outBtnEl" [tooltipContent]="'Ra khỏi lớp'"></lib-tooltip>
            </div>
        </div>
    </div>

    <div class="fixed top-[75px] left-[50vw] z-[101]">
        <awareness
            *ngIf="(coordStateS.selected$ | async) && (onlStateS.coordinator$ | async)"
            class="relative top-0 left-[-50%]"
            [vpId$]="coordStateS.selected$"
            [coord$]="onlStateS.coordinator$">
        </awareness>
    </div>

    <conference />

    <app-event-notification></app-event-notification>
    <!-- SIDE BAR -->
    <div *ngIf="sidebarTab$ | async as sidebarTab" #sidebar class="side-bar z-[100] absolute overflow-hidden">
        <ng-template [ngIf]="sidebarTab === 'document'">
            <div *ngIf="validDocuments$ | async as docs" class="d-flex flex-col w-[240px] 2xl:w-[300px] h-full">
                <div
                    class="d-flex flex-row align-items-center ms-[15px] me-[15px] mt-[10px] mb-[10px] pb-[5px] border-b border-P1 text-P1">
                    <div>
                        <span class="text-[20px] leading-[20px] align-middle">
                            <i class="vcon vcon-onl vcon_sidebar-document"></i>
                        </span>
                        <span class="ps-[5px] text-[12px] font-[600] align-middle">TÀI LIỆU</span>
                    </div>
                    <span
                        class="absolute right-[15px] text-BW1 text-[20px] leading-[20px] align-middle cursor-pointer"
                        (click)="switchSideBarTab(undefined)">
                        <i class="vcon-general vcon_delete"></i>
                    </span>
                </div>
                <div class="d-flex flex-column text-P1 pr-[15px] pl-[15px] mb-[10px]">
                    <div class="text-[14px] font-[500]">Tổng: {{ docs?.length ?? 0 }}</div>
                    <div class="mt-[5px]">
                        <div class="d-flex flex-row border border-BW4 rounded-[12px] bg-BW7 p-[5px_10px] justify-end">
                            <input
                                type="text"
                                class="w-full outline-0 text-sm"
                                placeholder="Tất cả"
                                [(ngModel)]="filterDocumentQuery" />
                            <i class="vcon vcon-general vcon_general_filter ms-auto text-BW3"></i>
                        </div>
                    </div>
                </div>

                <div class="d-flex flex-column h-full overflow-y-auto">
                    <ng-template ngFor let-doc [ngForOf]="docs" [ngForTrackBy]="trackDocumentById">
                        <div [doc-item]="doc" [classroomOwner$]="creatorMember$"></div>
                    </ng-template>
                </div>
                <app-insert-document />
            </div>
        </ng-template>

        <ng-template [ngIf]="['confirmed', 'raising-hand', 'waiting-confirm'].includes(sidebarTab)">
            <div class="d-flex flex-col w-[240px] 2xl:w-[300px] h-full pb-[10px] text-P1">
                <div
                    class="d-flex flex-row align-items-center ms-[15px] me-[15px] mt-[10px] mb-[10px] pb-[5px] border-b border-P1 text-P1">
                    <ng-template [ngIf]="sidebarTab === 'confirmed'">
                        <div>
                            <span class="text-[20px] leading-[20px] align-middle">
                                <i class="vcon-general vcon_user_students"></i>
                            </span>
                            <span class="ps-[5px] text-[12px] font-[600] align-middle">HỌC VIÊN</span>
                        </div>
                    </ng-template>
                    <ng-template [ngIf]="sidebarTab === 'raising-hand'">
                        <div>
                            <span class="text-[20px] leading-[20px] align-middle">
                                <i class="vcon-onl vcon_sidebar_raise-hand"></i>
                            </span>
                            <span class="ps-[5px] text-[12px] font-[600] align-middle">PHÁT BIỂU</span>
                        </div>
                    </ng-template>
                    <ng-template [ngIf]="sidebarTab === 'waiting-confirm'">
                        <div>
                            <span class="text-[20px] leading-[20px] align-middle">
                                <i class="vcon-onl vcon_user_waiting-student"></i>
                            </span>
                            <span class="ps-[5px] text-[12px] font-[600] align-middle">CHỜ XÁC NHẬN</span>
                        </div>
                    </ng-template>
                    <span
                        class="absolute right-[15px] text-BW1 text-[20px] leading-[20px] align-middle cursor-pointer"
                        (click)="switchSideBarTab(undefined)">
                        <i class="vcon-general vcon_delete"></i>
                    </span>
                </div>
                <div class="d-flex flex-row relative align-items-center gap-[5px] ms-auto me-auto text-BW3">
                    <div
                        [class.text-P1]="sidebarTab === 'confirmed'"
                        class="w-[55px] text-center cursor-pointer click-indicator rounded-full px-2 py-1"
                        (click)="switchSideBarTab('confirmed')">
                        <i class="vcon-general vcon_user_students"></i>
                    </div>
                    <div
                        #riseHandEl
                        [class.text-P1]="sidebarTab === 'raising-hand'"
                        class="w-[55px] text-center cursor-pointer click-indicator rounded-full px-2 py-1"
                        (click)="switchSideBarTab('raising-hand'); memberStateS.newMemberRaisingHand$.next(0)">
                        <span class="w-[20px] h-[20px]" [badge]="memberStateS.newMemberRaisingHand$" [right]="-8">
                            <i class="vcon-onl vcon_sidebar_raise-hand"></i>
                        </span>
                    </div>
                    <div
                        [class.text-P1]="sidebarTab === 'waiting-confirm'"
                        class="w-[55px] text-center cursor-pointer click-indicator rounded-full px-2 py-1"
                        (click)="switchSideBarTab('waiting-confirm'); memberStateS.newMemberWaitingConfirm$.next(0)">
                        <span class="w-[20px] h-[20px]" [badge]="memberStateS.newMemberWaitingConfirm$">
                            <i class="vcon-onl vcon_user_waiting-student"></i>
                        </span>
                    </div>
                </div>
                <div class="d-flex flex-row align-items-center ms-[15px] text-sm font-[500] mb-[7.5px]">
                    <div>Tổng: {{ (members$ | async).length }}</div>
                    <div *ngIf="onlStateS.isOwner$ | async" class="ms-auto me-[15px]">
                        <button #allMuteBtn class="v-tool-btn relative" (click)="muteAllMics()">
                            <button
                                class="v-tool-btn"
                                *ngIf="!(mediaTogglesInProgress.allMics | async); else muteAllMicsLoading">
                                <img class="w-[14px] h-[14px]" src="assets/img/all-mic-off.svg" />
                            </button>
                        </button>
                        <lib-tooltip
                            [toolTipFor]="allMuteBtn"
                            [tooltipContent]="'Tắt mic trừ người phát biểu'"></lib-tooltip>
                        <ng-template #muteAllMicsLoading>
                            <span><button class="v-tool-btn" [spinner]="mediaTogglesInProgress.allMics"></button></span>
                        </ng-template>
                    </div>
                </div>
                <div class="d-flex flex-column h-full overflow-y-auto text-[14px] z-[100]">
                    <ng-template ngFor let-member [ngForOf]="members$ | async" [ngForTrackBy]="trackMemberById">
                        <div [member-item]="member.id"></div>
                    </ng-template>
                </div>
            </div>
        </ng-template>
        <ng-template [ngIf]="sidebarTab === 'notification'">
            <div
                class="d-flex flex-col w-[350px] text-P1 h-full pb-[10px]"
                click-outside
                (clickOutside)="hideSidebar($event)">
                <div
                    class="d-flex flex-row align-items-center ms-[15px] me-[15px] mt-[10px] mb-[10px] pb-[5px] border-b border-P1">
                    <div>
                        <span class="text-[20px] leading-[20px] align-middle">
                            <i class="vcon vcon-onl vcon_page-bar_notification"></i>
                        </span>
                        <span class="ps-[5px] text-[12px] font-[600] align-middle">THÔNG BÁO</span>
                    </div>
                    <div class="absolute right-[15px] text-BW1 text-[20px] leading-[20px] align-middle">
                        <button
                            class="btn btn-outline-primary h-[20px] w-[30px] !p-0"
                            (click)="notificationStateS.newNotification$.next(0)">
                            <span class="text-BW1 text-[20px] leading-[20px] align-middle">
                                <i class="vcon-general vcon_general_yes"></i>
                            </span>
                        </button>
                        <span
                            class="text-BW1 text-[20px] leading-[20px] align-middle cursor-pointer"
                            (click)="switchSideBarTab(undefined)">
                            <i class="vcon-general vcon_delete"></i>
                        </span>
                    </div>
                </div>
                <div class="d-flex flex-column h-full overflow-y-auto">
                    <ng-template
                        ngFor
                        let-noti
                        [ngForOf]="notifications$ | async"
                        [ngForTrackBy]="trackNotificationById">
                        <div [notification-item]="noti.id"></div>
                    </ng-template>
                </div>
            </div>
        </ng-template>
    </div>

    <!-- SIDE BAR -->
    <div *ngIf="sidebarTab$ | async as sidebarTab" #sidebar class="side-bar-cover z-[100] absolute overflow-hidden">
        <ng-template [ngIf]="sidebarTab === 'setting'">
            <div class="p-[15px]">
                <setting-tool
                    [settings]="settingS.docSetting$ | async"
                    [presenter]="presenterS.classroomPresenterState$ | async"
                    (onClose)="switchSideBarTab(undefined)"
                    (onChange)="onChangeSetting($event)"></setting-tool>
            </div>
        </ng-template>
    </div>

    <ng-template [ngIf]="markerS.isActiveMarkerTool$ | async">
        <marker-tool
            [settings]="markerS.classroomMarkerState$ | async"
            (onClose)="markerS.toggleActiveMarkerTool()"
            (onEraseMyDrawing)="eraseMyDrawing()"
            (onEraseOthersDrawing)="eraseOtherDrawing()"
            [isCanClearAll]="
                (classroomUserState$ | async)?.raiseHandStatus === 'PRESENTING' || (this.onlStateS.isOwner$ | async)
            "
            (onChange)="onChangeMarker($event)"></marker-tool>
    </ng-template>

    <!-- Viewports -->
    <ng-template [ngIf]="coordInitialized$ | async">
        <ng-template ngFor let-state$ [ngForOf]="coordStateS.curStates$ | async" [ngForTrackBy]="trackCoordStateById">
            <div
                *ngIf="state$ | async as state"
                class="classroom-viewport absolute h-full w-full top-0 left-0"
                [ngClass]="{ hidden: (coordStateS.selected$ | async) !== state.id }"
                [app-classroom-viewport]="state"
                (viewportCreated)="onViewportCreated($event)"></div>
        </ng-template>
    </ng-template>
</div>
