import { CommonModule } from '@angular/common';
import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    Output,
    ViewChild,
} from '@angular/core';
import { VEventListener, VEventSource, ZoomEventData } from '@viclass/editor.core';
import { TooltipComponent } from '@viclass/portal.common';
import { Observable } from 'rxjs';
import { MemberAvatarViewModel } from '../../../model';

@Component({
    selector: 'share-screen',
    templateUrl: './share-screen.component.html',
    styleUrls: ['./share-screen.component.css'],
    standalone: true,
    imports: [CommonModule, TooltipComponent],
})
export class ShareScreenComponent implements AfterViewInit {
    @ViewChild('videoWrapper', { static: true }) videoWrapperRef!: ElementRef<HTMLVideoElement>;
    @ViewChild('videoElement', { static: true }) videoElementRef!: ElementRef<HTMLVideoElement>;
    @ViewChild('videoContainerElement', { static: true }) videoContainerElementRef!: ElementRef<HTMLElement>;

    @Output() videoElementReady = new EventEmitter<HTMLVideoElement>();

    @Input()
    docLocalId: number;

    @Input()
    userId: string;

    @Input()
    avatarModel$: Observable<MemberAvatarViewModel>;

    @Input()
    zoomEventEmitter$: VEventSource<ZoomEventData>;

    @Output()
    onViewFullScreen = new EventEmitter<ShareScreenComponent>();

    zoomLevel = 1;

    viewFullScreen() {
        this.onViewFullScreen.emit(this);
    }

    constructor(private cdr: ChangeDetectorRef) {}

    ngAfterViewInit(): void {
        const videoElement = this.videoElementRef.nativeElement;
        this.videoElementReady.emit(videoElement);

        this.viewportChangeListener = this.initViewportChangeListener(this);
        this.zoomEventEmitter$.registerListener(this.viewportChangeListener);
    }

    restoreVideoElement(videoElement: HTMLVideoElement) {
        if (this.videoContainerElementRef && videoElement) {
            this.videoContainerElementRef.nativeElement.appendChild(videoElement);
        }
    }

    private viewportChangeListener: VEventListener<ZoomEventData>;

    protected initViewportChangeListener(p: ShareScreenComponent): VEventListener<any> {
        const self = p;
        return new (class implements VEventListener<ZoomEventData> {
            onEvent(eventData: any): any {
                const state = (eventData as ZoomEventData).state;
                self.zoomLevel = state.zoomLevel > 1 ? 1 : state.zoomLevel;
                self.cdr.detectChanges();
                return eventData;
            }
        })();
    }

    ngOnDestroy() {
        this.zoomEventEmitter$.unregisterListener(this.viewportChangeListener);
    }
}
