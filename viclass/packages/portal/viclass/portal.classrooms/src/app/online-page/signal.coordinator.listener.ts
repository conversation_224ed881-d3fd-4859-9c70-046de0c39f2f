import { SignalMessage, SignalMessageListener, SignalMessageType } from '@viclass/editor.coordinator/classroom';
import { Injectable } from '@angular/core';
import { OnlineStateService } from './online.state.service';
import { firstValueFrom } from 'rxjs';
import { SignalProcessor } from './signal.processor';
import { classroomErrorHandler } from './error-handler';
import { ErrorHandlerDecorator } from '@viclass/editor.core';

/**
 * Listen signal message related to coordinator/editor manage coordinator state, doc, syncing (allowSyncing,
 * stoppedSyncing, insertResponse, presentCoordState, renamedCoordState, pinCoordState, unpinCoordState, ...)
 *
 * <AUTHOR>
 */
@Injectable()
export class SignalCoordinatorListener implements SignalMessageListener {
    readonly signalMessageType: SignalMessageType[] = [
        'AcceptPresentationRequestND',
        'AcceptRaiseHandND',
        'StopPresentationND',
        'InsertDocResponse',
        'InsertLayerResponse',
        'presentCoordState',
        'renamedCoordState',
        'PinnedCoordStateND',
        'UnpinnedCoordStateND',
        'kickOut',
        'RTCConnectionChange',
        'DocInfoUpdated',
        'DocInfoDeleted',
    ];

    constructor(
        private signalProcessor: SignalProcessor,
        private onlStateS: OnlineStateService
    ) {}

    @ErrorHandlerDecorator([classroomErrorHandler])
    async onRequest(msg: SignalMessage): Promise<SignalMessage> {
        switch (msg.signalType) {
            case 'StopPresentationND': {
                await this.processRequestStopSyncingMsg(msg);
                break;
            }
            default:
                break;
        }
        return firstValueFrom(this.onlStateS.coordinator$).then(coord => {
            msg.toPeer = msg.fromPeer;
            msg.fromPeer = coord.conf.syncConf.peerId;
            return msg;
        });
    }

    @ErrorHandlerDecorator([classroomErrorHandler])
    async onMessage(msg: SignalMessage) {
        this.signalProcessor.onNewSignalMessage(msg);
    }

    private async processRequestStopSyncingMsg(msg: SignalMessage) {
        await firstValueFrom(this.onlStateS.coordinator$).then(async coord => {
            await coord.switchViewportMode(coord.roomInfo.presentingCoordState, 'InteractiveMode');
            await coord.switchViewport(coord.roomInfo.presentingCoordState);
        });
    }
}
