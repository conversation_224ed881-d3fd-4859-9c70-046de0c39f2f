import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { ReplaySubject } from 'rxjs';
import { CommonModule, NgOptimizedImage } from '@angular/common';
import { LSessionRegistrationModel } from '@viclass/portal.common';
import { PopupConfirmType } from '../../../model';

@Component({
    standalone: true,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [CommonModule, NgOptimizedImage],
    selector: '[confirm-kick-out-popup]',
    templateUrl: './confirm.kick.out.popup.component.html',
})
export class ConfirmKickOutPopupComponent {
    @Input('confirm-kick-out-popup')
    confirm$: ReplaySubject<PopupConfirmType>;

    @Input()
    member: LSessionRegistrationModel;

    protected action(type: PopupConfirmType) {
        this.confirm$.next(type);
        this.confirm$.complete();
    }

    protected get memberName(): string {
        return this.member.profile.username;
    }
}
