import { Inject, Injectable } from '@angular/core';
import Lifecycle from 'page-lifecycle';
import IdleTracker from 'idle-tracker';
import { ENVIRONMENT, UserAvailableStatus } from '@viclass/portal.common';
import { EditorCoordinator, ErrorHandlerDecorator } from '@viclass/editor.core';
import { ClassroomCoordinator, ClassroomCriticalError, RTCReport } from '@viclass/editor.coordinator/classroom';
import { UserStatusReport } from '../model';
import { CcsGateway } from '../gateways/ccs.gateway';
import { firstValueFrom } from 'rxjs';
import { classroomErrorHandler } from './error-handler';

/**
 *
 * <AUTHOR>
 */
@Injectable()
export class UserStatusDetector {
    private _currUserAvailableStatus: UserAvailableStatus;
    private idleTracker: IdleTracker;
    private _editorCoordinator: ClassroomCoordinator;
    private scheduleReportAway: number;
    private reportUserStatusScheduler: number;

    // Flags to manage reporting state
    private _isReporting: boolean = false;
    private _needsImmediateReport: boolean = false;
    private _stopped: boolean = false;

    constructor(
        @Inject(ENVIRONMENT) private readonly environment: any,
        private readonly ccsGateway: CcsGateway
    ) {
        this.idleTracker = new IdleTracker({
            timeout: environment.idleTimeout,
        });
    }

    private lifecycleStatechangeListener = (event: any) => {
        // console.debug('lifecycle', event);
        if (['active', 'passive'].includes(event.newState)) this.onReportUserIsOnline();
        else this.scheduleReportUserIsAway();
    };

    start(editorCoordinator: EditorCoordinator) {
        this._stopped = false; // Reset stopped state
        this.currUserAvailableStatus = 'ONLINE'; // Set initial status before first report
        this._editorCoordinator = editorCoordinator as ClassroomCoordinator;
        Lifecycle.addEventListener('statechange', this.lifecycleStatechangeListener);

        this.idleTracker.start({
            onIdleCallback: (data?: { idle: boolean; event?: Event | undefined }) => {
                // console.debug('idleTracker', data);
                if (data.idle) this.onReportUserIsIdle();
                else this.onReportUserIsOnline();
            },
        });

        this.report(0); // Initial report, scheduled immediately
    }

    rtcConnectionChange(status: string) {
        if (status == 'failed' || status == 'closed') {
            this.onReportUserIsOffline();
        }
    }

    async stop() {
        console.debug('stopping user status detector...');
        this._stopped = true; // Signal that detector is stopping

        Lifecycle.removeEventListener('statechange', this.lifecycleStatechangeListener);
        this.idleTracker.end();

        if (this.scheduleReportAway) {
            clearTimeout(this.scheduleReportAway);
            this.scheduleReportAway = undefined;
        }

        if (this.reportUserStatusScheduler) {
            clearTimeout(this.reportUserStatusScheduler);
            this.reportUserStatusScheduler = undefined;
        }
        // Any in-progress report will see _stopped = true and not schedule a new one.
    }

    get currUserAvailableStatus(): UserAvailableStatus {
        return this._currUserAvailableStatus;
    }

    private scheduleReportUserIsAway() {
        if (this._stopped) return;
        if (!this.scheduleReportAway) {
            this.scheduleReportAway = window.setTimeout(() => {
                this.onReportUserIsAway();
            }, this.environment.awayTimeOut);
        }
    }

    private onReportUserIsOnline() {
        if (this._stopped) return;
        if (this.scheduleReportAway) {
            clearTimeout(this.scheduleReportAway);
            this.scheduleReportAway = undefined;
        }
        if (!['ONLINE'].includes(this.currUserAvailableStatus)) {
            this.currUserAvailableStatus = 'ONLINE';
        }
    }

    private onReportUserIsIdle() {
        if (this._stopped) return;
        if (['ONLINE'].includes(this.currUserAvailableStatus)) {
            this.currUserAvailableStatus = 'IDLE';
        }
    }

    private onReportUserIsAway() {
        if (this._stopped) return;
        if (this.scheduleReportAway) {
            clearTimeout(this.scheduleReportAway);
            this.scheduleReportAway = undefined;
        }
        if (['ONLINE', 'IDLE'].includes(this.currUserAvailableStatus)) {
            this.currUserAvailableStatus = 'AWAY';
        }
    }

    private onReportUserIsOffline() {
        if (this._stopped) return;
        if (this.currUserAvailableStatus !== 'OFFLINE') {
            this.currUserAvailableStatus = 'OFFLINE';
        }
    }

    private set currUserAvailableStatus(status: UserAvailableStatus) {
        if (this._currUserAvailableStatus !== status) {
            this._currUserAvailableStatus = status;
            if (!this._stopped) {
                // Only report if not stopped
                this.report(0); // Report status change immediately
            }
        }
    }

    /**
     * Schedules or executes a user status report.
     */
    @ErrorHandlerDecorator([classroomErrorHandler])
    private report(scheduleTime: number) {
        if (this._stopped) {
            console.debug('UserStatusDetector is stopped. Skipping report scheduling.');
            return;
        }

        // Clear any existing pending timer.
        if (this.reportUserStatusScheduler) {
            clearTimeout(this.reportUserStatusScheduler);
            this.reportUserStatusScheduler = undefined;
        }

        // If an immediate report is requested by this call, flag it.
        if (scheduleTime === 0) {
            this._needsImmediateReport = true;
        }

        // If a report is currently being sent via network (_isReporting is true),
        // do not schedule another timer now. The current send, upon completion,
        // will check _needsImmediateReport and call report() again.
        if (this._isReporting) {
            console.log(
                `Report scheduling deferred: a report is already in progress. Needs immediate: ${this._needsImmediateReport}`
            );
            return;
        }

        // Determine the actual delay for this report.
        // If _needsImmediateReport is true, schedule immediately. Otherwise, use the provided scheduleTime.
        const actualScheduleTime = this._needsImmediateReport ? 0 : scheduleTime;

        this.reportUserStatusScheduler = window.setTimeout(async () => {
            if (this._stopped) {
                console.debug('UserStatusDetector stopped before report execution.');
                return;
            }

            // This is a safeguard. The primary _isReporting check in the outer `report` function
            // should prevent this state. If this occurs, it indicates a potential logic flaw
            // allowing concurrent setTimeout callbacks related to reporting.
            if (this._isReporting) {
                console.warn(
                    'UserStatusDetector: Report execution started while _isReporting was already true. This indicates a potential concurrency issue. Aborting this execution and flagging for immediate report if necessary.'
                );
                if (actualScheduleTime === 0) {
                    this._needsImmediateReport = true; // Ensure this immediate request is not lost
                }
                return; // Do not proceed with this report; let the other one finish.
            }

            this._isReporting = true;
            // Consume the flag for *this* report execution.
            // If another immediate report is requested *during* this send, _needsImmediateReport will be set true again by a call to report(0).
            const reportIsForImmediate = this._needsImmediateReport;
            this._needsImmediateReport = false;

            try {
                console.debug(
                    `UserStatusDetector: Sending report. Status: ${this.currUserAvailableStatus}, Immediate: ${reportIsForImmediate}, Timestamp: ${new Date().toISOString()}`
                );
                if (!this._editorCoordinator) {
                    console.warn('UserStatusDetector: EditorCoordinator not available. Skipping report.');
                    return; // Cannot report without editorCoordinator
                }

                const rtcReport: RTCReport = await this._editorCoordinator.rtcConnReports();
                const reportData: UserStatusReport = {
                    roomId: this._editorCoordinator.syncConfig().roomId,
                    peerId: this._editorCoordinator.syncConfig().peerId,
                    availableStatus: this.currUserAvailableStatus, // Uses the latest status
                    rtcConn: rtcReport,
                };

                await firstValueFrom(this.ccsGateway.sendReport(reportData));
                console.debug(`UserStatusDetector: Report sent successfully for status: ${reportData.availableStatus}`);
            } catch (e) {
                console.error('UserStatusDetector: send report error... ', e);
                // The ErrorHandlerDecorator applies to the `report` method itself.
                // Errors thrown from this async callback will be unhandled promise rejections
                // unless explicitly caught and handled, or if the decorator somehow instruments this.
                // The original code re-throws, which is kept here.
                if (e instanceof ClassroomCriticalError) throw e;
            } finally {
                this._isReporting = false;

                if (this._stopped) {
                    console.debug('UserStatusDetector: Stopped after report completion. No further reports scheduled.');
                } else {
                    // Determine delay for the *next* report.
                    // If _needsImmediateReport became true *during* this send operation (e.g. status changed),
                    // the next report should be immediate.
                    const nextScheduleDelay = this._needsImmediateReport
                        ? 0
                        : this.environment.reportUserStatusInterval;
                    if (this._needsImmediateReport) {
                        console.debug(
                            'UserStatusDetector: An immediate report was requested during the last send. Scheduling next report immediately.'
                        );
                    } else {
                        console.debug(`UserStatusDetector: Scheduling next report in ${nextScheduleDelay}ms.`);
                    }
                    this.report(nextScheduleDelay); // Schedule the next report.
                }
            }
        }, actualScheduleTime);
    }
}
