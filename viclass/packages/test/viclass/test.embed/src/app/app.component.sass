.board-viewport
  border: 1px solid red
  height: 700px
  width: 1024px
  position: relative
  background: linear-gradient(-90deg, rgba(0, 0, 0, .05) 1px, transparent 1px), linear-gradient(rgba(0, 0, 0, .05) 1px, transparent 1px), linear-gradient(-90deg, rgba(0, 0, 0, .08) 1px, transparent 1px), linear-gradient(rgba(0, 0, 0, .08) 1px, transparent 1px), #fff
  background-size: 20px 20px, 20px 20px, 100px 100px, 100px 100px


.viewport-container
  width: fit-content
  width: -moz-fit-content
  position: relative
  overflow: hidden


  .editor-ui
    position: absolute
    height: fit-content
    max-height: 100%
    width: fit-content
    max-width: 100%
    overflow: auto
    scrollbar-width: none
    padding-bottom: 1px

    ~::-webkit-scrollbar
      display: none


.example-list
  width: 100px
  border: solid 1px #ccc
  border-radius: 5px
  background: #fff
  text-align: center
  padding: 10px
  margin: 0


.example-list > li
  list-style-type: none
  border-bottom: solid 1px #8b8b8b
  padding: 8px 0


.example-list > li:last-child
  border-bottom: none

