@font-face {
    font-family: 'viclass-freedrawing';
    src:
        url('fonts/viclass-freedrawing.ttf?2igqwl') format('truetype'),
        url('fonts/viclass-freedrawing.woff?2igqwl') format('woff'),
        url('fonts/viclass-freedrawing.svg?2igqwl#viclass-freedrawing') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.vcon-freedrawing {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'viclass-freedrawing' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vcon_brush-outline .path1:before {
    content: '\e010';
    color: rgb(0, 0, 0);
}
.vcon_brush-outline .path2:before {
    content: '\e011';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_brush-outline .path3:before {
    content: '\e012';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_brush-outline_no-color .path1:before {
    content: '\e013';
    color: rgb(255, 0, 46);
}
.vcon_brush-outline_no-color .path2:before {
    content: '\e014';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_brush-outline_no-color .path3:before {
    content: '\e015';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_brush-outline_no-color .path4:before {
    content: '\e016';
    margin-left: -1em;
    color: rgb(18, 20, 20);
}
.vcon_color-list .path1:before {
    content: '\e018';
    color: rgb(18, 20, 20);
}
.vcon_color-list .path2:before {
    content: '\e019';
    margin-left: -1em;
    color: rgb(255, 255, 255);
}
.vcon_color-list_no-color .path1:before {
    content: '\e020';
    color: rgb(18, 20, 20);
}
.vcon_color-list_no-color .path2:before {
    content: '\e021';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_color-list_no-color .path3:before {
    content: '\e022';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_color-list_no-color .path4:before {
    content: '\e023';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_color-list_no-color .path5:before {
    content: '\e024';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon_delete:before {
    content: '\e903a';
}
.vcon_empty:before {
    content: '\e006';
}
.vcon_text-bold:before {
    content: '\e030';
}
.vcon_text-underline:before {
    content: '\e031';
}
.vcon_text-italic:before {
    content: '\e029';
}
.vcon_text-color .path1:before {
    content: '\e953';
    color: rgb(0, 0, 0);
}
.vcon_text-color .path2:before {
    content: '\e954';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path3:before {
    content: '\e955';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path4:before {
    content: '\e956';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path5:before {
    content: '\e957';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path6:before {
    content: '\e958';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path7:before {
    content: '\e959';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path8:before {
    content: '\e95a';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path9:before {
    content: '\e95b';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_text-color .path10:before {
    content: '\e95c';
    margin-left: -1em;
    color: rgb(250, 154, 0);
}
.vcon_line-weight:before {
    content: '\e952a';
}
.vcon_more-tools:before {
    content: '\e003';
}
.vcon_shapes:before {
    content: '\e002';
}
.vcon_pen:before {
    content: '\e945';
}
.vcon_text-note:before {
    content: '\e946';
}
.vcon_line:before {
    content: '\e947';
}
.vcon_arrow:before {
    content: '\e948';
}
.vcon_shape_square:before {
    content: '\e949a';
}
.vcon_shape_circle:before {
    content: '\e949b';
}
.vcon_shape_triangle:before {
    content: '\e949c';
}
.vcon_shape_polygon:before {
    content: '\e949d';
}
.vcon_shape_star:before {
    content: '\e949e';
}
.vcon_shape_chat:before {
    content: '\e949f';
}
.vcon_shape_think:before {
    content: '\e949';
}
.vcon_eraser:before {
    content: '\e950';
}
.vcon_flip_horizontal:before {
    content: '\e944a';
}
.vcon_flip_vertical:before {
    content: '\e944b';
}
