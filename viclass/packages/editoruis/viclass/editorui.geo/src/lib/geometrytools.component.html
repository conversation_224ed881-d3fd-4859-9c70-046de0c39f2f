<ng-template [ngIf]="(show$ | async) && tb">
    <div class="v-toolbar geo-toolbar">
        <div class="v-tool-group" tb-float-tool-input [toolBar]="tb" [floatHA]="hAlign" [floatVA]="vAlign">
            <ng-template [ngIf]="hasTool('CreateDocumentTool')">
                <ng-template [ngIf]="createTool && !(createTool.isCreatingDoc$ | async)" [ngIfElse]="disableSpinner">
                    <button
                        #geoToolCreateWorkspace
                        class="v-tool-btn"
                        [ngClass]="{ active: isToolActive('CreateDocumentTool') }"
                        (click)="switchTool('CreateDocumentTool')"
                        [hidden]="!focusAble('CreateDocumentTool')"
                        [disabled]="tb.isToolDisable('CreateDocumentTool')">
                        <span class="vcon vcon-geometry vcon_page-bar_ad"></span>
                    </button>
                    <tooltip-geo
                        [toolTipFor]="geoToolCreateWorkspace"
                        [tooltipContent]="'Tạo vùng làm việc'"></tooltip-geo>
                </ng-template>
            </ng-template>

            <tb-toolbar-button
                *ngFor="let btnData of geometryBtnsDataForCommandTool"
                [data]="btnData"
                [subMenuPositions]="subMenuPositions"
                [isToolActive]="isToolActive"
                [allowCheck]="isToolAllow"
                (btnClicked)="onToolbarBtnSwitchForCommandTool($event)">
            </tb-toolbar-button>

            <button #listElementBtnEl class="v-tool-btn" cdkOverlayOrigin>
                <tb-toolbar-button
                    [data]="{
                        name: 'ListElementTool',
                        iconClasses: 'vcon-geometry vcon_geometry_map',
                        label: 'Danh sách đối tượng',
                    }"
                    [subMenuPositions]="subMenuPositions"
                    [isToolActive]="isToolActive"
                    [allowCheck]="isToolAllow"
                    (btnClicked)="switchTool('ListElementTool')">
                </tb-toolbar-button>
            </button>
            <ng-template
                cdkConnectedOverlay
                cdkConnectedOverlayPush
                [cdkConnectedOverlayDisableClose]="true"
                [cdkConnectedOverlayOrigin]="listElementBtnEl"
                [cdkConnectedOverlayOpen]="isToolActive('ListElementTool') && isToolAllow('ListElementTool')"
                [cdkConnectedOverlayPositions]="subMenuPositions"
                [cdkConnectedOverlayOffsetY]="-200">
                <div class="v-toolbar">
                    <div class="v-tool-group shadow-SH1">
                        <div
                            class="absolute left-0 p-3 bg-white rounded-lg h-[500px] shadow-SH1 !will-change-auto min-w-[280px]">
                            <tb-list-element (onClose)="switchTool('ListElementTool')" [toolBar]="tb"></tb-list-element>
                        </div>
                    </div>
                </div>
            </ng-template>
            <tooltip-geo [toolTipFor]="listElementBtnEl" [tooltipContent]="'Danh sách đối tượng'"></tooltip-geo>

            <!-- Setting button -->
            <button #settingBtnEl class="v-tool-btn" cdkOverlayOrigin>
                <tb-toolbar-button
                    [data]="{
                        name: 'UpdatePropTool',
                        iconClasses: 'vcon-general vcon_sidebar-setting',
                        label: 'Cài đặt',
                    }"
                    [subMenuPositions]="subMenuPositions"
                    [isToolActive]="isToolActive"
                    [allowCheck]="isToolAllow"
                    (btnClicked)="switchTool('UpdatePropTool')">
                </tb-toolbar-button>
            </button>
            <ng-template
                cdkConnectedOverlay
                cdkConnectedOverlayPush
                [cdkConnectedOverlayDisableClose]="true"
                [cdkConnectedOverlayOrigin]="settingBtnEl"
                [cdkConnectedOverlayOpen]="isToolActive('UpdatePropTool') && isToolAllow('UpdatePropTool')"
                [cdkConnectedOverlayPositions]="subMenuPositions"
                [cdkConnectedOverlayOffsetY]="-200">
                <div class="v-toolbar">
                    <div class="v-tool-group shadow-SH1">
                        <div class="setting-tool-popup shadow-SH1">
                            <setting-tool (onClose)="switchTool('UpdatePropTool')" [tb]="tb"></setting-tool>
                        </div>
                    </div>
                </div>
            </ng-template>
            <tooltip-geo [toolTipFor]="settingBtnEl" [tooltipContent]="'Cài đặt'"></tooltip-geo>

            <div class="v-tool-separation"></div>
            <tb-toolbar-button
                *ngFor="let btnData of geometryBtnsData"
                [data]="btnData"
                [subMenuPositions]="subMenuPositions"
                [isToolActive]="isToolActive"
                [allowCheck]="isToolAllow"
                (btnClicked)="onToolbarBtnSwitch($event)">
            </tb-toolbar-button>
        </div>
    </div>

    <tb-naming-element-input
        [toolBar]="tb"
        [vAlign]="vAlign"
        [hAlign]="hAlign"
        [direction]="direction"></tb-naming-element-input>
</ng-template>

<ng-template #disableSpinner>
    <button class="v-tool-btn" disabled *ngIf="createTool != null">
        <span class="vcon vcon-common vcon_mini-spinner"></span>
    </button>
</ng-template>
