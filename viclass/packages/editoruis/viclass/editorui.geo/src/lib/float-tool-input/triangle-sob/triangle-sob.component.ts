import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnD<PERSON>roy } from '@angular/core';
import { GeometryToolBar, GeometryToolType, TriangleToolState } from '@viclass/editor.geo';
import { BehaviorSubject } from 'rxjs';
import { TOOLBAR, TOOLTYPE } from '../injection.token';
import { ToolListener, ToolListenerHost } from '../tool.listener';

/**
 * Component for display and edit the number of edges for CreateRegularPolygonTool
 */
@Component({
    selector: 'tb-triangle-sob',
    templateUrl: './triangle-sob.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TriangleSOBComponent implements AfterViewInit, OnDestroy, ToolListenerHost {
    private toolListener: ToolListener;

    public sob$ = new BehaviorSubject<boolean>(false);

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(TOOLBAR) private toolbar: GeometryToolBar,
        @Inject(TOOLTYPE) private tooltype: GeometryToolType
    ) {
        this.toolListener = new ToolListener(this, tooltype, changeDetectorRef);
    }

    ngAfterViewInit(): void {
        this.toolbar.registerToolListener(this.toolListener);
        this.updateInputFromToolState();
    }

    ngOnDestroy(): void {
        this.toolbar.unregisterToolListener(this.toolListener);
    }

    setSOB(value: boolean) {
        const ts = this.toolbar.toolState(this.tooltype) as TriangleToolState;
        ts.createFromBase = value;
        this.toolbar.update(this.tooltype, ts);
    }

    get sob(): boolean {
        const ts = this.toolbar.toolState(this.tooltype) as TriangleToolState;
        return ts.createFromBase;
    }

    updateInputFromToolState() {
        this.sob$.next(this.sob);
    }
}
