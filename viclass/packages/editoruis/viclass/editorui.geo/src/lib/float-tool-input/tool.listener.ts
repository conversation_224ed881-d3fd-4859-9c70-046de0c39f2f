import { ChangeDetectorRef } from '@angular/core';
import { ToolBar, ToolEventData, ToolEventListener, ToolState } from '@viclass/editor.core';
import { GeometryTool, GeometryToolType } from '@viclass/editor.geo';

export interface ToolListenerHost {
    updateInputFromToolState();
}

export class ToolListener
    implements ToolEventListener<ToolBar<GeometryToolType, GeometryTool<ToolState>>, GeometryToolType>
{
    constructor(
        private _p: ToolListenerHost,
        private tooltype: GeometryToolType,
        private cd: ChangeDetectorRef
    ) {}
    onEvent(eventData: ToolEventData<any, any>): ToolEventData<any, any> {
        if (eventData.toolType != this.tooltype) return eventData;

        this._p.updateInputFromToolState();
        this.cd.markForCheck();

        return eventData;
    }
}
