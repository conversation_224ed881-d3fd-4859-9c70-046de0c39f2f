$width: min(100vw, 570px);
$height: 70vh;

*::-webkit-scrollbar {
    @apply w-[4px];
}

.command-ui {
    @screen sm {
        top: calc($height / 2);
        left: calc($width / -2);
    }

    button {
        @apply leading-[20px] text-[20px] rounded-[10px];
    }

    .curly-braces {
        @apply text-P1;
    }

    .input-container {
        height: $height;
        width: $width;
        box-shadow: 0 5px 20px rgba(0, 66, 75, 0.2);
        @apply absolute;

        @screen max-xs {
            width: 100% !important;
        }

        .input-body {
            flex-flow: column;
        }

        .selected-constraint-row {
            @apply text-[13px] rounded-[10px] p-[10px] pt-[15px] float-left bg-BW6 border border-BW4;

            .selected-constraint {
                @apply cursor-pointer;

                .ML__mathit {
                    @apply text-SC1;
                }

                .vcon {
                    @apply text-[12px] leading-[12px] p-0 pl-[5px];
                }
            }
        }

        .command-input-group {
            button {
                @apply h-[24px] py-[2px] px-[5px] my-auto;

                .vcon {
                    line-height: 19px !important;
                }
            }

            input {
                @apply outline-none border-none;

                &::placeholder {
                    @apply text-BW3;
                    font-style: italic !important;
                }
            }

            .object-name-input {
                @apply w-[100px] pl-[5px] border-l border-l-BW3;
            }
        }

        .btn {
            @apply h-[30px] py-0 px-[10px] text-[14px] box-border font-semibold font-[Montserrat];
        }
    }
}

.list-tpl-container {
    @screen max-xs {
        width: 100% !important;
    }

    .list-tpl-body {
        .tpl-item {
            @apply cursor-pointer;

            &:hover,
            &:active,
            &:focus {
                @apply bg-BW4;
            }

            span {
                @apply whitespace-nowrap;
            }
        }
    }
}

.input-constraint {
    .constraint-params-input {
        &:focus {
            @apply outline-none;
        }

        .param-input {
            @apply border-none min-w-[5px];

            &:focus {
                @apply outline-none;
            }
        }
    }

    .input-container-1 {
        .param-text {
            @apply inline-block invisible whitespace-pre;
        }

        .param-input {
            @apply absolute top-0 left-0 right-0 bottom-0 w-full h-[26px];
        }
    }

    math-field {
        @apply outline-none border-transparent select-auto min-w-[20px] text-[1rem];

        &:focus {
            outline: none !important;
        }

        &::part(menu-toggle) {
            @apply hidden;
        }

        &::part(virtual-keyboard-toggle) {
            @apply hidden;
        }

        &::part(content) {
            border-bottom: thin solid;
            @apply border-P1;
        }

        &::part(container) {
            --_selection-color: inherit !important;
        }
    }

    .error-param-input math-field::part(content) {
        border-bottom: 1px solid red !important;
    }
}

.command-tool {
    max-width: min(80vw, 800px) !important;

    @screen max-xs {
        max-width: 100vw !important;
        width: 100vw !important;
        height: $height !important;
    }

    .mat-mdc-dialog-container {
        border-radius: 35px !important;
        max-height: calc(100vh - 100px) !important;

        @screen max-xs {
            width: 100vw !important;
            border-radius: 0px !important;
            border-radius: 0px !important;
        }

        @screen sm-max-md {
            @apply min-w-full;
        }

        @screen lg {
            @apply min-w-full;
        }

        .mdc-dialog__surface {
            @screen max-xs {
                border-radius: 0px !important;
            }
        }

        .popup-container {
            overflow-x: hidden !important;
            overflow-y: auto !important;

            @screen max-xs {
                @apply overflow-y-scroll max-h-screen;
            }
        }

        .mdc-dialog__surface {
            @apply shadow-none overflow-visible bg-transparent;
            border-radius: 35px !important;
            max-height: calc(100vh - 100px) !important;
        }
    }
}

// .mat-slide-toggle-bar {
//     background-color: #363a3e !important;
// }

// .mat-slide-toggle-thumb {
//     background-color: white !important;
//     border: 2px #363a3e solid !important;
//     background-image: url('/assets/img/minus.svg') !important;
//     background-size: 65% auto !important;
//     @apply bg-no-repeat bg-center;
// }

// .mat-slide-toggle.mat-checked {
//     .mat-slide-toggle-bar {
//         background-color: #00aeef !important;
//     }

//     .mat-slide-toggle-thumb {
//         background-color: white !important;
//         border: 2px #00aeef solid !important;
//         background-image: url('/assets/img/tick-resgistration-completion.svg') !important;
//         background-size: 100% auto !important;
//         @apply bg-no-repeat bg-center;
//     }
// }

#mathlive-suggestion-popover {
    z-index: 9999 !important;
}

.ML__keyboard.is-visible {
    @apply z-[9999];
}

.cdk-global-overlay-wrapper {
    @screen max-xs {
        align-items: end !important;
        padding: 0px !important;
    }
}
