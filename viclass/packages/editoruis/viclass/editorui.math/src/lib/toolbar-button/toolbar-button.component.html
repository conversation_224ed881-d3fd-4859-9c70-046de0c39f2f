<div *ngIf="data && !isBreak">
    <button
        class="v-tool-btn relative"
        [ngClass]="{ active: !isRootBtn && isActivated }"
        placement="bottom"
        (pointerup)="onBtnClick($event)"
        [disabled]="!isAllow"
        cdkOverlayOrigin
        #toolbarBtnRef
        (pointerenter)="onBtnMouseEnter()"
        (pointerleave)="onBtnMouseLeave($event)">
        <span class="{{ data.iconClasses }}"></span>
        <span *ngIf="hasAllowedChildren" class="vcon vcon-mathtype vcon_more-tools"></span>
    </button>

    <tooltip-math *ngIf="!!data.label" [toolTipFor]="toolbarBtnRef" [tooltipContent]="data.label"></tooltip-math>

    <ng-container *ngIf="hasAllowedChildren">
        <ng-template
            cdkConnectedOverlay
            cdkConnectedOverlayPush
            [cdkConnectedOverlayOrigin]="toolbarBtnRef"
            [cdkConnectedOverlayOpen]="stateService.isFocusPath(btnPath) | async"
            [cdkConnectedOverlayPositions]="subMenuPositions">
            <div
                [class]="'v-toolbar math-toolbar ' + childSize"
                (pointerenter)="onBtnMouseEnter(true)"
                (pointerleave)="onBtnMouseLeave($event, true)">
                <div
                    class="v-tool-group"
                    [ngStyle]="{
                        'grid-template-columns': 'repeat(' + layoutCols + ', minmax(0, 1fr))',
                        display: layoutCols > 1 ? 'grid' : 'flex',
                        width: groupWidth,
                    }">
                    <div class="grid-break">
                        <ng-content></ng-content>
                    </div>
                    <tb-toolbar-button
                        *ngFor="let btnData of data.children"
                        [data]="btnData"
                        [isToolActive]="isToolActive"
                        [subMenuPositions]="subMenuPositions"
                        [allowCheck]="allowCheck"
                        [parentPath]="btnPath"
                        (btnClicked)="onChildBtnClicked($event)"
                        [ngClass]="{
                            'grid-break': btnData.isBreak,
                        }"></tb-toolbar-button>
                </div>
            </div>
        </ng-template>
    </ng-container>
</div>
