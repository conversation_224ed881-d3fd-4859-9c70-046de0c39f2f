@font-face {
    font-family: 'viclass-mathtyp2';
    src:
        url('fonts/viclass-mathtyp2.ttf?z57zo8') format('truetype'),
        url('fonts/viclass-mathtyp2.woff?z57zo8') format('woff'),
        url('fonts/viclass-mathtyp2.svg?z57zo8#viclass-mathtyp2') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: block;
}

.vcon-mathtype {
    /* use !important to prevent issues with browser extensions that change fonts */
    font-family: 'viclass-mathtyp2' !important;
    speak: never;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;

    /* Better Font Rendering =========== */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.vcon-color-list_no-color .path1:before {
    content: '\e020';
    color: rgb(18, 20, 20);
}
.vcon-color-list_no-color .path2:before {
    content: '\e021';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon-color-list_no-color .path3:before {
    content: '\e022';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon-color-list_no-color .path4:before {
    content: '\e023';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon-color-list_no-color .path5:before {
    content: '\e024';
    margin-left: -1em;
    color: rgb(255, 0, 46);
}
.vcon-color-list .path1:before {
    content: '\e018';
    color: rgb(18, 20, 20);
}
.vcon-color-list .path2:before {
    content: '\e019';
    margin-left: -1em;
    color: rgb(255, 255, 255);
}
.vcon-bracket_none:before {
    content: '\e582';
}
.vcon-latex:before {
    content: '\e567';
}
.vcon-keyboard:before {
    content: '\e565';
}
.vcon-document_mathtype:before {
    content: '\e910e';
}
.vcon-page-bar_ad:before {
    content: '\e938';
}
.vcon-empty:before {
    content: '\e006';
}
.vcon-group_structure-operator:before {
    content: '\e491';
}
.vcon-group_structure-large-operator:before {
    content: '\e492';
}
.vcon-group_structure-dots:before {
    content: '\e493';
}
.vcon-group_structure-matrix:before {
    content: '\e494';
}
.vcon-group_structure-space:before {
    content: '\e495';
}
.vcon-group-symbol:before {
    content: '\e496';
}
.vcon-group_bat-dang-thuc:before {
    content: '\e497';
}
.vcon-group_relation-nhi-phan:before {
    content: '\e498';
}
.vcon-group_relation-tep-thuoc-vao:before {
    content: '\e499';
}
.vcon-group_binary-nhi-phan:before {
    content: '\e500';
}
.vcon-group_gk-and-func:before {
    content: '\e501';
}
.vcon-group_greek:before {
    content: '\e502';
}
.vcon-group_math-mode-accents:before {
    content: '\e503';
}
.vcon-group_accents-ext-panel:before {
    content: '\e504';
}
.vcon-group_trig:before {
    content: '\e505';
}
.vcon-group_inverse-trig:before {
    content: '\e506';
}
.vcon-group_log:before {
    content: '\e507';
}
.vcon-group_limit:before {
    content: '\e508';
}
.vcon-group_function:before {
    content: '\e509';
}
.vcon-group_math-arrow:before {
    content: '\e510';
}
.vcon-group_dau-ngoac:before {
    content: '\e511';
}
.vcon-group_text-style:before {
    content: '\e512';
}
.vcon-group_text-size:before {
    content: '\e513';
}
.vcon-midline-horizontal-ellipsis:before {
    content: '\e487';
}
.vcon-vertical-ellipsis:before {
    content: '\e488';
}
.vcon-down-right-diagonal-ellipsis:before {
    content: '\e490';
}
.vcon-math_negative-space:before {
    content: '\e483';
}
.vcon-math_thin-space:before {
    content: '\e484';
}
.vcon-math_medium-space:before {
    content: '\e485';
}
.vcon-math_thick-space:before {
    content: '\e486';
}
.vcon-math-structure_matrices-with-brackets-6:before {
    content: '\e473';
}
.vcon-math-structure_summation01:before {
    content: '\e438';
}
.vcon-math-structure_summation03:before {
    content: '\e440';
}
.vcon-math-structure_summation05:before {
    content: '\e442';
}
.vcon-math-structure_co-product01:before {
    content: '\e443';
}
.vcon-math-structure_co-product03:before {
    content: '\e445';
}
.vcon-math-structure_co-product05:before {
    content: '\e447';
}
.vcon-math-structure_product01:before {
    content: '\e448';
}
.vcon-math-structure_product03:before {
    content: '\e450';
}
.vcon-math-structure_product05:before {
    content: '\e452';
}
.vcon-math-structure_union01:before {
    content: '\e453';
}
.vcon-math-structure_union03:before {
    content: '\e455';
}
.vcon-math-structure_union05:before {
    content: '\e457';
}
.vcon-math-structure_intersection01:before {
    content: '\e458';
}
.vcon-math-structure_intersection03:before {
    content: '\e460';
}
.vcon-math-structure_intersection05:before {
    content: '\e462';
}
.vcon-math-structure_vee01:before {
    content: '\e463';
}
.vcon-math-structure_vee03:before {
    content: '\e465';
}
.vcon-math-structure_vee05:before {
    content: '\e467';
}
.vcon-math-structure_wedge01:before {
    content: '\e468';
}
.vcon-math-structure_wedge03:before {
    content: '\e470';
}
.vcon-math-structure_wedge05:before {
    content: '\e472';
}
.vcon-math-structure_superscript:before {
    content: '\e413';
}
.vcon-math-structure_subscript:before {
    content: '\e414';
}
.vcon-math-structure_subscript-superscript:before {
    content: '\e415';
}
.vcon-math-structure_subscript-superscript-2:before {
    content: '\e417';
}
.vcon-math-structure_fraction:before {
    content: '\e418';
}
.vcon-math-structure_interal01:before {
    content: '\e419';
}
.vcon-math-structure_interal02:before {
    content: '\e420';
}
.vcon-math-structure_interal03:before {
    content: '\e421';
}
.vcon-math-structure_interal04:before {
    content: '\e422';
}
.vcon-math-structure_double-interal01:before {
    content: '\e424';
}
.vcon-math-structure_double-interal02:before {
    content: '\e425';
}
.vcon-math-structure_tripple-interal01:before {
    content: '\e427';
}
.vcon-math-structure_tripple-interal02:before {
    content: '\e428';
}
.vcon-math-structure_contour-interal01:before {
    content: '\e430';
}
.vcon-math-structure_contour-interal02:before {
    content: '\e431';
}
.vcon-font-style_bold:before {
    content: '\e406';
}
.vcon-font-style_italic:before {
    content: '\e407';
}
.vcon-font-style_regular:before {
    content: '\e408';
}
.vcon-font-size_very-small:before {
    content: '\e409';
}
.vcon-font-size_small:before {
    content: '\e410';
}
.vcon-font-size_regular:before {
    content: '\e411';
}
.vcon-font-size_big:before {
    content: '\e412';
}
.vcon-vertical-bar:before {
    content: '\e398';
}
.vcon-double-pipe-character:before {
    content: '\e399';
}
.vcon-parentheses-symbols:before {
    content: '\e400';
}
.vcon-angle-bracket-symbol:before {
    content: '\e401';
}
.vcon-braces:before {
    content: '\e402';
}
.vcon-square-brackets:before {
    content: '\e403';
}
.vcon-ceiling-symbol:before {
    content: '\e404';
}
.vcon-floor-symbol:before {
    content: '\e405';
}
.vcon-right-arrow-from-bar:before {
    content: '\e368';
}
.vcon-left-arrow:before {
    content: '\e369';
}
.vcon-right-arrow:before {
    content: '\e370';
}
.vcon-long-left-arrow:before {
    content: '\e371';
}
.vcon-long-right-arrow:before {
    content: '\e372';
}
.vcon-left-double-arrow:before {
    content: '\e373';
}
.vcon-right-double-arrow:before {
    content: '\e374';
}
.vcon-long-left-double-arrow:before {
    content: '\e375';
}
.vcon-long-right-double-arrow:before {
    content: '\e376';
}
.vcon-left-harpoon-with-barb-up:before {
    content: '\e377';
}
.vcon-right-harpoon-with-barb-up:before {
    content: '\e378';
}
.vcon-left-harpoon-with-barb-down:before {
    content: '\e379';
}
.vcon-right-harpoon-with-barb-down:before {
    content: '\e380';
}
.vcon-left-harpoon-over-right-harpoon:before {
    content: '\e381';
}
.vcon-right-harpoon-over-left-harpoon:before {
    content: '\e382';
}
.vcon-left-right-arrow:before {
    content: '\e383';
}
.vcon-left-right-double-arrow:before {
    content: '\e384';
}
.vcon-left-arrow-with-hook:before {
    content: '\e385';
}
.vcon-right-arrow-with-hook:before {
    content: '\e386';
}
.vcon-right-squiggle:before {
    content: '\e387';
}
.vcon-diagonal-upward-left-arrow:before {
    content: '\e388';
}
.vcon-diagonal-upward-right-arrow:before {
    content: '\e389';
}
.vcon-diagonal-downward-left-arrow:before {
    content: '\e390';
}
.vcon-diagonal-downward-right-arrow:before {
    content: '\e391';
}
.vcon-up-arrow:before {
    content: '\e392';
}
.vcon-down-arrow:before {
    content: '\e393';
}
.vcon-up-double-arrow:before {
    content: '\e394';
}
.vcon-down-double-arrow:before {
    content: '\e395';
}
.vcon-up-down-arrow:before {
    content: '\e396';
}
.vcon-up-down-double-arrow:before {
    content: '\e397';
}
.vcon-argument-of-a-complex-number:before {
    content: '\e361';
}
.vcon-detdim:before {
    content: '\e362';
}
.vcon-gcd:before {
    content: '\e363';
}
.vcon-hom:before {
    content: '\e364';
}
.vcon-ker:before {
    content: '\e365';
}
.vcon-pr:before {
    content: '\e366';
}
.vcon-sup:before {
    content: '\e367';
}
.vcon-infinite:before {
    content: '\e349';
}
.vcon-limit:before {
    content: '\e434';
}
.vcon-limit-superior:before {
    content: '\e351';
}
.vcon-limit-inferior:before {
    content: '\e435';
}
.vcon-inj-limit:before {
    content: '\e353';
}
.vcon-proj-limit:before {
    content: '\e436';
}
.vcon-maximum:before {
    content: '\e437';
}
.vcon-minimum:before {
    content: '\e432';
}
.vcon-varlimsup:before {
    content: '\e357';
}
.vcon-varliminf:before {
    content: '\e358';
}
.vcon-varinjlim:before {
    content: '\e359';
}
.vcon-varprojlim:before {
    content: '\e360';
}
.vcon-exponential:before {
    content: '\e433';
}
.vcon-logarit-thap-phan:before {
    content: '\e344';
}
.vcon-logarit-tu-nhien:before {
    content: '\e345';
}
.vcon-logarit:before {
    content: '\e346';
}
.vcon-log-e:before {
    content: '\e347';
}
.vcon-log-10:before {
    content: '\e348';
}
.vcon-arcsine:before {
    content: '\e328';
}
.vcon-arcosine:before {
    content: '\e329';
}
.vcon-arctangent:before {
    content: '\e330';
}
.vcon-arccosecant:before {
    content: '\e331';
}
.vcon-arcsecant:before {
    content: '\e332';
}
.vcon-arccotangent:before {
    content: '\e333';
}
.vcon-inverse-sine:before {
    content: '\e334';
}
.vcon-inverse-cosine:before {
    content: '\e335';
}
.vcon-inverse-tangent:before {
    content: '\e336';
}
.vcon-inverse-cosecant:before {
    content: '\e337';
}
.vcon-inverse-secant:before {
    content: '\e338';
}
.vcon-inverse-cotangent:before {
    content: '\e339';
}
.vcon-inverse-hyperbolic-sine:before {
    content: '\e340';
}
.vcon-inverse-hyperbolic-cosine:before {
    content: '\e341';
}
.vcon-inverse-hyperbolic-tangent:before {
    content: '\e342';
}
.vcon-sine:before {
    content: '\e318';
}
.vcon-cosine:before {
    content: '\e319';
}
.vcon-tangent:before {
    content: '\e320';
}
.vcon-cosecant:before {
    content: '\e321';
}
.vcon-secant:before {
    content: '\e322';
}
.vcon-cotangent:before {
    content: '\e323';
}
.vcon-hyperbolic-sine:before {
    content: '\e324';
}
.vcon-hyperbolic-cosine:before {
    content: '\e325';
}
.vcon-hyperbolic-tangent:before {
    content: '\e326';
}
.vcon-hyperbolic-cotangent:before {
    content: '\e327';
}
.vcon-wide-tilde:before {
    content: '\e303';
}
.vcon-wide-hat:before {
    content: '\e304';
}
.vcon-over-line:before {
    content: '\e305';
}
.vcon-under-line:before {
    content: '\e306';
}
.vcon-over-left-arrow:before {
    content: '\e307';
}
.vcon-under-left-arrow:before {
    content: '\e308';
}
.vcon-over-right-arrow:before {
    content: '\e309';
}
.vcon-under-right-arrow:before {
    content: '\e310';
}
.vcon-over-left-right-arrow:before {
    content: '\e311';
}
.vcon-under-left-right-arrow:before {
    content: '\e312';
}
.vcon-over-brace:before {
    content: '\e313';
}
.vcon-under-brace:before {
    content: '\e314';
}
.vcon-over-set:before {
    content: '\e315';
}
.vcon-under-set:before {
    content: '\e316';
}
.vcon-square-root:before {
    content: '\e317';
}
.vcon-dot:before {
    content: '\e288';
}
.vcon-ddot:before {
    content: '\e289';
}
.vcon-breve:before {
    content: '\e292';
}
.vcon-check:before {
    content: '\e293';
}
.vcon-hat:before {
    content: '\e294';
}
.vcon-bar:before {
    content: '\e295';
}
.vcon-grave:before {
    content: '\e296';
}
.vcon-acute:before {
    content: '\e297';
}
.vcon-tilde:before {
    content: '\e298';
}
.vcon-circ:before {
    content: '\e299';
}
.vcon-mathring:before {
    content: '\e300';
}
.vcon-vector:before {
    content: '\e301';
}
.vcon-not:before {
    content: '\e302';
}
.vcon-ae:before {
    content: '\e280';
}
.vcon-ae-uppercase:before {
    content: '\e281';
}
.vcon-o:before {
    content: '\e282';
}
.vcon-o-uppercase:before {
    content: '\e283';
}
.vcon-l:before {
    content: '\e284';
}
.vcon-l-uppercase:before {
    content: '\e285';
}
.vcon-oe:before {
    content: '\e286';
}
.vcon-oe-uppercase:before {
    content: '\e287';
}
.vcon-alpha:before {
    content: '\e232';
}
.vcon-alpha-uppercase:before {
    content: '\e233';
}
.vcon-beta:before {
    content: '\e234';
}
.vcon-beta-uppercase:before {
    content: '\e235';
}
.vcon-gamma:before {
    content: '\e236';
}
.vcon-gamma-uppercase:before {
    content: '\e237';
}
.vcon-delta:before {
    content: '\e238';
}
.vcon-delta-uppercase:before {
    content: '\e239';
}
.vcon-epsilon:before {
    content: '\e240';
}
.vcon-varepsilon:before {
    content: '\e241';
}
.vcon-epsilon-uppercasw:before {
    content: '\e242';
}
.vcon-zeta:before {
    content: '\e243';
}
.vcon-zeta-uppercase:before {
    content: '\e244';
}
.vcon-eta:before {
    content: '\e245';
}
.vcon-eta-uppercase:before {
    content: '\e246';
}
.vcon-theta:before {
    content: '\e247';
}
.vcon-vartheta:before {
    content: '\e248';
}
.vcon-theta-uppercase:before {
    content: '\e249';
}
.vcon-iota:before {
    content: '\e250';
}
.vcon-iota-uppercase:before {
    content: '\e251';
}
.vcon-kappa:before {
    content: '\e252';
}
.vcon-kappa-uppercase:before {
    content: '\e253';
}
.vcon-lambda:before {
    content: '\e254';
}
.vcon-lambda-uppercase:before {
    content: '\e255';
}
.vcon-mu:before {
    content: '\e256';
}
.vcon-mu-uppercase:before {
    content: '\e257';
}
.vcon-nu:before {
    content: '\e258';
}
.vcon-nu-uppercase:before {
    content: '\e259';
}
.vcon-xi:before {
    content: '\e260';
}
.vcon-xi-uppercase:before {
    content: '\e261';
}
.vcon-pi:before {
    content: '\e262';
}
.vcon-pi-uppercase:before {
    content: '\e263';
}
.vcon-rho:before {
    content: '\e264';
}
.vcon-rho-uppercase:before {
    content: '\e265';
}
.vcon-sigma:before {
    content: '\e266';
}
.vcon-sigma-uppercase:before {
    content: '\e267';
}
.vcon-tau:before {
    content: '\e268';
}
.vcon-tau-uppercase:before {
    content: '\e269';
}
.vcon-upsilon:before {
    content: '\e270';
}
.vcon-upsilon-uppercase:before {
    content: '\e271';
}
.vcon-phi:before {
    content: '\e272';
}
.vcon-phi-uppercase:before {
    content: '\e273';
}
.vcon-chi:before {
    content: '\e274';
}
.vcon-chi-uppercase:before {
    content: '\e275';
}
.vcon-psi:before {
    content: '\e276';
}
.vcon-psi-uppercase:before {
    content: '\e277';
}
.vcon-omega:before {
    content: '\e278';
}
.vcon-omega-uppercase:before {
    content: '\e279';
}
.vcon-minus-plus:before {
    content: '\e193';
}
.vcon-plus-minus:before {
    content: '\e194';
}
.vcon-division-sign:before {
    content: '\e195';
}
.vcon-asterisk:before {
    content: '\e196';
}
.vcon-set-minus:before {
    content: '\e197';
}
.vcon-dot-plus:before {
    content: '\e198';
}
.vcon-product-sign:before {
    content: '\e199';
}
.vcon-coproduct-sign:before {
    content: '\e200';
}
.vcon-dagger:before {
    content: '\e201';
}
.vcon-double-dagger:before {
    content: '\e202';
}
.vcon-wreath-product:before {
    content: '\e203';
}
.vcon-circled-asterisk-operator:before {
    content: '\e204';
}
.vcon-intersection:before {
    content: '\e205';
}
.vcon-union:before {
    content: '\e206';
}
.vcon-double-intersection:before {
    content: '\e207';
}
.vcon-double-union:before {
    content: '\e208';
}
.vcon-square-cap:before {
    content: '\e209';
}
.vcon-square-cup:before {
    content: '\e210';
}
.vcon-logical-and:before {
    content: '\e211';
}
.vcon-logical-or:before {
    content: '\e212';
}
.vcon-normal-subgroup-of-or-equal-to:before {
    content: '\e213';
}
.vcon-contains-as-normal-subgroup-or-equal-to:before {
    content: '\e214';
}
.vcon-normal-subgroup-of:before {
    content: '\e215';
}
.vcon-contains-as-normal-subgroup-of:before {
    content: '\e216';
}
.vcon-diamond:before {
    content: '\e217';
}
.vcon-composite-function:before {
    content: '\e218';
}
.vcon-circled-minus:before {
    content: '\e219';
}
.vcon-circled-plus:before {
    content: '\e220';
}
.vcon-circle-division-slash:before {
    content: '\e221';
}
.vcon-circled-dash:before {
    content: '\e222';
}
.vcon-circled-dot-operator:before {
    content: '\e223';
}
.vcon-dot-operator:before {
    content: '\e224';
}
.vcon-multiset-union:before {
    content: '\e225';
}
.vcon-bigtriangleup:before {
    content: '\e226';
}
.vcon-bigtriangledown:before {
    content: '\e227';
}
.vcon-star:before {
    content: '\e228';
}
.vcon-big-star:before {
    content: '\e229';
}
.vcon-bigcirc:before {
    content: '\e230';
}
.vcon-circled-times:before {
    content: '\e231';
}
.vcon-subset-of:before {
    content: '\e168';
}
.vcon-superset-of:before {
    content: '\e169';
}
.vcon-subset-of-or-equal-to:before {
    content: '\e170';
}
.vcon-superset-of-or-equal-to:before {
    content: '\e171';
}
.vcon-subset-of-with-not-equal-to:before {
    content: '\e172';
}
.vcon-superset-of-with-not-equal-to:before {
    content: '\e173';
}
.vcon-varsubsetneq:before {
    content: '\e174';
}
.vcon-varsupsetneq:before {
    content: '\e175';
}
.vcon-neither-a-subset-of-nor-equal-to:before {
    content: '\e176';
}
.vcon-neither-a-superset-of-nor-equal-to:before {
    content: '\e177';
}
.vcon-square-image-of:before {
    content: '\e178';
}
.vcon-square-original-of:before {
    content: '\e179';
}
.vcon-square-image-of-or-equal-to:before {
    content: '\e180';
}
.vcon-square-original-of-or-equal-to:before {
    content: '\e181';
}
.vcon-subseteqq:before {
    content: '\e182';
}
.vcon-supseteqq:before {
    content: '\e183';
}
.vcon-varsubsetneqq:before {
    content: '\e184';
}
.vcon-subsetneqq:before {
    content: '\e185';
}
.vcon-varsupsetneqq:before {
    content: '\e186';
}
.vcon-nsupseteqq:before {
    content: '\e187';
}
.vcon-double-subset:before {
    content: '\e188';
}
.vcon-double-superset:before {
    content: '\e189';
}
.vcon-belong-to:before {
    content: '\e190';
}
.vcon-contains-as-member:before {
    content: '\e191';
}
.vcon-does-not-belong-to:before {
    content: '\e192';
}
.vcon-less-than:before {
    content: '\e134';
}
.vcon-greater-than:before {
    content: '\e135';
}
.vcon-less-than-or-equal-to:before {
    content: '\e136';
}
.vcon-greater-than-or-equal-to:before {
    content: '\e137';
}
.vcon-not-less-than:before {
    content: '\e138';
}
.vcon-not-greater-than:before {
    content: '\e139';
}
.vcon-neither-less-than-nor-equal-to:before {
    content: '\e140';
}
.vcon-neither-greater-than-nor-equal-to:before {
    content: '\e141';
}
.vcon-much-less-than:before {
    content: '\e142';
}
.vcon-much-greater-than:before {
    content: '\e143';
}
.vcon-precedes:before {
    content: '\e144';
}
.vcon-succeed:before {
    content: '\e145';
}
.vcon-precedes-or-equal-to:before {
    content: '\e146';
}
.vcon-succeed-or-equal-to:before {
    content: '\e147';
}
.vcon-does-not-yield:before {
    content: '\e148';
}
.vcon-proves:before {
    content: '\e149';
}
.vcon-true:before {
    content: '\e150';
}
.vcon-perpendicular-to:before {
    content: '\e151';
}
.vcon-parallel-to:before {
    content: '\e152';
}
.vcon-middle:before {
    content: '\e153';
}
.vcon-join:before {
    content: '\e154';
}
.vcon-equal:before {
    content: '\e155';
}
.vcon-approaches-the-limit:before {
    content: '\e156';
}
.vcon-identical:before {
    content: '\e157';
}
.vcon-not-equal-to:before {
    content: '\e158';
}
.vcon-not-identical-to:before {
    content: '\e159';
}
.vcon-approximately-sign:before {
    content: '\e15a';
}
.vcon-almost-equal-to:before {
    content: '\e160';
}
.vcon-asymptotically-equal-to:before {
    content: '\e161';
}
.vcon-approximately-equals-to:before {
    content: '\e162';
}
.vcon-frown:before {
    content: '\e163';
}
.vcon-smile:before {
    content: '\e164';
}
.vcon-proportional-to:before {
    content: '\e165';
}
.vcon-bowtie:before {
    content: '\e166';
}
.vcon-equivalent-to:before {
    content: '\e167';
}
.vcon-therefore:before {
    content: '\e100';
}
.vcon-because:before {
    content: '\e101';
}
.vcon-partial-differential:before {
    content: '\e102';
}
.vcon-real-part:before {
    content: '\e103';
}
.vcon-imaginary-part:before {
    content: '\e104';
}
.vcon-for-all:before {
    content: '\e105';
}
.vcon-prime:before {
    content: '\e107';
}
.vcon-natural:before {
    content: '\e108';
}
.vcon-integer:before {
    content: '\e109';
}
.vcon-irrational:before {
    content: '\e10f';
}
.vcon-rational:before {
    content: '\e111';
}
.vcon-real:before {
    content: '\e112';
}
.vcon-complex:before {
    content: '\e113';
}
.vcon-acute-angle:before {
    content: '\e114';
}
.vcon-measured-angle:before {
    content: '\e115';
}
.vcon-spherical-angle:before {
    content: '\e116';
}
.vcon-empty-set:before {
    content: '\e117';
}
.vcon-infinity-sign:before {
    content: '\e118';
}
.vcon-inverted-ohm-sign:before {
    content: '\e119';
}
.vcon-weierstrass-elliptic-function:before {
    content: '\e120';
}
.vcon-not-signed:before {
    content: '\e121';
}
.vcon-surd:before {
    content: '\e122';
}
.vcon-triangle:before {
    content: '\e123';
}
.vcon-square:before {
    content: '\e124';
}
.vcon-tee:before {
    content: '\e125';
}
.vcon-perpendicular-to1:before {
    content: '\e126';
}
.vcon-empty-set1:before {
    content: '\e127';
}
.vcon-there-does-not-exist:before {
    content: '\e128';
}
.vcon-complement:before {
    content: '\e129';
}
.vcon-integral:before {
    content: '\e130';
}
.vcon-double-Integral:before {
    content: '\e131';
}
.vcon-contour-integral:before {
    content: '\e132';
}
.vcon-summation-sign:before {
    content: '\e133';
}
