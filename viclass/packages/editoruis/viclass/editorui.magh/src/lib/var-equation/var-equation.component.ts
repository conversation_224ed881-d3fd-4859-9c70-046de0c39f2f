import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    ElementRef,
    EventEmitter,
    Input,
    OnDestroy,
    OnInit,
    Output,
    ViewChild,
} from '@angular/core';
import { EqType, EquationState, UpdateEquationsTool } from '@viclass/editor.magh';
import { MathfieldElement, validateLatex } from 'lib-mathlive';
import { BehaviorSubject, Subject, throttleTime } from 'rxjs';
import { MaghEquationService } from '../magh.equation.service';
import { detectInvalidInputMethod, getSyntaxErrorMessage, UpdateExpressionEvent } from '../maghtools.models';

@Component({
    selector: 'lib-var-equation',
    templateUrl: './var-equation.component.html',
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class VarEquationComponent implements OnInit, AfterViewInit, OnD<PERSON>roy {
    @ViewChild('mathRoot', { read: ElementRef })
    mathRoot: ElementRef<HTMLDivElement>;

    mathfield: MathfieldElement;

    @Input() equation: Extract<EquationState, { equationType: EqType.ScopeVar }>;
    @Input() showSettings: boolean = false;
    @Input() colorPalette: string[] = [];
    @Input() tool: UpdateEquationsTool;

    @Output() onToggleSettings = new EventEmitter<number>();
    @Output() onDelete = new EventEmitter<number>();

    exprThrottler = new Subject<UpdateExpressionEvent>();
    unsubscribes: (() => void)[] = [];

    showEditScopeVar$ = new BehaviorSubject<boolean>(false);

    syntaxError$ = new BehaviorSubject<string | null>(null);

    invalidInputMethod$ = new BehaviorSubject<boolean>(false);

    isEditing = false;

    get iconColor(): string {
        return '#fff';
    }

    get scopeMin(): number {
        return this.equation.globalScope.min;
    }

    get scopeMax(): number {
        return this.equation.globalScope.max;
    }

    constructor(
        private cdr: ChangeDetectorRef,
        private maghEquationService: MaghEquationService
    ) {}

    ngOnInit(): void {
        const exprSubsc = this.exprThrottler
            .pipe(throttleTime(100, undefined, { leading: true, trailing: true }))
            .subscribe(ev => {
                this.maghEquationService.updateExpression(this.tool, ev);
                this.validateExpression(ev.expression);
            });

        const changedSusc = this.maghEquationService.equationChanged$.subscribe(() => {
            this.cdr.markForCheck();
            // because the value is not mapped directly to the mathfield, we need to do it manually
            requestAnimationFrame(() => {
                if (!this.mathfield) return;
                const latex = this.equation.expression;
                if (latex !== this.mathfield.value) {
                    this.mathfield.setValue(latex);
                    this.validateExpression(latex);
                }
            });
        });

        this.unsubscribes.push(
            () => exprSubsc.unsubscribe(),
            () => changedSusc.unsubscribe()
        );
    }

    ngOnDestroy(): void {
        this.unsubscribes.forEach(unsubscribe => unsubscribe());
        this.unsubscribes.length = 0;

        this.mathfield?.disconnectedCallback();
        this.mathfield?.remove();
        delete this.mathfield;
    }

    ngAfterViewInit(): void {
        const equation = this.equation;
        this.mathfield = new MathfieldElement();
        this.mathfield.setValue(equation.expression);
        Object.assign(this.mathfield.style, {
            display: 'block',
            outline: 'none',
            borderColor: 'transparent',
            userSelect: 'auto',
        });
        this.mathRoot.nativeElement.appendChild(this.mathfield);

        this.mathfield.menuItems = [];
        const inputListener = () => {
            this.exprThrottler.next({
                index: equation.index,
                expression: this.mathfield.value,
                finished: false,
            });
        };
        const changeListener = () => {
            this.exprThrottler.next({
                index: equation.index,
                expression: this.mathfield.value,
                finished: true,
            });
        };
        const focusListener = () => (this.isEditing = true);
        const blurListener = () => (this.isEditing = false);
        const keydownListener = (e: KeyboardEvent) => {
            // prevent trigger unexpected events like 'v'/'h' is the shortcut for move/pan tool
            if (e.code.startsWith('Digit') || e.code.startsWith('Key')) {
                e.stopPropagation();
            }
        };
        this.mathfield.addEventListener('input', inputListener);
        this.mathfield.addEventListener('change', changeListener);
        this.mathfield.addEventListener('focus', focusListener);
        this.mathfield.addEventListener('blur', blurListener);
        this.mathfield.addEventListener('keydown', keydownListener);
        this.unsubscribes.push(() => {
            this.mathfield.removeEventListener('input', inputListener);
            this.mathfield.removeEventListener('change', changeListener);
            this.mathfield.removeEventListener('focus', focusListener);
            this.mathfield.removeEventListener('blur', blurListener);
            this.mathfield.removeEventListener('keydown', keydownListener);
        });

        const { isInvalidInput$, cleanup } = detectInvalidInputMethod(this.mathfield);
        this.invalidInputMethod$ = isInvalidInput$;
        this.unsubscribes.push(cleanup);

        this.validateExpression(equation.expression);
    }

    deleteEquation() {
        this.onDelete.emit(this.equation.index);
    }

    changeGlobalScope($event: Event) {
        const newVal = ($event.target as HTMLInputElement).value;
        const latexParts = this.mathfield.value.split('=');
        if (latexParts.length < 2) {
            return;
        }
        latexParts.pop();
        latexParts.push(newVal);
        const newLatex = latexParts.join('=');
        this.mathfield.setValue(newLatex);

        this.exprThrottler.next({
            index: this.equation.index,
            expression: newLatex,
            finished: false,
        });
    }

    changeGlobalScopeRange(field: 'min' | 'max' | 'step', value: number | null) {
        if (value !== null) {
            this.equation.globalScope[field] = value;
        }
    }

    applyGlobalScopeRange() {
        this.maghEquationService.changeGlobalScope(this.tool, {
            index: this.equation.index,
            min: this.equation.globalScope.min,
            max: this.equation.globalScope.max,
            step: this.equation.globalScope.step,
        });
        this.showEditScopeVar$.next(false);
    }

    private validateExpression(latex: string): void {
        if (!latex) {
            this.syntaxError$.next(null);
            return;
        }

        const errors = validateLatex(latex);
        if (errors.length > 0) {
            const error = errors[0];
            this.syntaxError$.next(getSyntaxErrorMessage(error));
        } else {
            this.syntaxError$.next(null);
        }
    }
}
