<div class="plot-settings w-[260px] min-h-[200px]">
    <div class="flex gap-1 items-center">
        <div class="flex items-center justify-center gap-3">
            <button (click)="selectedTab = 'doc'" class="flex items-center justify-center gap-1">
                <span
                    class="vcon vcon-general vcon_document_magh"
                    [ngClass]="{
                        'text-P1': selectedTab === 'doc',
                    }"></span>
                <span *ngIf="selectedTab === 'doc'" class="font-[600] text-[12px] leading-[18px] text-P1">
                    TÀI LIỆU
                </span>
            </button>
            <button (click)="selectedTab = 'defaultStyles'" class="flex items-center justify-center gap-1">
                <span
                    class="vcon vcon-magh vcon_properties"
                    [ngClass]="{
                        'text-P1': selectedTab === 'defaultStyles',
                    }"></span>
                <span *ngIf="selectedTab === 'defaultStyles'" class="font-[600] text-[12px] leading-[18px] text-P1">
                    MẶC ĐỊNH
                </span>
            </button>
        </div>
        <div class="flex-grow"></div>
        <button (click)="onClose.emit(true)">
            <span class="vcon vcon-general vcon_delete text-BW1 text-[13px]"></span>
        </button>
    </div>

    <div class="bg-P1 h-[1px]" style="margin: 0.75rem 0"></div>

    <div class="overflow-x-hidden h-full w-full">
        <div class="flex gap-3" *ngIf="!!settings && selectedTab === 'doc'">
            <div class="flex gap-3 flex-col w-full h-full overflow-hidden">
                <lib-setting-tool-adjust-number
                    *ngIf="settings.scale !== null"
                    label="Hiển thị"
                    [value]="{ value: settings.scale }"
                    field="scale"
                    [min]="25"
                    [max]="500"
                    suffix="%"
                    [step]="10"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-adjust-number>
                <lib-setting-tool-switch
                    [label]="'Trục tọa độ'"
                    field="axis"
                    [value]="{ value: settings.axis }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <lib-setting-tool-switch
                    [label]="'Mũi tên trục tọa độ'"
                    field="showAxisArrows"
                    [value]="{ value: settings.showAxisArrows }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <lib-setting-tool-switch
                    [label]="'Nhãn trục tọa độ'"
                    field="showAxisLabels"
                    [value]="{ value: settings.showAxisLabels }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <lib-setting-tool-switch
                    [label]="'Hiển thị đơn vị π'"
                    field="usePiGrid"
                    [value]="{ value: settings.usePiGrid }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <lib-setting-tool-switch
                    [label]="'Khung lưới chính'"
                    field="grid"
                    [value]="{ value: settings.grid }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
                <lib-setting-tool-switch
                    [label]="'Khung lưới chi tiết'"
                    field="detailGrid"
                    [value]="{ value: settings.detailGrid }"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-switch>
            </div>
        </div>

        <div class="flex gap-3" *ngIf="!!settings && selectedTab === 'defaultStyles'">
            <div class="flex gap-3 flex-col w-full h-full">
                <span class="text-BW1">Hiển thị đường</span>
                <div class="flex gap-6">
                    <div *ngFor="let lineStyle of lineStyles" class="flex gap-2 items-center">
                        <input
                            type="radio"
                            name="lineStyle"
                            [ngModel]="settings.lineStyle"
                            [value]="lineStyle"
                            (ngModelChange)="onLineChange($event)" />
                        <div class="flex items-center py-2 cursor-pointer" (click)="onLineChange(lineStyle)">
                            <div
                                [class]="
                                    'w-[24px] h-[3px] !border-t-[2px] !border-black equation-line-style_' + lineStyle
                                "></div>
                        </div>
                    </div>
                </div>
                <lib-setting-tool-adjust-number
                    label="Độ dày"
                    [value]="{ value: settings.lineWidth }"
                    field="lineWidth"
                    [min]="1"
                    [max]="10"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-adjust-number>
                <!-- <lib-setting-tool-adjust-number
                    label="Hiển thị nền"
                    [value]="{ value: settings.opacity ?? 100 }"
                    [min]="0"
                    [max]="100"
                    field="opacity"
                    (onChange)="onFieldChange($event)"></lib-setting-tool-adjust-number> -->
            </div>
        </div>
    </div>
</div>
