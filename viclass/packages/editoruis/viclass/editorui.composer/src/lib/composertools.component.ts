import { ConnectionPositionPair, HorizontalConnectionPos, VerticalConnectionPos } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import {
    ComposerCreateDocumentTool,
    ComposerSettingsTool,
    ComposerSettingsToolState,
    ComposerTool,
    ComposerToolBar,
    ComposerToolEventData,
    ComposerToolType,
    DocInsertTool,
    SubEditorManagerToolState,
} from '@viclass/editor.composer';
import { DefaultToolBar, EditorType, ToolEventListener } from '@viclass/editor.core';
import { EditorUIComponent, EditorUILoaderComponent, EditorUILoaderEvent } from '@viclass/editorui.loader';
import { TooltipOptions, initFlowbite } from 'flowbite';
import { BehaviorSubject, Observable, ReplaySubject, Subscription } from 'rxjs';
import { ComposerToolbarBtnStateService } from './composertoolbar-btn-state.service';
import {
    CommunicationEvent,
    ComposerEditorControllerEvent,
    ComposerTools,
    ComposerUISettings,
} from './composertools.models';
import { COMPOSER_UI_SETTINGS } from './composertools.module';
import { SettingFieldChangeEmitterData } from './setting-tool/setting-tool.models';

@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'tb-composertools',
    templateUrl: './composertools.component.html',
})
export class ComposertoolsComponent implements OnInit, OnDestroy, EditorUIComponent {
    show = false;

    private notifier: ReplaySubject<CommunicationEvent<ComposerEditorControllerEvent>>;
    private readonly editor$: BehaviorSubject<ComposerTools> = new BehaviorSubject(null);
    private readonly subscription: Subscription;

    settings$: BehaviorSubject<ComposerSettingsToolState> = new BehaviorSubject(null);

    vAlign: 'top' | 'center' | 'bottom';
    hAlign: 'left' | 'center' | 'right';
    direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    private uiLoader: EditorUILoaderComponent;
    private subUILoader: EditorUILoaderComponent;

    fillColorSubMenuOpened: boolean = false;
    strokeStyleSubMenuOpened: boolean = false;
    lineWeightSubMenuOpened: boolean = false;
    isSubEditorReady: boolean = false;

    focusInsertSubEditor$: Observable<boolean>;

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(COMPOSER_UI_SETTINGS) private uiSettings: ComposerUISettings,
        private btnStateSv: ComposerToolbarBtnStateService,
        private dialog: MatDialog
    ) {
        this.notifier = new ReplaySubject<CommunicationEvent<ComposerEditorControllerEvent>>();
        this.subscription = this.notifier.subscribe(event => this.handleEvents(event));
        this.focusInsertSubEditor$ = this.btnStateSv.isFocusPath('InsertSubEditor');
    }

    ngOnInit(): void {
        initFlowbite();
        this.editor$.subscribe(tools => {
            if (!tools) return;
            tools.toolbar.registerToolListener(ComposertoolsComponent.ComposerToolListener(this));
        });
        this.btnStateSv.addGlobalClickHandler();
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
        this.btnStateSv.removeGlobalClickHandler();
    }

    /**
     * To be called from the UI holder component to init some config for the UI
     */
    loadedBy(uiLoader: EditorUILoaderComponent) {
        this.uiLoader = uiLoader;

        this.vAlign = uiLoader.vAlign;
        this.hAlign = uiLoader.hAlign;
        this.direction = uiLoader.direction;
    }

    private get tools(): ComposerTools {
        return this.editor$.getValue();
    }

    private get toolBar(): ComposerToolBar {
        return this.tools.toolbar;
    }

    private getTool(tool: ComposerToolType): ComposerTool<any, any> {
        return this.toolBar.getTool(tool);
    }

    private get viewport(): string {
        return this.toolBar.viewport?.id;
    }

    get isHorizontal(): boolean {
        return this.direction == 'ltr' || this.direction == 'rtl';
    }

    get isVertical(): boolean {
        return this.direction == 'ttb' || this.direction == 'btt';
    }

    get subMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions();
    }

    get settingsSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(160, -20);
    }

    readonly colorList = ['#FFFFFF', '#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];

    /**
     * Update the current button path on mouse enter to show sub-toolbar.
     * To be called from the button of tb-toolbar-button component.
     */
    onMouseEnter(btnType: string) {
        this.btnStateSv.enterPath(btnType);
    }

    /**
     * Update the current button path on mouse leave to hide sub-toolbar.
     * To be called from the button of tb-toolbar-button component.
     */
    onMouseLeave(btnType: string, event?: PointerEvent) {
        this.btnStateSv.leavePath(btnType, event);
    }

    /**
     * To be called when the UI is loaded to connect the toolbar instance to the UI.
     */
    connectToolbar<T extends DefaultToolBar<any, any>>(toolbar: T) {
        this.editor$.next(new ComposerTools(toolbar as unknown as ComposerToolBar));

        this.isSubEditorReady = true;
    }

    /**
     * Update the current composer settings (for now is only the composer doc padding).
     * To be called from the tool listener
     */
    updateSettings() {
        const settings = this.toolBar.toolState<ComposerSettingsToolState>('ComposerSettingsTool');
        this.settings$.next(settings);
    }

    /**
     * Get the list of EditorTypes that are available as sub-editors. To be used by the UI
     */
    subEditors(): EditorType[] {
        const types: EditorType[] = [];
        for (const lookup of this.uiSettings.subEditorUILookups) {
            types.push(lookup.editorType);
        }
        return types;
    }

    /**
     * Get the icon class of an EditorType of sub-editors. To be used by the UI
     */
    iconClass(edType: EditorType) {
        return this.uiSettings.iconClasses[edType];
    }

    /**
     * Get the label of an EditorType of sub-editors. To be used by the UI
     */
    tooltipLabel(edType: EditorType) {
        switch (edType) {
            case 'FreeDrawingEditor':
                return 'Vẽ tự do';
            case 'GeometryEditor':
                return 'Hình học';
            case 'MathEditor':
                return 'Công thức toán';
            case 'MathGraphEditor':
                return 'Đồ thị hàm số';
            case 'WordEditor':
                return 'Văn bản';
            default:
                return '';
        }
    }

    get createTool(): ComposerCreateDocumentTool | undefined {
        return this.toolBar?.getTool('CreateComposerDocumentTool') as ComposerCreateDocumentTool | undefined;
    }

    /**
     * Check if the toolbar has a specific tool so we can show/hide the button
     */
    hasTool(tool: ComposerToolType) {
        return this.toolBar && this.toolBar.getTool(tool) != null;
    }

    /**
     * Switch to activate another composer tool. To be called from the UI
     */
    switchTool(toolType: ComposerToolType) {
        this.notifier.next({
            source: this,
            eventType: 'switch-tool',
            eventData: toolType,
        });
    }

    /**
     * Handle insert new sub-viewport into the composer document
     */
    insertDocument(edType: EditorType, e: Event) {
        // prevent other default, so that the composer doc doesn't loose focus
        const insertTool = this.getTool('InsertionEditorTool') as DocInsertTool;
        insertTool.insert(edType);

        e.preventDefault();
    }

    /**
     * Check if the tools in active in the current selection.
     * E.g. When selecting a bullet list, the list tool should be active
     */
    isToolActive = (toolType: ComposerToolType, param?: string): boolean => {
        return this.toolBar.isToolActive(toolType);
    };

    /**
     * Check if the tool is enable and can be used
     */
    isToolEnable = (toolType: ComposerToolType): boolean => {
        if (toolType == 'CreateComposerDocumentTool') {
            return !this.toolBar.isToolDisable('CreateComposerDocumentTool');
        }

        return !!this.getTool(toolType)?.focusAble(this.viewport);
    };

    disableUI() {}

    /**
     * Handle hide the toolbar UI
     */
    hideUI() {
        this.toolBar?.clearAllFocus();
        this.show = false;
        this.changeDetectorRef.markForCheck();
    }

    /**
     * Handle show the toolbar UI
     */
    showUI() {
        this.show = true;
        this.changeDetectorRef.markForCheck();
    }

    /**
     * Check if the toolbar UI is showing
     */
    isShowing(): boolean {
        return this.show;
    }

    /**
     * Handle editor controller event e.g. switch tool
     */
    private handleEvents(event: CommunicationEvent<ComposerEditorControllerEvent>) {
        switch (event.eventType) {
            case 'switch-tool': {
                const toolType = event.eventData as ComposerToolType;

                if (!this.toolBar.isToolActive(toolType)) this.toolBar.focus(toolType);
                else this.toolBar.blur(toolType);

                break;
            }
            default:
                break;
        }
    }

    /**
     * Handle events from the sub editor UI to initialize them (e.g. load theme, connect toolbar,...)
     */
    onSubEditorUILoaderEvent(event: EditorUILoaderEvent) {
        switch (event.eventType) {
            case 'loader-initialized':
                this.subUILoader = event.source;
                // create the UI for sub editor
                setTimeout(() =>
                    event.source.loadBaseTheme(this.uiSettings.subEditorUIBaseTheme).then(() => {
                        event.source.createUI(this.uiSettings.subEditorUILookups);
                    })
                );
                break;
            case 'ui-loaded':
                const loadedUI = event.source.getUI(event.state as EditorType);
                const subEditorManagerToolState = this.toolBar.toolState(
                    'SubEditorManagerTool'
                ) as SubEditorManagerToolState;

                if (subEditorManagerToolState.hasEditor(event.state as EditorType)) {
                    loadedUI.connectToolbar(subEditorManagerToolState.getEditorToolbar(event.state as EditorType));
                }
                break;
            case 'all-ui-loaded':
                break;
        }
    }

    /**
     * Check if there is any sub editor UI is showing.
     * In that case we will hide most of the composer toolbar to show the sub editor UI instead
     */
    isShowingSubEditorUI() {
        const subEditorManagerToolState = this.toolBar.toolState('SubEditorManagerTool') as SubEditorManagerToolState;
        return !!subEditorManagerToolState.currentEditor;
    }

    /**
     * Show the configured tooltip on the button
     */
    showTooltip(tooltipId: string) {
        const Tooltip = window['Tooltip'];
        const flowbiteInstances = window['FlowbiteInstances'];

        const Default: TooltipOptions = {
            placement: 'top',
            triggerType: 'hover',
            onShow: () => {},
            onHide: () => {},
            onToggle: () => {},
        };

        const $triggerEl = document.querySelector(`[data-tooltip-target=${tooltipId}]`);
        const $tooltipEl: any = document.getElementById(tooltipId);

        let tooltip = flowbiteInstances.getInstance('Tooltip', tooltipId);

        if (!$tooltipEl) {
            console.error(
                `The tooltip element with id "${tooltipId}" does not exist. Please check the data-tooltip-target attribute.`
            );
        } else if (!tooltip) {
            const triggerType = $triggerEl!.getAttribute('data-tooltip-trigger');
            const placement = $triggerEl!.getAttribute('data-tooltip-placement');

            tooltip = new Tooltip(
                $tooltipEl as HTMLElement,
                $triggerEl as HTMLElement,
                {
                    placement: placement ? placement : Default.placement,
                    triggerType: triggerType ? triggerType : Default.triggerType,
                } as TooltipOptions
            );
        }

        tooltip.show();
    }

    /**
     * Create connection position for the sub menu overlay
     */
    private createSubMenuPositions(mainOffset: number = 50, subOffset: number = 0): ConnectionPositionPair[] {
        const horizontalConnectionPos: HorizontalConnectionPos = 'center';
        const verticalConnectionPos: VerticalConnectionPos = 'center';

        let offsetX: number = 0;
        let offsetY: number = 0;

        if (this.isHorizontal) {
            if (this.vAlign == 'top') {
                offsetY = mainOffset;
                offsetX = subOffset;
            } else {
                offsetY = -mainOffset;
                offsetX = -subOffset;
            }
        } else if (this.isVertical) {
            if (this.hAlign == 'right') {
                offsetX = -mainOffset;
                offsetY = -subOffset;
            } else {
                offsetX = mainOffset;
                offsetY = subOffset;
            }
        }

        return [
            new ConnectionPositionPair(
                {
                    originX: horizontalConnectionPos,
                    originY: verticalConnectionPos,
                },
                {
                    overlayX: horizontalConnectionPos,
                    overlayY: verticalConnectionPos,
                },
                offsetX,
                offsetY
            ),
        ];
    }

    /**
     * Listen and handle events like tool state changes from the toolbar
     */
    private static ComposerToolListener(
        _p: ComposertoolsComponent
    ): ToolEventListener<ComposerToolBar, ComposerToolType> {
        return new (class implements ToolEventListener<ComposerToolBar, ComposerToolType> {
            onEvent(eventData: ComposerToolEventData): ComposerToolEventData {
                if (eventData.toolType === 'SubEditorManagerTool' && _p.subUILoader) {
                    // check if have to show or hide any sub tool
                    const subEditorManagerToolState = _p.toolBar.toolState(
                        'SubEditorManagerTool'
                    ) as SubEditorManagerToolState;
                    if (subEditorManagerToolState.currentEditor) {
                        _p.subUILoader.switchTo(subEditorManagerToolState.currentEditor);
                    } else {
                        _p.subUILoader.hideAll();
                    }
                }

                if (eventData.toolType === 'ComposerSettingsTool') {
                    _p.updateSettings();
                }

                _p.changeDetectorRef.markForCheck();

                return eventData;
            }
        })();
    }

    /**
     * Update composer doc setting on setting field change
     */
    onSettingFieldChange(ev: SettingFieldChangeEmitterData) {
        const settingsTool = this.getTool('ComposerSettingsTool') as ComposerSettingsTool;
        settingsTool.updateSetting(ev.field, ev.value);
    }
}
