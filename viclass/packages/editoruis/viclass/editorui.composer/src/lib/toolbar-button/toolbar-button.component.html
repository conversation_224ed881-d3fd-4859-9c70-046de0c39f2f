<button
    class="v-tool-btn relative"
    [ngClass]="{ active: isActivated }"
    placement="bottom"
    (pointerup)="onBtnClick($event)"
    [ngClass]="{
        active: isActivated,
        '!hidden': hideOnUnallowed && !(isAllow || hasAllowedChildren),
    }"
    [disabled]="!isAllow"
    cdkOverlayOrigin
    #toolbarBtnRef
    (pointerenter)="onBtnMouseEnter()"
    (pointerleave)="onBtnMouseLeave($event)">
    <span class="{{ data.iconClasses }}"></span>
    <ng-content></ng-content>
    <span *ngIf="hasAllowedChildren" class="vcon vcon-composer vcon_more-tools"></span>
</button>

<tooltip-composer *ngIf="!!data.label" [toolTipFor]="toolbarBtnRef" [tooltipContent]="data.label"></tooltip-composer>

<ng-container *ngIf="hasAllowedChildren">
    <ng-template
        cdkConnectedOverlay
        cdkConnectedOverlayPush
        [cdkConnectedOverlayOrigin]="toolbarBtnRef"
        [cdkConnectedOverlayOpen]="stateService.isFocusPath(this.btnPath) | async"
        [cdkConnectedOverlayPositions]="subMenuPositions">
        <div
            class="v-toolbar composer-toolbar"
            (pointerenter)="onBtnMouseEnter(true)"
            (pointerleave)="onBtnMouseLeave($event, true)">
            <div class="v-tool-group">
                <tb-toolbar-button
                    *ngFor="let btnData of data.children"
                    [data]="btnData"
                    [isToolActive]="isToolActive"
                    [subMenuPositions]="subMenuPositions"
                    [allowCheck]="allowCheck"
                    [parentPath]="btnPath"
                    (btnClicked)="onChildBtnClicked($event)"></tb-toolbar-button>
            </div>
        </div>
    </ng-template>
</ng-container>
