import { DragDropModule } from '@angular/cdk/drag-drop';
import { Overlay, OverlayContainer, OverlayModule } from '@angular/cdk/overlay';
import { CommonModule } from '@angular/common';
import { InjectionToken, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EditorUILoaderComponent, EditorUILoaderModule } from '@viclass/editorui.loader';
import { ComposertoolsComponent } from './composertools.component';
import { ComposerUISettings } from './composertools.models';
import {
    SettingToolAdjustNumberComponent,
    SettingToolColorsComponent,
    SettingToolComponent,
    SettingToolSwitchComponent,
} from './setting-tool';
import { ToolbarButtonComponent } from './toolbar-button/toolbar-button.component';
import { TooltipComponent } from './tooltip/tooltip.component';
import { WidthSliderComponent } from './width-slider/width-slider.component';

export const COMPOSER_UI_SETTINGS = new InjectionToken<ComposerUISettings>('composerui.settings');

const uiSettings: ComposerUISettings = {
    subEditorUILookups: [],
    subEditorUIBaseTheme: undefined,
    iconClasses: undefined,
};

@NgModule({
    declarations: [
        ComposertoolsComponent,
        ToolbarButtonComponent,
        SettingToolColorsComponent,
        SettingToolSwitchComponent,
        SettingToolAdjustNumberComponent,
        SettingToolComponent,
    ],
    imports: [
        CommonModule,
        OverlayModule,
        MatAutocompleteModule,
        MatSlideToggleModule,
        ReactiveFormsModule,
        FormsModule,
        DragDropModule,
        EditorUILoaderModule,
        TooltipComponent,
        MatDialogModule,
        WidthSliderComponent,
    ],
    exports: [ComposertoolsComponent],
    providers: [
        {
            provide: COMPOSER_UI_SETTINGS,
            useValue: uiSettings,
        },
        {
            provide: OverlayContainer,
            useFactory: (comp: EditorUILoaderComponent) => {
                return comp.overlayContainer;
            },
            deps: [EditorUILoaderComponent],
        },
        { provide: Overlay, useClass: Overlay },
    ],
})
export class ComposerToolsModule {}
