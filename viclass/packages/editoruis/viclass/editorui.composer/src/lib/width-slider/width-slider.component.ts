import { CommonModule } from '@angular/common';
import {
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    EventEmitter,
    Input,
    OnDestroy,
    Output,
} from '@angular/core';

@Component({
    selector: 'tb-width-slider',
    templateUrl: './width-slider.component.html',
    styleUrls: ['./width-slider.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    standalone: true,
    imports: [CommonModule],
})
export class WidthSliderComponent implements OnDestroy {
    @Input() value: number = 1;
    @Input() minValue: number = 1;
    @Input() maxValue: number = 50;
    @Input() vertical: boolean = false;
    @Input() height: number = 150;
    @Input() fill: string = '#4a86e8';
    @Output() valueChange = new EventEmitter<number>();

    sliderDragging = false;
    lineWidthAdjustTimeout?: number;

    constructor(private changeDetectorRef: ChangeDetectorRef) {}

    ngOnDestroy(): void {
        this.cancelLineWidthAdjust();
    }

    onLineWidthAdjust(increment: number, startPress = true) {
        let newWidth = this.value + increment;

        // Apply min/max constraints
        if (newWidth < this.minValue) {
            newWidth = this.minValue;
        } else if (newWidth > this.maxValue) {
            newWidth = this.maxValue;
        }

        const isChanged = this.value !== newWidth;

        if (isChanged) {
            this.value = newWidth;
            this.valueChange.emit(newWidth);
            this.changeDetectorRef.markForCheck();

            // Set timeout for continuous adjustment when button is held down
            this.lineWidthAdjustTimeout = window.setTimeout(
                () => this.onLineWidthAdjust(increment, false),
                startPress ? 500 : 100
            ) as unknown as number;
        }
    }

    cancelLineWidthAdjust() {
        if (this.lineWidthAdjustTimeout) {
            window.clearTimeout(this.lineWidthAdjustTimeout);
            this.lineWidthAdjustTimeout = undefined;
        }
    }

    onSliderTrackClick(event: PointerEvent) {
        event.preventDefault();
        const trackElement = event.currentTarget as HTMLElement;
        const rect = trackElement.getBoundingClientRect();

        if (this.vertical) {
            const totalHeight = rect.height;
            const clickY = event.clientY - rect.top;

            // Calculate percentage from bottom (0% is bottom, 100% is top)
            const percentFromBottom = Math.max(0, Math.min(100, ((totalHeight - clickY) / totalHeight) * 100));

            // Convert percentage to line width value
            let newWidth = Math.round((percentFromBottom / 100) * this.maxValue);

            // Apply min/max constraints
            if (newWidth < this.minValue) newWidth = this.minValue;
            if (newWidth > this.maxValue) newWidth = this.maxValue;

            this.updateValue(newWidth);
        } else {
            const totalWidth = rect.width;
            const clickX = event.clientX - rect.left;

            // Calculate percentage from left (0% is left, 100% is right)
            const percentFromLeft = Math.max(0, Math.min(100, (clickX / totalWidth) * 100));

            // Convert percentage to line width value
            let newWidth = Math.round((percentFromLeft / 100) * (this.maxValue - this.minValue)) + this.minValue;

            // Apply min/max constraints
            if (newWidth < this.minValue) newWidth = this.minValue;
            if (newWidth > this.maxValue) newWidth = this.maxValue;

            this.updateValue(newWidth);
        }
    }

    startSliderDrag(event: PointerEvent) {
        event.preventDefault();
        event.stopPropagation();

        // Set pointer capture to ensure all pointer events go to the target element
        (event.target as HTMLElement).setPointerCapture(event.pointerId);

        this.sliderDragging = true;

        const trackElement = (event.currentTarget as HTMLElement).parentElement;
        const knobElement = event.currentTarget as HTMLElement;
        const rect = trackElement.getBoundingClientRect();

        const handlePointerMove = (moveEvent: PointerEvent) => {
            if (!this.sliderDragging) return;
            moveEvent.preventDefault();

            if (this.vertical) {
                const totalHeight = rect.height;
                let pointerY = moveEvent.clientY - rect.top;

                // Ensure pointer doesn't go out of bounds
                if (pointerY < 0) pointerY = 0;
                if (pointerY > totalHeight) pointerY = totalHeight;

                // Calculate percentage from bottom (0% is bottom, 100% is top)
                const percentFromBottom = ((totalHeight - pointerY) / totalHeight) * 100;

                // Convert percentage to line width value
                let newWidth = Math.round((percentFromBottom / 100) * this.maxValue);

                // Apply min/max constraints
                if (newWidth < this.minValue) newWidth = this.minValue;
                if (newWidth > this.maxValue) newWidth = this.maxValue;

                this.updateValue(newWidth);
            } else {
                const totalWidth = rect.width;
                let pointerX = moveEvent.clientX - rect.left;

                // Ensure pointer doesn't go out of bounds
                if (pointerX < 0) pointerX = 0;
                if (pointerX > totalWidth) pointerX = totalWidth;

                // Calculate percentage from left (0% is left, 100% is right)
                const percentFromLeft = (pointerX / totalWidth) * 100;

                // Convert percentage to line width value
                let newWidth = Math.round((percentFromLeft / 100) * (this.maxValue - this.minValue)) + this.minValue;

                // Apply min/max constraints
                if (newWidth < this.minValue) newWidth = this.minValue;
                if (newWidth > this.maxValue) newWidth = this.maxValue;

                this.updateValue(newWidth);
            }

            this.changeDetectorRef.markForCheck();
        };

        const handlePointerUp = (upEvent: PointerEvent) => {
            if (!this.sliderDragging) return;

            // Release pointer capture
            try {
                knobElement.releasePointerCapture(event.pointerId);
            } catch (e) {
                // Handle possible errors on older browsers
            }

            this.sliderDragging = false;
            document.removeEventListener('pointermove', handlePointerMove);
            document.removeEventListener('pointerup', handlePointerUp);
            document.removeEventListener('pointercancel', handlePointerUp);
        };

        document.addEventListener('pointermove', handlePointerMove, { passive: false });
        document.addEventListener('pointerup', handlePointerUp);
        document.addEventListener('pointercancel', handlePointerUp);
    }

    updateValue(newWidth: number) {
        this.value = newWidth;
        this.valueChange.emit(newWidth);
        this.changeDetectorRef.markForCheck();
    }
}
