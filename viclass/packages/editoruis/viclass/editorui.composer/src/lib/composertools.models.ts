import { ComposerToolBar, ComposerToolType } from '@viclass/editor.composer';
import { EditorType, ModuleLookup } from '@viclass/editor.core';
import { EditorUILookup } from '@viclass/editorui.loader';

export interface CommunicationEvent<T> {
    source: any;
    eventType: T;
    eventData: any;
}

export type ComposerEditorControllerEvent = 'switch-tool';

export class ComposerTools {
    toolbar: ComposerToolBar;

    constructor(toolBar: ComposerToolBar) {
        this.toolbar = toolBar;
    }

    activeTool(): ComposerToolType {
        return this.toolbar.activeTool?.toolType;
    }
}

export interface ComposerUISettings {
    subEditorUILookups: EditorUILookup[];
    subEditorUIBaseTheme: ModuleLookup;
    iconClasses: Partial<{ [key in EditorType]: string }>;
}

export type ButtonData<T> = {
    name: T;
    iconClasses: string;
    param?: string;
    onClick?: (btnName: T) => void;
    label?: string;
    children?: ButtonData<T>[];
};
