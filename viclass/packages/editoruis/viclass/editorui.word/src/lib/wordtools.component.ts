import { ConnectionPositionPair, HorizontalConnectionPos, VerticalConnectionPos } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { DefaultToolBar, EditorType, ToolEventListener } from '@viclass/editor.core';
import {
    AlignmentType,
    BlockTypeTool,
    DocInsertTool,
    FormatTextTool,
    HeadingWrapperTool,
    LayoutTool,
    ListTool,
    SubEditorManagerToolState,
    TableContextState,
    TextStylingTool,
    WordAlignmentTool,
    WordContextState,
    WordCreateDocumentTool,
    WordSettingsTool,
    WordSettingsToolState,
    WordTableTool,
    WordTool,
    WordToolBar,
    WordToolEventData,
    WordToolType,
} from '@viclass/editor.word';
import { HeadingWrapperType } from '@viclass/editor.word.transform';
import { EditorUIComponent, EditorUILoaderComponent, EditorUILoaderEvent } from '@viclass/editorui.loader';
import { TooltipOptions, initFlowbite } from 'flowbite';
import { BehaviorSubject, Observable, ReplaySubject, Subscription, map } from 'rxjs';
import { SettingFieldChangeEmitterData } from './setting-tool/setting-tool.models';
import { GridSize } from './table-size-selector/table-size-selector.component';
import { WordToolbarBtnStateService } from './wordtoolbar-btn-state.service';
import {
    ButtonData,
    CommunicationEvent,
    WordEditorControllerEvent,
    WordTools,
    WordUISettings,
} from './wordtools.models';
import { WORD_UI_SETTINGS } from './wordtools.module';

const AlignmentIcons = {
    left: 'vcon vcon-word vcon_text-align_left',
    center: 'vcon vcon-word vcon_text-align_center',
    right: 'vcon vcon-word vcon_text-align_right',
    justify: 'vcon vcon-word vcon_text-align_justify',
};

const parseFontSize = (fontSize: string) => {
    return Number(fontSize.replace('px', ''));
};

@Component({
    changeDetection: ChangeDetectionStrategy.OnPush,
    selector: 'tb-wordtools',
    templateUrl: './wordtools.component.html',
})
export class WordtoolsComponent implements OnInit, OnDestroy, EditorUIComponent {
    show = false;

    private notifier: ReplaySubject<CommunicationEvent<WordEditorControllerEvent>>;
    private readonly editor$: BehaviorSubject<WordTools> = new BehaviorSubject(null);
    private readonly subscription: Subscription;

    wordContext$: BehaviorSubject<WordContextState> = new BehaviorSubject(null);
    focusedDocIdCtx$ = this.wordContext$.pipe(map(ctx => ctx?.focusedDocId || ''));
    textColorCtx$ = this.wordContext$.pipe(map(ctx => ctx?.fontColor || '#000'));
    highlightColorCtx$ = this.wordContext$.pipe(map(ctx => ctx?.backgroundColor || 'transparent'));
    fontFamilyCtx$ = this.wordContext$.pipe(map(ctx => ctx?.fontFamily || 'Montserrat'));
    fontSizeCtx$ = this.wordContext$.pipe(map(ctx => parseFontSize(ctx?.fontSize || '16px')));
    blockType$ = this.wordContext$.pipe(map(ctx => ctx?.blockType || 'paragraph'));

    tableContext$: BehaviorSubject<TableContextState> = new BehaviorSubject(null);
    settings$: BehaviorSubject<WordSettingsToolState> = new BehaviorSubject(null);

    // BehaviorSubjects for overlay management
    showTableCellEditOverlay$: BehaviorSubject<boolean> = new BehaviorSubject(false);
    showTableLayoutEditOverlay$: BehaviorSubject<boolean> = new BehaviorSubject(false);

    get cellBackgroundColor(): string {
        return this.tableContext$.value?.currentCellBgColor || 'transparent';
    }

    vAlign: 'top' | 'center' | 'bottom';
    hAlign: 'left' | 'center' | 'right';
    direction: 'ltr' | 'rtl' | 'btt' | 'ttb';

    private uiLoader: EditorUILoaderComponent;
    private subUILoader: EditorUILoaderComponent;

    fillColorSubMenuOpened: boolean = false;
    strokeStyleSubMenuOpened: boolean = false;
    lineWeightSubMenuOpened: boolean = false;
    isSubEditorReady: boolean = false;

    alignmentBtn: ButtonData<WordToolType> = {
        name: 'WordAlignmentTool',
        param: 'left',
        iconClasses: AlignmentIcons.left,
        label: 'Canh lề',
        children: [
            {
                name: 'WordAlignmentTool',
                param: 'left',
                iconClasses: AlignmentIcons.left,
                label: 'Canh trái',
            },
            {
                name: 'WordAlignmentTool',
                param: 'center',
                iconClasses: AlignmentIcons.center,
                label: 'Canh giữa',
            },
            {
                name: 'WordAlignmentTool',
                param: 'right',
                iconClasses: AlignmentIcons.right,
                label: 'Canh phải',
            },
            {
                name: 'WordAlignmentTool',
                param: 'justify',
                iconClasses: AlignmentIcons.justify,
                label: 'Canh đều',
            },
        ],
    };

    layoutTypeBtn: ButtonData<WordToolType> = {
        name: 'LayoutTool',
        iconClasses: 'vcon vcon-word vcon_2column-layout1',
        label: 'Tạo bố cục',
        children: [
            {
                name: 'LayoutTool',
                iconClasses: 'vcon vcon-word vcon_2column-layout1',
                label: '2 cột (bằng nhau)',
                param: '1fr 1fr',
            },
            {
                name: 'LayoutTool',
                iconClasses: 'vcon vcon-word vcon_2column-layout2',
                label: '2 cột (25%-75%)',
                param: '1fr 3fr',
            },
            {
                name: 'LayoutTool',
                iconClasses: 'vcon vcon-word vcon_3column-layout1',
                label: '3 cột (bằng nhau)',
                param: '1fr 1fr 1fr',
            },
            {
                name: 'LayoutTool',
                iconClasses: 'vcon vcon-word vcon_3column-layout2',
                label: '3 cột (25%-50%-25%)',
                param: '1fr 2fr 1fr',
            },
            {
                name: 'LayoutTool',
                iconClasses: 'vcon vcon-word vcon_4column-layout',
                label: '4 cột (bằng nhau)',
                param: '1fr 1fr 1fr 1fr',
            },
        ],
    };

    fontFamilies: string[] = ['Montserrat', 'Time New Roman', 'Noto Sans', 'Pacifico'];
    headingList: {
        label: String;
        type: HeadingWrapperType;
    }[] = [
        {
            label: 'Nội dung 1',
            type: 'normal-1',
        },
        {
            label: 'Nội dung 2',
            type: 'normal-2',
        },
        {
            label: 'Tiêu đề 1',
            type: 'title-1',
        },
        {
            label: 'Tiêu đề 2',
            type: 'title-2',
        },
        {
            label: 'Tiêu đề 3',
            type: 'title-3',
        },
        {
            label: 'Tiêu đề 4',
            type: 'title-4',
        },
    ];

    focusInsertSubEditor$: Observable<boolean>;
    focusStyleNFormat$: Observable<boolean>;
    focusTextColor$: Observable<boolean>;
    focusHighlightColor$: Observable<boolean>;
    focusFont$: Observable<boolean>;
    focusHeading$: Observable<boolean>;
    focusCreateTable$: Observable<boolean>;
    focusTableActions$: Observable<boolean>;
    focusTableCellColor$: Observable<boolean>;
    focusTableBorderColorWidth$: Observable<boolean>;
    focusTableBorderType$: Observable<boolean>;
    focusTableCellEdit$: Observable<boolean>;
    focusList$: Observable<boolean>;
    listBtnChildren: {
        name: WordToolType;
        param: string;
        iconClasses: string;
    }[] = [
        {
            name: 'ListTool',
            param: 'none',
            iconClasses: 'vcon vcon-word vcon_bullet_none',
        },
        {
            name: 'ListTool',
            param: 'bullet',
            iconClasses: 'vcon vcon-word vcon_bullet_point',
        },
        {
            name: 'ListTool',
            param: 'bullet-pinwheel',
            iconClasses: 'vcon vcon-word vcon_bullet_pinwheel',
        },
        {
            name: 'ListTool',
            param: 'bullet-star',
            iconClasses: 'vcon vcon-word vcon_bullet_star',
        },
        {
            name: 'ListTool',
            param: 'bullet-plus',
            iconClasses: 'vcon vcon-word vcon_bullet_plus',
        },
        {
            name: 'ListTool',
            param: 'number-upercase',
            iconClasses: 'vcon vcon-word vcon_bullet_upercase',
        },
        {
            name: 'ListTool',
            param: 'number-lowercase',
            iconClasses: 'vcon vcon-word vcon_bullet_lowercase',
        },
        {
            name: 'ListTool',
            param: 'number-roman',
            iconClasses: 'vcon vcon-word vcon_bullet_roman-number',
        },
        {
            name: 'ListTool',
            param: 'number',
            iconClasses: 'vcon vcon-word vcon_bullet_number',
        },
    ];

    constructor(
        private changeDetectorRef: ChangeDetectorRef,
        @Inject(WORD_UI_SETTINGS) private uiSettings: WordUISettings,
        private btnStateSv: WordToolbarBtnStateService,
        private dialog: MatDialog
    ) {
        this.notifier = new ReplaySubject<CommunicationEvent<WordEditorControllerEvent>>();
        this.subscription = this.notifier.subscribe(event => this.handleEvents(event));
        this.focusInsertSubEditor$ = this.btnStateSv.isFocusPath('InsertSubEditor');
        this.focusStyleNFormat$ = this.btnStateSv.isFocusPath('StyleFormat');
        this.focusTextColor$ = this.btnStateSv.isFocusPath('StyleFormat.TextColor');
        this.focusHighlightColor$ = this.btnStateSv.isFocusPath('StyleFormat.HighlightColor');
        this.focusFont$ = this.btnStateSv.isFocusPath('Font');
        this.focusHeading$ = this.btnStateSv.isFocusPath('Heading');
        this.focusCreateTable$ = this.btnStateSv.isFocusPath('CreateTable');
        this.focusTableActions$ = this.btnStateSv.isFocusPath('TableActions');
        this.focusTableCellColor$ = this.btnStateSv.isFocusPath('TableActions.TableCellColor');
        this.focusTableBorderColorWidth$ = this.btnStateSv.isFocusPath('TableActions.BorderColorWidth');
        this.focusTableBorderType$ = this.btnStateSv.isFocusPath('TableActions.TableBorderType');
        this.focusList$ = this.btnStateSv.isFocusPath('List');
    }

    ngOnInit(): void {
        initFlowbite();
        this.editor$.subscribe(tools => {
            if (!tools) return;
            tools.toolbar.registerToolListener(WordtoolsComponent.WordToolListener(this));
        });
        this.btnStateSv.addGlobalClickHandler();
    }

    ngOnDestroy(): void {
        this.subscription?.unsubscribe();
        this.btnStateSv.removeGlobalClickHandler();
    }

    /**
     * To be called from the UI holder component to init some config for the UI
     */
    loadedBy(uiLoader: EditorUILoaderComponent) {
        this.uiLoader = uiLoader;

        this.vAlign = uiLoader.vAlign;
        this.hAlign = uiLoader.hAlign;
        this.direction = uiLoader.direction;
    }

    private get tools(): WordTools {
        return this.editor$.getValue();
    }

    private get toolBar(): WordToolBar {
        return this.tools.toolbar;
    }

    private getTool(tool: WordToolType): WordTool<any, any> {
        return this.toolBar.getTool(tool);
    }

    private get viewport(): string {
        return this.toolBar.viewport?.id;
    }

    get formatTextTool(): FormatTextTool {
        return this.getTool('FormatTextTool') as FormatTextTool;
    }

    get textStylingTool(): TextStylingTool {
        return this.getTool('TextStylingTool') as TextStylingTool;
    }

    get headingTool(): BlockTypeTool {
        return this.getTool('BlockTypeTool') as BlockTypeTool;
    }

    get headingWrapperTool(): HeadingWrapperTool {
        return this.getTool('HeadingWrapperTool') as HeadingWrapperTool;
    }

    get tableTool(): WordTableTool {
        return this.getTool('WordTableTool') as WordTableTool;
    }

    get isHorizontal(): boolean {
        return this.direction == 'ltr' || this.direction == 'rtl';
    }

    get isVertical(): boolean {
        return this.direction == 'ttb' || this.direction == 'btt';
    }

    get listSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(50, -160);
    }

    get subMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions();
    }

    get x2GroupSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(70);
    }

    get headingSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(110);
    }

    get fontSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(110);
    }

    get settingsSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(160, -20);
    }

    get tableSubMenuPositions(): ConnectionPositionPair[] {
        return this.createSubMenuPositions(180, -50);
    }

    readonly colorList = ['#FFFFFF', '#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];

    readonly highlightColorList = ['#FFFAE0', '#EDFF82', '#FFDDB6', '#BDFFD0', '#C3DFFF'];

    // prettier-ignore
    readonly tableCellBgColorList =  ['#FFFFFF', '#FFFAE0', '#EDFF82', '#FFDDB6', '#BDFFD0', '#C3DFFF', '#121414', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];

    /**
     * Update the current button path on mouse enter to show sub-toolbar.
     * To be called from the button of tb-toolbar-button component.
     */
    onMouseEnter(btnType: string) {
        this.btnStateSv.enterPath(btnType);
    }

    /**
     * Update the current button path on mouse leave to hide sub-toolbar.
     * To be called from the button of tb-toolbar-button component.
     */
    onMouseLeave(btnType: string, event?: PointerEvent) {
        this.btnStateSv.leavePath(btnType, event);
    }

    /**
     * To be called when the UI is loaded to connect the toolbar instance to the UI.
     */
    connectToolbar<T extends DefaultToolBar<any, any>>(toolbar: T) {
        this.editor$.next(new WordTools(toolbar as unknown as WordToolBar));

        this.isSubEditorReady = true;
    }

    /**
     * Update the current word context (e.g. text format, alignment, styling,...).
     * To be called from the tool listener
     */
    updateWordContext() {
        const context = this.toolBar.toolState<WordContextState>('WordContextTool');
        if (!context) return;

        this.wordContext$.next(context);

        // update alignment button
        const alignment = context.formatType in AlignmentIcons ? context.formatType : 'left';
        this.alignmentBtn.param = alignment;
        this.alignmentBtn.iconClasses = AlignmentIcons[alignment];
    }

    /**
     * Update the current table context (e.g. table selection context, cell context).
     * To be called from the tool listener
     */
    updateTableContext() {
        const context = this.toolBar.toolState<TableContextState>('WordTableTool');
        if (!context) return;

        this.tableContext$.next(context);

        // Auto hide table overlays when not in table editing mode
        if (!context.isTableEditing) {
            this.showTableCellEditOverlay$.next(false);
            this.showTableLayoutEditOverlay$.next(false);
        }
    }

    /**
     * Update the current word settings (for now is only the word doc padding).
     * To be called from the tool listener
     */
    updateSettings() {
        const settings = this.toolBar.toolState<WordSettingsToolState>('WordSettingsTool');
        this.settings$.next(settings);
    }

    /**
     * Get the list of EditorTypes that are available as sub-editors. To be used by the UI
     */
    subEditors(): EditorType[] {
        const types = [];
        for (const lookup of this.uiSettings.subEditorUILookups) {
            types.push(lookup.editorType);
        }
        return types;
    }

    /**
     * Get the icon class of an EditorType of sub-editors. To be used by the UI
     */
    iconClass(edType: EditorType) {
        return this.uiSettings.iconClasses[edType];
    }

    /**
     * Get the label of an EditorType of sub-editors. To be used by the UI
     */
    tooltipLabel(edType: EditorType) {
        switch (edType) {
            case 'FreeDrawingEditor':
                return 'Vẽ tự do';
            case 'GeometryEditor':
                return 'Hình học';
            case 'MathEditor':
                return 'Công thức toán';
            case 'MathGraphEditor':
                return 'Đồ thị hàm số';
            default:
                return '';
        }
    }

    get createTool(): WordCreateDocumentTool | undefined {
        return this.toolBar?.getTool('CreateWordDocumentTool') as WordCreateDocumentTool | undefined;
    }

    /**
     * Check if the toolbar has a specific tool so we can show/hide the button
     */
    hasTool(tool: WordToolType) {
        return this.toolBar && this.toolBar.getTool(tool) != null;
    }

    /**
     * Switch to activate another word tool. To be called from the UI
     */
    switchTool(toolType: WordToolType) {
        if (toolType === 'WordSettingsTool' && !this.isToolActive('WordSettingsTool')) {
            // when turn on WordSettingsTool, close all table overlays
            this.closeTableCellEditOverlay();
            this.closeTableLayoutEditOverlay();
        }

        this.notifier.next({
            source: this,
            eventType: 'switch-tool',
            eventData: toolType,
        });
    }

    /**
     * Toggle table cell edit overlay
     */
    toggleTableCellEditOverlay() {
        // Hide WordSettingsTool if active
        if (this.isToolActive('WordSettingsTool')) {
            this.switchTool('WordSettingsTool');
        }

        this.showTableLayoutEditOverlay$.next(false);

        const currentState = this.showTableCellEditOverlay$.value;
        this.showTableCellEditOverlay$.next(!currentState);
    }

    /**
     * Toggle table layout edit overlay
     */
    toggleTableLayoutEditOverlay() {
        // Hide WordSettingsTool if active
        if (this.isToolActive('WordSettingsTool')) {
            this.switchTool('WordSettingsTool');
        }

        this.showTableCellEditOverlay$.next(false);

        const currentState = this.showTableLayoutEditOverlay$.value;
        this.showTableLayoutEditOverlay$.next(!currentState);
    }

    /**
     * Close table cell edit overlay
     */
    closeTableCellEditOverlay() {
        this.showTableCellEditOverlay$.next(false);
    }

    /**
     * Close table layout edit overlay
     */
    closeTableLayoutEditOverlay() {
        this.showTableLayoutEditOverlay$.next(false);
    }

    /**
     * Handle insert new sub-viewport into the word document
     */
    insertDocument(edType: EditorType, e: Event) {
        // prevent other default, so that the word doc doesn't loose focus
        const insertTool = this.getTool('InsertionEditorTool') as DocInsertTool;
        insertTool.insert(edType);

        e.preventDefault();
    }

    /**
     * Horizontally align the selected text/content
     */
    onAlignmentBtnClick(btn: ButtonData<WordToolType>) {
        if (btn.name !== 'WordAlignmentTool') return;

        const alignTool = this.toolBar.getTool('WordAlignmentTool') as WordAlignmentTool;
        alignTool.align(btn.param as AlignmentType);
    }

    /**
     * Insert a new layout into the document (similar to the Columns feature in Microsoft Word)
     */
    onInsertLayout(btn: ButtonData<WordToolType>) {
        if (btn.name !== 'LayoutTool') return;

        const layoutTool = this.getTool('LayoutTool') as LayoutTool;
        layoutTool.insertLayout(btn.param!);
    }

    /**
     * Insert a new table into the document
     */
    insertTable(size: GridSize) {
        this.tableTool.insertTable(size.col, size.row);
        this.btnStateSv.clearPath();
    }

    /**
     * Handle click event of the list buttons to toggle bullet/number list on the current selection
     */
    onListBtnClick(btn: ButtonData<WordToolType>) {
        const listTool = this.toolBar.getTool('ListTool') as ListTool;
        const isListNode = ['bullet', 'number'].includes(this.wordContext$.value?.blockType);
        if (isListNode && btn.param?.length > 0 && btn.param === 'none') {
            listTool.removeList();
            return;
        }
        if (btn.param?.startsWith('number')) listTool.insertOrderedList(btn.param);
        else if (btn.param?.startsWith('bullet')) listTool.insertUnorderedList(btn.param);
    }

    /**
     * Change color of the selected text
     */
    onSelectTextColor(color: string) {
        this.textStylingTool.changeTextColor(color);
    }

    /**
     * Change highlight color (background) of the selected text
     */
    onSelectHighlightColor(color?: string) {
        this.textStylingTool.changeBackgroundColor(color);
    }

    /**
     * Change font size of the selected text
     */
    onFontSizeChange(fontSize: number) {
        this.textStylingTool.changeFontSize(fontSize);
    }

    /**
     * Change font family of the selected text
     */
    onFontFamilyChange(fontFamily: string) {
        this.textStylingTool.changeFontFamily(fontFamily);
    }

    /**
     * Check if the tools in active in the current selection.
     * E.g. When selecting a bullet list, the list tool should be active
     */
    isToolActive = (toolType: WordToolType, param?: string): boolean => {
        // active state of the btn based on word context
        const context = this.wordContext$.value;
        if (context) {
            switch (toolType) {
                case 'ListTool':
                    if (!param) return ['bullet', 'number'].includes(context.blockType);
                    return context.list?.listStyleType === param;
                case 'WordAlignmentTool':
                    return context.formatType === param;
            }
        }

        return this.toolBar.isToolActive(toolType);
    };

    /**
     * Check if the tool is enable and can be used
     */
    isToolEnable = (toolType: WordToolType): boolean => {
        if (toolType == 'CreateWordDocumentTool') {
            return !this.toolBar.isToolDisable('CreateWordDocumentTool');
        }

        return !!this.getTool(toolType)?.focusAble(this.viewport);
    };

    disableUI() {}

    /**
     * Handle hide the toolbar UI
     */
    hideUI() {
        this.toolBar?.clearAllFocus();
        this.show = false;
        this.changeDetectorRef.markForCheck();
    }

    /**
     * Handle show the toolbar UI
     */
    showUI() {
        this.show = true;
        this.changeDetectorRef.markForCheck();
    }

    /**
     * Check if the toolbar UI is showing
     */
    isShowing(): boolean {
        return this.show;
    }

    /**
     * Handle editor controller event e.g. switch tool
     */
    private handleEvents(event: CommunicationEvent<WordEditorControllerEvent>) {
        switch (event.eventType) {
            case 'switch-tool': {
                const toolType = event.eventData as WordToolType;

                if (!this.toolBar.isToolActive(toolType)) this.toolBar.focus(toolType);
                else this.toolBar.blur(toolType);

                break;
            }
            default:
                break;
        }
    }

    /**
     * Handle events from the sub editor UI to initialize them (e.g. load theme, connect toolbar,...)
     */
    onSubEditorUILoaderEvent(event: EditorUILoaderEvent) {
        switch (event.eventType) {
            case 'loader-initialized':
                this.subUILoader = event.source;
                // create the UI for sub editor
                setTimeout(() =>
                    event.source.loadBaseTheme(this.uiSettings.subEditorUIBaseTheme).then(() => {
                        event.source.createUI(this.uiSettings.subEditorUILookups);
                    })
                );
                break;
            case 'ui-loaded':
                const loadedUI = event.source.getUI(event.state as EditorType);
                const subEditorManagerToolState = this.toolBar.toolState(
                    'SubEditorManagerTool'
                ) as SubEditorManagerToolState;

                if (subEditorManagerToolState.hasEditor(event.state as EditorType)) {
                    loadedUI.connectToolbar(subEditorManagerToolState.getEditorToolbar(event.state as EditorType));
                }
                break;
            case 'all-ui-loaded':
                break;
        }
    }

    /**
     * Check if there is any sub editor UI is showing.
     * In that case we will hide most of the word toolbar to show the sub editor UI instead
     */
    isShowingSubEditorUI() {
        const subEditorManagerToolState = this.toolBar.toolState('SubEditorManagerTool') as SubEditorManagerToolState;
        return !!subEditorManagerToolState.currentEditor;
    }

    /**
     * Show the configured tooltip on the button
     */
    showTooltip(tooltipId: string) {
        const Tooltip = window['Tooltip'];
        const flowbiteInstances = window['FlowbiteInstances'];

        const Default: TooltipOptions = {
            placement: 'top',
            triggerType: 'hover',
            onShow: () => {},
            onHide: () => {},
            onToggle: () => {},
        };

        const $triggerEl = document.querySelector(`[data-tooltip-target=${tooltipId}]`);
        const $tooltipEl: any = document.getElementById(tooltipId);

        let tooltip = flowbiteInstances.getInstance('Tooltip', tooltipId);

        if (!$tooltipEl) {
            console.error(
                `The tooltip element with id "${tooltipId}" does not exist. Please check the data-tooltip-target attribute.`
            );
        } else if (!tooltip) {
            const triggerType = $triggerEl.getAttribute('data-tooltip-trigger');
            const placement = $triggerEl.getAttribute('data-tooltip-placement');

            tooltip = new Tooltip(
                $tooltipEl as HTMLElement,
                $triggerEl as HTMLElement,
                {
                    placement: placement ? placement : Default.placement,
                    triggerType: triggerType ? triggerType : Default.triggerType,
                } as TooltipOptions
            );
        }

        tooltip.show();
    }

    /**
     * Create connection position for the sub menu overlay
     */
    private createSubMenuPositions(mainOffset: number = 50, subOffset: number = 0): ConnectionPositionPair[] {
        const horizontalConnectionPos: HorizontalConnectionPos = 'center';
        const verticalConnectionPos: VerticalConnectionPos = 'center';

        let offsetX: number = 0;
        let offsetY: number = 0;

        if (this.isHorizontal) {
            if (this.vAlign == 'top') {
                offsetY = mainOffset;
                offsetX = subOffset;
            } else {
                offsetY = -mainOffset;
                offsetX = -subOffset;
            }
        } else if (this.isVertical) {
            if (this.hAlign == 'right') {
                offsetX = -mainOffset;
                offsetY = -subOffset;
            } else {
                offsetX = mainOffset;
                offsetY = subOffset;
            }
        }

        return [
            new ConnectionPositionPair(
                {
                    originX: horizontalConnectionPos,
                    originY: verticalConnectionPos,
                },
                {
                    overlayX: horizontalConnectionPos,
                    overlayY: verticalConnectionPos,
                },
                offsetX,
                offsetY
            ),
        ];
    }

    /**
     * Listen and handle events like tool state changes from the toolbar
     */
    private static WordToolListener(_p: WordtoolsComponent): ToolEventListener<WordToolBar, WordToolType> {
        return new (class implements ToolEventListener<WordToolBar, WordToolType> {
            onEvent(eventData: WordToolEventData): WordToolEventData {
                if (eventData.toolType === 'SubEditorManagerTool' && _p.subUILoader) {
                    // check if have to show or hide any sub tool
                    const subEditorManagerToolState = _p.toolBar.toolState(
                        'SubEditorManagerTool'
                    ) as SubEditorManagerToolState;
                    if (subEditorManagerToolState.currentEditor) {
                        _p.subUILoader.switchTo(subEditorManagerToolState.currentEditor);
                    } else {
                        _p.subUILoader.hideAll();
                    }
                }

                if (eventData.toolType === 'WordContextTool') {
                    _p.updateWordContext();
                }

                if (eventData.toolType === 'WordTableTool') {
                    _p.updateTableContext();
                }

                if (eventData.toolType === 'WordSettingsTool') {
                    _p.updateSettings();
                }

                _p.changeDetectorRef.markForCheck();

                return eventData;
            }
        })();
    }

    /**
     * Update word doc setting on setting field change
     */
    onSettingFieldChange(ev: SettingFieldChangeEmitterData) {
        const settingsTool = this.getTool('WordSettingsTool') as WordSettingsTool;
        settingsTool.updateSetting(ev.field, ev.value);
    }

    /**
     * Replace the selected heading wrapper style with the style from the current word selection context
     */
    replaceHeadingWrapperStyle(wrapperType: HeadingWrapperType) {
        const ctx = this.wordContext$.value;

        this.headingWrapperTool.replaceHeadingStyle(wrapperType, {
            fontFamily: ctx.fontFamily,
            fontSize: ctx.fontSize,
            fontColor: ctx.fontColor,
            backgroundColor: ctx.backgroundColor,
            isBold: ctx.isBold,
            isItalic: ctx.isItalic,
            isUnderline: ctx.isUnderline,
            isStrikethrough: ctx.isStrikethrough,
        });
    }
}
