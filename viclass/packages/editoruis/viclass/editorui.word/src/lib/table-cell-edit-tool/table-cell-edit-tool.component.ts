import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { BorderStyleApplyMode, TableContextState, WordTableTool } from '@viclass/editor.word';

@Component({
    selector: 'lib-table-cell-edit-tool',
    templateUrl: './table-cell-edit-tool.component.html',
    styleUrls: ['./table-cell-edit-tool.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TableCellEditToolComponent {
    @Input()
    tableContext: TableContextState;
    @Input()
    tableTool: WordTableTool;

    @Output()
    onClose = new EventEmitter<void>();

    // Cell border editing properties
    cellBorderColor: string = '#000000';
    cellBorderWidth: number = 1;
    cellBorderType: string = 'solid';

    get cellBackgroundColor(): string {
        return this.tableContext.currentCellBgColor || 'transparent';
    }

    // Cell background color properties
    readonly tableCellBgColorList = [
        '#FFFFFF',
        '#FFFAE0',
        '#EDFF82',
        '#FFDDB6',
        '#BDFFD0',
        '#C3DFFF',
        '#000000',
        '#00AEEF',
        '#DB00FF',
        '#31E37C',
        '#FFD600',
        '#FF7A00',
        '#FF002E',
    ];

    readonly borderTypes = [
        'solid', // solid border
        'dotted', // dotted border
        'dashed', // dashed border
        'double', // double border
        // 'hidden', // The same as "none", except in border conflict resolution for table elements -> this is set through the no-color btn
    ] as const;

    readonly borderColorList = ['#000000', '#00AEEF', '#DB00FF', '#31E37C', '#FFD600', '#FF7A00', '#FF002E'];

    readonly borderApplicationModes = [
        { value: 'none', icon: 'vcon_table_outline_none', label: 'Hủy viền' },
        { value: BorderStyleApplyMode.ALL, icon: 'vcon_table_outline_full', label: 'Tất cả' },
        { value: BorderStyleApplyMode.INNER, icon: 'vcon_table_outline_grid', label: 'Trong' },
        { value: BorderStyleApplyMode.OUTER, icon: 'vcon_table_outline_outer', label: 'Ngoài' },
        { value: BorderStyleApplyMode.LEFT, icon: 'vcon_table_outline_left', label: 'Trái' },
        { value: BorderStyleApplyMode.RIGHT, icon: 'vcon_table_outline_right', label: 'Phải' },
        { value: BorderStyleApplyMode.TOP, icon: 'vcon_table_outline_top', label: 'Trên' },
        { value: BorderStyleApplyMode.BOTTOM, icon: 'vcon_table_outline_bottom', label: 'Dưới' },
    ] as const;

    /**
     * Handle cell border color change in cell edit mode
     */
    onCellBorderColorChange(color: string) {
        this.cellBorderColor = color;
    }

    /**
     * Handle cell border width change in cell edit mode
     */
    onCellBorderWidthChange(width: number) {
        this.cellBorderWidth = width;
    }

    /**
     * Handle cell border type change in cell edit mode
     * @param borderType - The selected border type from radio input
     */
    onCellBorderTypeChange(borderType: string) {
        this.cellBorderType = borderType;

        // For double border, the minimun border width is 3. Otherwise it will be render as a single line border
        if (borderType === 'double' && this.cellBorderWidth < 3) {
            this.cellBorderWidth = 3;
        }
    }

    /**
     * Handle border application mode selection
     */
    onBorderApplicationModeSelect(mode: string) {
        if (mode === 'none') {
            // Apply hidden border
            this.tableTool.changeCellBorderStyle(
                {
                    color: this.cellBorderColor,
                    width: this.cellBorderWidth,
                    type: 'hidden',
                },
                BorderStyleApplyMode.ALL
            );
        } else {
            // Apply visible border
            this.tableTool.changeCellBorderStyle(
                {
                    color: this.cellBorderColor,
                    width: this.cellBorderWidth,
                    type: this.cellBorderType,
                },
                mode as BorderStyleApplyMode
            );
        }
        this.onClose.emit();
    }

    /**
     * Handle cell background color selection
     * @param color - The selected background color, or undefined for transparent
     */
    onSelectCellColor(color?: string) {
        this.tableTool.changeCellBgColor(color || 'transparent');
    }
}
