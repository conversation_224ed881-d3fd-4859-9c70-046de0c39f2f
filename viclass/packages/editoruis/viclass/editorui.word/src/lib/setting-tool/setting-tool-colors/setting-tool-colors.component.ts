import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output } from '@angular/core';
import { SettingFieldChangeEmitterData, SettingFieldValue } from '../setting-tool.models';

@Component({
    selector: 'lib-setting-tool-colors',
    templateUrl: './setting-tool-colors.component.html',
    styleUrls: ['./setting-tool-colors.component.sass'],
    changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SettingToolColorsComponent {
    @Input() label?: string;
    @Input() field: string;
    @Input() colorList: string[];
    @Input() value: SettingFieldValue;
    @Input() disabled?: boolean;
    @Input() maxWidth?: number = 175;

    @Output() onChange = new EventEmitter<SettingFieldChangeEmitterData>();

    /**
     * To be called from the UI to handle a value change
     */
    changeValue(value: string) {
        this.onChange.emit({ field: this.field, value });
    }
}
