import { EditorUILookup } from '@viclass/editorui.loader';
import { Environment, MFEConfRequest, MFEDescription, MFESpec, MFESpecCommonToolUISettings } from 'src/app.model';

const switchToolSettings = {
    availableEditors: ['FreeDrawingEditor', 'WordEditor', 'GeometryEditor', 'MathEditor', 'MathGraphEditor'],
    iconClasses: {
        FreeDrawingEditor: 'vcon_document_freedrawing',
        WordEditor: 'vcon_document_word',
        GeometryEditor: 'vcon_document_geometry',
        MathEditor: 'vcon_document_mathtype',
        MathGraphEditor: 'vcon_document_magh',
    },
};

export default async function (spec: MFESpec, env: Environment, request: MFEConfRequest): Promise<MFEDescription> {
    const uiLookup: EditorUILookup = {
        editorType: 'ClassroomToolsEditor',
        uiImpl: {
            type: 'module',
            remoteName: 'editorui.classroomtools',
            remoteEntry: `${env.scheme}://${env.domain}/modules/mfe/editorui.classroomtools.js`,
            exposedModule: './editorui.classroomtools',
        },
        style: {
            type: 'module',
            remoteName: 'editorui.classroomtools.style',
            remoteEntry: `${env.scheme}://${env.domain}/modules/themes/editorui.classroomtools.style.js`,
            exposedModule: './editorui.classroomtools.style',
        },
    };

    if (spec.ui && spec.ui !== true && spec.ui.settings) {
        const settings = spec.ui.settings as MFESpecCommonToolUISettings;

        if (settings.switch) {
            const lookupSettings = {
                availableEditors: [],
                iconClasses: {},
            };

            for (const e of settings.switch) {
                lookupSettings.availableEditors.push(e);
                lookupSettings.iconClasses[e] = switchToolSettings.iconClasses[e];
            }

            uiLookup.settings = lookupSettings;
        }
    }

    return {
        item: 'ClassroomToolsEditor',
        ui: uiLookup,
    };
}
