import type { EditorLookup, OperationMode } from '@viclass/editor.core';
import { EditorViewportConfig, WordEditorConfig, WordEditorCoordinatorConfig } from '@viclass/editor.word';
import {
    Environment,
    frag,
    MFEConfCreator,
    MFEConfRequest,
    MFEDescription,
    MFESpec,
    MFESpecWordSettings,
} from 'src/app.model';
import * as mapping from '../../../../editor-mapping';

// default configuration for viewport types
// of the editors that are embeddable inside word editor
const viewportConf: { [edType: string]: EditorViewportConfig } = {
    FreeDrawingEditor: {
        vpType: 'board',
        defaultHeight: '200px',
        defaultWidth: '100%',
    },
    GeometryEditor: {
        vpType: 'board',
        defaultWidth: '100%',
        defaultHeight: '300px',
    },
    MathEditor: {
        vpType: 'inline',
        defaultWidth: '100%',
        defaultHeight: '100px',
    },
    MathGraphEditor: {
        vpType: 'board',
        defaultWidth: '100%',
        defaultHeight: '100px',
    },
};

const LOCAL_CONTENT_SUB_EDITORS = ['MathEditor'];
const LOAD_OPTIMIZE_SUB_EDITORS = ['MathEditor'];

const create: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const edLookup: EditorLookup = {
        editorType: 'WordEditor',
        lookup: {
            type: 'module',
            remoteName: 'editor.word',
            remoteEntry: `${env.scheme}://${env.domain}/modules/editor.word/editor.word.js`,
            exposedModule: './editor.word',
        },
        // settings:  // settings should be generated dynamically
    };

    const wcoordConf: WordEditorCoordinatorConfig = {
        syncRouting: true,
        edLookups: [],
        viewport: {},
        editorTypeMapping: {},
        viewportElClass: 'vi-word-editor-viewport',
        id: '',
    };

    // configuration for geo editor and
    if (spec.impl && spec.impl !== true && spec.impl.settings) {
        const settings = spec.impl.settings as MFESpecWordSettings;
        for (const item of settings.embedded) {
            const creator = (await import(frag(`editors/usecases/embed/${item}`))).default as MFEConfCreator;
            // get the default embed configuration for editors to be embedded inside
            // the word editor
            const desc = await creator({ item: item, impl: { useCase: 'embed', settings: {} } }, env, null);

            const embedEdLookup = desc.impl as EditorLookup; // we know these are embedded editor
            if (LOCAL_CONTENT_SUB_EDITORS.includes(embedEdLookup.editorType)) {
                embedEdLookup.settings.operationMode = 'LOCAL';
            }
            if (LOAD_OPTIMIZE_SUB_EDITORS.includes(embedEdLookup.editorType)) {
                embedEdLookup.settings.optimizeLoading = true;
            }

            wcoordConf.edLookups.push(embedEdLookup);
            const edType = embedEdLookup.editorType;
            wcoordConf.viewport[edType] = viewportConf[edType];
            wcoordConf.editorTypeMapping[edType] = mapping.default[edType];
        }
    }

    const wordEditorSettings: Partial<WordEditorConfig> = {
        apiUri: `${env.scheme}://${env.domain}/word`,
        attachmentUri: `${env.scheme}://${env.domain}`,
        wcoordConf: wcoordConf,
        commonTheme: `${env.scheme}://${env.domain}/modules/themes/vi.theme.css`,
        wordTheme: `${env.scheme}://${env.domain}/modules/themes/vi.theme.word.css`,
        operationMode: 'CLOUD' as OperationMode,
    };

    edLookup.settings = wordEditorSettings;

    return {
        item: edLookup.editorType,
        impl: edLookup,
    };
};

export default create;
