import type { EditorLookup } from '@viclass/editor.core';
import { Environment, MFEConfCreator, MFEConfRequest, MFEDescription, MFESpec } from 'src/app.model';

const create: MFEConfCreator = async (
    spec: MFESpec,
    env: Environment,
    request: MFEConfRequest
): Promise<MFEDescription> => {
    const edLookup: EditorLookup = {
        editorType: 'MathEditor',
        lookup: {
            type: 'module',
            remoteName: 'editor.math',
            remoteEntry: `${env.scheme}://${env.domain}/modules/editor.math/editor.math.js`,
            exposedModule: './editor.math',
        },
        settings: {
            apiUri: `${env.scheme}://${env.domain}/math`,
            fontsUri: `${env.scheme}://${env.domain}/static/assets/matheditor/fonts`,
            soundsUri: `${env.scheme}://${env.domain}/static/assets/matheditor/sounds`,
            operationMode: 'LOCAL',
        },
        editorStyles: [
            {
                type: 'module',
                remoteName: 'viclass.math.static',
                remoteEntry: `${env.scheme}://${env.domain}/modules/themes/viclass.math.static.js`,
                exposedModule: './viclass.math.static',
            },
        ],
    };

    return {
        item: edLookup.editorType,
        impl: edLookup,
    };
};

export default create;
