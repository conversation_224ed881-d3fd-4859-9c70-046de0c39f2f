import { BadRequestException, Controller, Logger, Post, Query, Req } from '@nestjs/common';
import { CmdMetaProto } from '@viclass/proto/editor.core';
import {
    ResetHeadingOverridesCmdProto,
    SyncLexicalUpdateCmdProto,
    UpdateDocSettingsCmdProto,
    UpdateHeadingOverridesCmdProto,
    WordCmdTypeProto,
} from '@viclass/proto/editor.word';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { Request } from 'express';
import mongoose from 'mongoose';
import { UpdateBufferCacheService } from 'src/db/buffer.cache';
import { SettingsGatewayService } from 'src/db/settings.gateway.service';
import { YDocDbGatewayService } from 'src/db/ydoc-db.gateway.service';
import { reconnectUint8Array } from 'src/util';

const log = new Logger('ProcessCmdCtrl');

@Controller()
export class ProcessCmdCtrl {
    constructor(
        private readonly yDb: YDocDbGatewayService,
        private readonly settingsService: SettingsGatewayService,
        private readonly cache: UpdateBufferCacheService
    ) {}

    @Post('/cmd')
    async processCommand(@Req() req: Request, @Query('globalId') globalId: string) {
        const cmdBuffer = req.body as Buffer;

        const cmd = Uint8Array.from(cmdBuffer);

        let index = 0;

        const metaLength = cmd[index++];
        const metaArrBuf = cmd.slice(index, index + metaLength);
        index += metaLength;
        const stateData = cmd.slice(index);

        const metaProto = CmdMetaProto.deserializeBinary(metaArrBuf);

        if (metaProto.getChannelCode() != 3) {
            throw new BadRequestException({ message: 'Invalid Channel Code' });
        }
        if (!globalId || !mongoose.isObjectIdOrHexString(globalId))
            throw new BadRequestException({
                message: 'Global Id Not Available',
            });

        switch (metaProto.getCmdType()) {
            case WordCmdTypeProto.SYNC_LEXICAL_UPDATE: {
                const updateCmdState = SyncLexicalUpdateCmdProto.deserializeBinary(stateData);
                await this.saveLexicalUpdate(globalId, updateCmdState);
                return;
            }
            case WordCmdTypeProto.SYNC_LEXICAL_AWARENESS: {
                // do nothing
                return;
            }
            case WordCmdTypeProto.UPDATE_DOC_SETTINGS: {
                const cmdData = UpdateDocSettingsCmdProto.deserializeBinary(stateData);
                const settingObjs = cmdData.getSettingsList() || [];
                const saveSettings = settingObjs.map(setting =>
                    this.settingsService.saveSettings(setting.getDocGlobalId(), setting.getSettingJson())
                );
                await Promise.all(saveSettings);
                break;
            }

            case WordCmdTypeProto.UPDATE_HEADING_OVERRIDES: {
                const cmdData = UpdateHeadingOverridesCmdProto.deserializeBinary(stateData);
                await this.settingsService.saveHeadingOverrides(
                    cmdData.getDocGlobalId(),
                    cmdData.getHeadingOverrideJson()
                );
                break;
            }
            case WordCmdTypeProto.RESET_HEADING_OVERRIDES: {
                const cmdData = ResetHeadingOverridesCmdProto.deserializeBinary(stateData);
                await this.settingsService.resetHeadingOverrides(cmdData.getDocGlobalId());
                break;
            }
            case FCCmdTypeProto.INSERT_DOC:
            case FCCmdTypeProto.INSERT_LAYER:
            case FCCmdTypeProto.PREVIEW_BOUNDARY:
            case FCCmdTypeProto.REMOVE_DOC: {
                log.log(`Ignore command of type ${metaProto.getCmdType()}`);
                // do nothing
                break;
            }
            default:
                throw new BadRequestException({
                    message: `Word command type unknown ${metaProto.getCmdType()}!`,
                });
        }
    }

    async saveLexicalUpdate(docId: string, metaProto: SyncLexicalUpdateCmdProto) {
        if (!metaProto.hasUpdate()) return;

        let update = metaProto.getUpdate_asU8();
        if (metaProto.hasPartialId() && metaProto.getPartialId() > 0) {
            const partialId = metaProto.getPartialId();
            const totalChunks = metaProto.getTotalChunks();

            const buffer = await this.cache.saveBuffer(docId, partialId, metaProto);
            if (buffer.chunks.length < totalChunks) return;

            const sortedChunks = buffer.chunks.sort((a, b) => a.chunkIndex - b.chunkIndex).map(chunk => chunk.buffer);
            update = reconnectUint8Array(sortedChunks);

            this.cache.removeBuffer(docId, partialId);
        }

        const clockNum = await this.yDb.storeDocUpdate(docId, update);
        console.log(`Doc ${docId} updated on clock ${clockNum}`);

        if (clockNum % 100 === 0) {
            console.log(`Doc ${docId} flushed on clock ${clockNum}`);
            await this.yDb.flushDocUpdates(docId);
        }
    }
}
