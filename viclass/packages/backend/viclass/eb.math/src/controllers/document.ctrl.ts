import {
    BadRequestException,
    Body,
    Controller,
    Get,
    InternalServerErrorException,
    Logger,
    Post,
    Query,
} from '@nestjs/common';
import { FetchDocResponse } from '@viclass/editor.math';
import mongoose from 'mongoose';
import { MathDocGatewayService } from 'src/db/mathdoc.gateway.service';

const log = new Logger('MathCtrl');

@Controller()
export class DocumentCtrl {
    constructor(private readonly db: MathDocGatewayService) {}

    @Get('document/fetch')
    async fetchDocumentState(@Query('globalId') id: string): Promise<FetchDocResponse> {
        if (!mongoose.isObjectIdOrHexString(id)) throw new BadRequestException({ message: `Wrong Id ${id}` });
        const rawDoc = await this.db.loadDocument(id);

        return {
            id: id,
            latex: rawDoc.latex,
            version: rawDoc.version,
            value: rawDoc.value,
        };
    }

    @Post('document/create')
    async createDoc(): Promise<FetchDocResponse> {
        log.debug('Creating new math document');

        try {
            const pojo = await this.db.createDocument();
            return {
                id: pojo.id,
                latex: pojo.latex,
                version: pojo.version,
                value: pojo.value,
            };
        } catch (error) {
            log.error(error);
            throw new InternalServerErrorException({
                message: 'Unable to create a new document',
            });
        }
    }

    @Post('documents/duplicate')
    async duplicateDocs(@Body() docGlobalIds: string[]): Promise<object> {
        try {
            const mapping = {};
            log.debug('Duplicate math document');
            for (const globalId of docGlobalIds) {
                if (mongoose.isObjectIdOrHexString(globalId)) {
                    const newDoc = await this.db.duplicateDocument(globalId);

                    mapping[globalId] = newDoc.id;
                    log.debug(`mapping ${globalId} to ${newDoc.id}`);
                    log.debug(`mapping ${JSON.stringify(mapping)}`);
                }
            }
            log.debug(`mapping after duplicate ${JSON.stringify(mapping)}`);
            return { mapping: mapping };
        } catch (error) {
            log.error(error);
            throw new InternalServerErrorException({
                message: 'Unable to duplicate document',
            });
        }
    }
}
