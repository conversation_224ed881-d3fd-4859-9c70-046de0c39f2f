import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderLine,
    RenderLineSegment,
    RenderRay,
    RenderVector,
    RenderVertex,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { repeat, RepeatSelector, SelectedVertex, vertexS, VertexSelector } from '../selectors';
import { NamingElementTool } from '../tools/naming.element.tool';
import { GeometryTool } from './geo.tool';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, remoteConstruct } from './util.tool';

export class CreateLineTool extends GeometryTool<CommonToolState> {
    readonly toolType: GeometryToolType = 'CreateLineTool';

    protected override filterElementFunc = (el: GeoRenderElement) => el.type == 'RenderVertex';
    protected createdCls: new (...args) => RenderLine = RenderLine;
    protected get objNameDisplay() {
        return 'Đường thẳng';
    }

    vertSel: VertexSelector;
    declare selLogic: RepeatSelector<SelectedVertex>;
    pQ = new PreviewQueue();
    renderLine: RenderLine;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.doRegisterPointer();

        this.selLogic = repeat<SelectedVertex>(vertexS(this.pQ, this.pointerHandler.cursor), {
            count: 2,
            onComplete: this.performConstruction.bind(this, this.objNameDisplay),
        });
    }

    override resetState() {
        this.selLogic.reset();
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);
        if (selected && selected.length == 2) {
            const v1 = ((selected[0] as Array<any>).length == 2 ? selected[0][1] : selected[0][0]) as RenderVertex;
            const v2 = ((selected[1] as Array<any>).length == 2 ? selected[1][1] : selected[1][0]) as RenderVertex;
            // trial selection has two vertices
            // use those two vertices to sync the preview line
            this.renderLine = pLine(ctrl, -20, this.createdCls, v1, v2);
            this.pQ.add(this.renderLine);
        }

        this.pQ.flush(ctrl);
    }

    protected async performConstruction(objName: string, selector: RepeatSelector<SelectedVertex>, ctrl: GeoDocCtrl) {
        const needShapeName = this.createdCls != RenderLineSegment;
        const { pcs, points } = await assignNames(
            ctrl,
            selector.selected,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            needShapeName ? '2 điểm mới' : objName,
            needShapeName ? objName : undefined,
            needShapeName ? this.renderLine : undefined
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const lineName =
            this.createdCls == RenderLineSegment ? `${points[0].name}${points[1].name}` : this.renderLine.name;
        const constructionLine = this.buildLineConstruction(lineName, points[0].name, points[1].name);

        try {
            await remoteConstruct(ctrl, constructionLine, pcs, this.editor.geoGateway, objName);
        } finally {
            this.resetState();
        }
    }

    protected buildLineConstruction(name: string, p1: string, p2: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineVi/LineEC', 'LineVi', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: [p1, p2],
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateLineSegmentTool extends CreateLineTool {
    override readonly toolType: GeometryToolType = 'CreateLineSegmentTool';

    protected override get objNameDisplay(): string {
        return 'Đoạn thẳng';
    }
    protected override createdCls: new (...args) => RenderLine = RenderLineSegment;

    protected override buildLineConstruction(name: string, p1: string, p2: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('LineSegment/LineSegmentEC', 'LineSegment', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }
}

export class CreateRayTool extends CreateLineSegmentTool {
    override readonly toolType: GeometryToolType = 'CreateRayTool';

    protected override get objNameDisplay(): string {
        return 'Tia';
    }
    protected override createdCls: new (...args) => RenderLine = RenderRay;

    protected override buildLineConstruction(name: string, p1: string, p2: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Ray/RayEC', 'VectorVi', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: [p1, p2],
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateVectorTool extends CreateLineTool {
    override readonly toolType: GeometryToolType = 'CreateVectorTool';

    protected override get objNameDisplay(): string {
        return 'Vector';
    }
    protected override createdCls: new (...args) => RenderLine = RenderVector;

    protected override buildLineConstruction(name: string, p1: string, p2: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('VectorVi/VectorEC', 'VectorVi', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: [p1, p2],
                    },
                },
            },
        ];

        return construction;
    }
}
