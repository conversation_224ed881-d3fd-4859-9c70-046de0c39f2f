import { point, vector } from '@flatten-js/core';
import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, RenderLineSegment, RenderVertex, SectorToolState } from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue, pSector, pSectorShape } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import {
    circleTransform,
    nPoints,
    or,
    SelectedVertex,
    then,
    ThenSelector,
    vert,
    vertex,
    vertexOnStroke,
} from '../selectors';
import { getFocusDocCtrl } from './util.tool';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, handleIfPointerNotInError, pickShapeName, remoteConstruct } from './util.tool';

export class CreateSectorTool extends GeometryTool<SectorToolState> {
    readonly toolType: GeometryToolType = 'CreateSectorTool';

    pQ = new PreviewQueue();
    declare selLogic?: ThenSelector;
    previewSector: any = null; // Store preview sector for reuse

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();

        // Register keyboard handling for Shift key
        this.registerKeyboardHandling({
            event: 'keyup',
            keys: ['shift'],
            global: false,
        });
    }

    override resetState() {
        this.selLogic.reset();
        this.previewSector = null; // Reset preview sector
        super.resetState();
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.clockwise = !this.toolState.clockwise;
            this.toolbar.update(this.toolType, this.toolState);
            return event;
        }

        super.handleKeyboardEvent(event);
        return event;
    }

    protected createSelLogic() {
        const first2Points = nPoints(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onPartialSelection: (newSel: SelectedVertex, curSel, selector, doc) => {
                return true;
            },
        });

        const endPointSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: el => circleTransform(this.selLogic.selected[0] as SelectedVertex[], el, 0),
                }),
                nPoints(this.pQ, this.pointerHandler.cursor, {
                    count: 1,
                }),
            ],
            {
                flatten: true,
            }
        );

        this.selLogic = then([first2Points, endPointSelector], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl));

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (!selected || selected.length < 2) {
            this.pQ.flush(ctrl);
            return;
        }

        const first2Points = selected[0] as SelectedVertex[];
        const endSelection = selected[1] as SelectedVertex;

        if (!first2Points || first2Points.length < 2) {
            this.pQ.flush(ctrl);
            return;
        }

        const centerPoint = vert(first2Points[0]);
        const startPoint = vert(first2Points[1]);
        const endPoint = vert(endSelection);

        // For preview: swap start and end points based on clockwise direction
        const previewStart = this.toolState.clockwise ? startPoint : endPoint;
        const previewEnd = this.toolState.clockwise ? endPoint : startPoint;

        // Add preview lines from center to start and end points to complete sector outline
        this.pQ.add(pLine(ctrl, -22, RenderLineSegment, centerPoint, previewStart));
        this.pQ.add(pLine(ctrl, -23, RenderLineSegment, centerPoint, previewEnd));

        // Store sector for reuse in performConstruction
        this.previewSector = pSector(ctrl, -20, previewStart, centerPoint, previewEnd);
        this.pQ.add(pSectorShape(ctrl, -21, previewStart, centerPoint, previewEnd, this.previewSector));

        this.pQ.flush(ctrl);
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = selector.selected;
        if (!selected || selected.length < 2) {
            this.resetState();
            return;
        }

        const first2Points = selected[0] as SelectedVertex[];
        const endSelection = selected[1] as SelectedVertex;

        if (!first2Points || first2Points.length < 2) {
            this.resetState();
            return;
        }

        const centerPoint = vert(first2Points[0]);
        const startPoint = vert(first2Points[1]);
        const endPoint = vert(endSelection);

        try {
            const hasStrokeDependency = Array.isArray(endSelection) && endSelection.length === 2;
            const hasName = endPoint.name && endPoint.name.length > 0;
            const isEndPointVirtual = !hasName && !hasStrokeDependency;

            if (isEndPointVirtual) {
                await this.handleAngleBasedApproach(
                    ctrl,
                    selected as [SelectedVertex[], SelectedVertex],
                    centerPoint,
                    startPoint,
                    endPoint
                );
            } else {
                await this.handleThreePointApproach(
                    ctrl,
                    selected as [SelectedVertex[], SelectedVertex],
                    centerPoint,
                    startPoint,
                    endPoint
                );
            }
        } finally {
            this.resetState();
        }
    }

    private async handleAngleBasedApproach(
        ctrl: GeoDocCtrl,
        selected: [SelectedVertex[], SelectedVertex],
        centerPoint: RenderVertex,
        startPoint: RenderVertex,
        endPoint: RenderVertex
    ) {
        const { pcs } = await assignNames(
            ctrl,
            [...selected[0], endPoint], // Include the actual endpoint
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Điểm cung tròn',
            'cung tròn',
            this.previewSector // Use the preview sector from doTrySelection
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        // Get the sector name from the preview sector
        const sectorName = this.previewSector.name || pickShapeName(ctrl, []);

        const pC = point(centerPoint.coords[0], centerPoint.coords[1]);
        const pS = point(startPoint.coords[0], startPoint.coords[1]);
        const pE = point(endPoint.coords[0], endPoint.coords[1]);

        const vecStart = vector(pC, pS);
        const vecEnd = vector(pC, pE);
        let angle = vecStart.angleTo(vecEnd);

        // Adjust angle based on clockwise/counterclockwise direction
        if (!this.toolState.clockwise && angle > 0) {
            angle = angle - 2 * Math.PI; // Convert to negative angle for counterclockwise
        } else if (this.toolState.clockwise && angle < 0) {
            angle = angle + 2 * Math.PI; // Convert to positive angle for clockwise
        }
        const sectorConstruction = new GeoElConstructionRequest(
            'CircularSector/CircularSectorEC',
            'CircularSector',
            'WithCenterAndStartPointAngleRadian'
        );
        sectorConstruction.name = sectorName; // Set sector name
        sectorConstruction.paramSpecs = [
            this.createParamSpec(0, 'aPoint', 'tpl-CenterCircle', centerPoint.name),
            this.createParamSpec(1, 'aPoint', 'tpl-Point', startPoint.name),
            this.createParamSpec(2, 'aValue', 'tpl-AngleRadian', angle),
            this.createParamSpec(3, 'aName', 'tpl-EndPoint', endPoint.name), // Add endpoint name
        ];

        // For angle-based approach, endPoint is virtual and will be created by backend
        // So only include constructions for center and start points
        const pointConstructions = pcs.filter(pc => pc.name === centerPoint.name || pc.name === startPoint.name);

        await remoteConstruct(ctrl, sectorConstruction, pointConstructions, this.editor.geoGateway, 'cung tròn');
    }

    private async handleThreePointApproach(
        ctrl: GeoDocCtrl,
        selected: [SelectedVertex[], SelectedVertex],
        centerPoint: RenderVertex,
        startPoint: RenderVertex,
        endPoint: RenderVertex
    ) {
        const allPoints = [...selected[0], selected[1]];

        const { pcs } = await assignNames(
            ctrl,
            allPoints,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Điểm cung tròn',
            'cung tròn',
            this.previewSector // Use the preview sector from doTrySelection
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        // Get the sector name from the preview sector
        const sectorName = this.previewSector.name || pickShapeName(ctrl, []);

        const sectorConstruction = new GeoElConstructionRequest(
            'CircularSector/CircularSectorEC',
            'CircularSector',
            'ThreePoint'
        );
        sectorConstruction.name = sectorName; // Set sector name
        sectorConstruction.paramSpecs = [
            this.createParamSpec(0, 'aPoint', 'tpl-CenterPoint', centerPoint.name),
            this.createParamSpec(1, 'aPoint', 'tpl-Point', startPoint.name),
            this.createParamSpec(2, 'aPoint', 'tpl-Point', endPoint.name),
            this.createParamSpec(3, 'aValue', 'tpl-Direction', this.toolState.clockwise ? 1 : 0), // Add direction parameter
        ];
        await remoteConstruct(ctrl, sectorConstruction, pcs, this.editor.geoGateway, 'cung tròn');
    }

    private createParamSpec(indexInCG: number, paramDefId: string, tplStrLangId: string, value: string | number) {
        return {
            indexInCG,
            paramDefId,
            optional: false,
            tplStrLangId,
            params:
                paramDefId === 'aValue'
                    ? { value: { type: 'singleValue', value } }
                    : { name: { type: 'singleValue', value } },
        };
    }
}
