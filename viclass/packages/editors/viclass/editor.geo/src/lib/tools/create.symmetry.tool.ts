import { UIPointerEventData } from '@viclass/editor.core';
import { syncRemovePreviewCmd } from '../cmd';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderCircleShape,
    RenderEllipseShape,
    RenderLine,
    RenderPolygon,
    RenderSectorShape,
    RenderVertex,
    SymmetryThroughLineToolState,
    SymmetryThroughPointToolState,
} from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, SelectedVertex, then, ThenSelector, vertexOnStroke } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    buildSymmetryConstruction,
    calculateSymmetricPointsThroughLine,
    calculateSymmetricPointsThroughMiddlePoint,
    createSymmetry,
} from './utils.symmetry.tool';
import {
    assignNames,
    buildPreviewLineRenderProp,
    buildPreviewVertexRenderProp,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    remoteConstruct,
} from './util.tool';

/**
 *
 * <AUTHOR>
 */

/**
 * Base class for symmetry tools that provides common functionality
 * for creating symmetric objects through different reference elements
 */
export abstract class BaseSymmetryTool<T> extends GeometryTool<T> {
    pQ = new PreviewQueue();
    declare selLogic?: ThenSelector;
    protected previousPreviewRelIndexes: number[] = [];
    protected referencePreview?: GeoRenderElement;
    private isConstructing = false;

    // Abstract properties that concrete classes must implement
    abstract readonly symmetryToolType: 'line' | 'point';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    /**
     * Abstract method to get the first selector (reference element)
     * Line tool returns line selector, Point tool returns point selector
     */
    protected abstract getFirstSelector(): any;

    /**
     * Abstract method to get reference element from selection
     * Line tool extracts line, Point tool extracts point
     */
    protected abstract getReferenceElementFromSelection(selection: SelectedVertex): GeoRenderElement | null;

    /**
     * Abstract method to create highlighted preview for reference element
     * Line tool creates red preview line, Point tool creates highlighted preview point
     */
    protected abstract createReferenceElementPreview(
        referenceElement: GeoRenderElement,
        ctrl: GeoDocCtrl
    ): GeoRenderElement;

    /**
     * Common createSelLogic implementation
     * Uses template method pattern - concrete classes define the first selector
     */
    protected createSelLogic() {
        // Get the first selector from concrete class (line or point)
        const firstSelector = this.getFirstSelector();

        // Element selector: choose element to create symmetry of (same for both tools)
        const elementSelector = or(
            [
                vertexOnStroke({
                    selectableStrokeTypes: [
                        'RenderLine',
                        'RenderLineSegment',
                        'RenderRay',
                        'RenderVector',
                        'RenderCircle',
                        'RenderCircleShape',
                        'RenderEllipseShape',
                        'RenderPolygon',
                        'RenderSectorShape',
                        'RenderVertex',
                    ],
                    syncPreview: false,
                }),
            ],
            {
                flatten: true,
            }
        );

        // Always select reference element first, then target element
        this.selLogic = then([firstSelector, elementSelector], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    /**
     * Common doTrySelection implementation
     * Uses template method pattern for reference element handling
     */
    protected async doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        // Always clean up previous previews first

        const selected = this.selLogic.trySelect(event, ctrl);

        // Validate selections
        if (!selected || selected.length < 2) {
            if (!this.isConstructing) await this.cleanupPreviews(ctrl);
            return;
        }

        // Always select reference element first, then target element
        const referenceSelection = selected[0] as SelectedVertex;
        const elementSelection = selected[1] as SelectedVertex;

        if (!referenceSelection || !elementSelection) {
            return;
        }

        // Extract reference element and target element from selections
        const referenceElement = this.getReferenceElementFromSelection(referenceSelection);
        const targetElement = this.getElementFromSelection(elementSelection);

        // Create preview for reference element (line or point highlighting)
        if (referenceElement && this.shouldUpdateReferencePreview(referenceElement)) {
            this.updateSelectedReference(referenceElement);
            const referencePreview = this.createReferenceElementPreview(referenceElement, ctrl);
            this.referencePreview = referencePreview;
            this.pQ.add(referencePreview);

            // Update selection state when reference element is selected
            this.updateSelectionState(true);
        }

        // Create and add symmetry preview if both elements are valid
        if (referenceElement && targetElement) {
            const symmetryResult = this.createSymmetryPreview(referenceElement, targetElement, ctrl);

            if (symmetryResult) {
                this.pQ.add(symmetryResult.element);
                this.previousPreviewRelIndexes.push(...symmetryResult.relIndexes);
            }
        }

        // Flush the new preview elements that were just added
        this.pQ.flush(ctrl);
    }

    /**
     * Common performConstruction implementation
     */
    private async performConstruction(selector: ThenSelector, doc: GeoDocCtrl) {
        const selections = selector.selected;
        if (!selections || selections.length < 2) return;

        // Always select reference element first, then target element
        const referenceSelection = selections[0] as SelectedVertex;
        const elementSelection = selections[1] as SelectedVertex;

        const referenceElement = this.getReferenceElementFromSelection(referenceSelection);
        const targetElement = this.getElementFromSelection(elementSelection);

        if (!referenceElement || !targetElement) {
            return;
        }

        try {
            this.isConstructing = true;
            await this.createSymmetricObject(doc, referenceElement, targetElement);
            this.resetState();
        } catch (error) {
            throw error;
        }
    }

    /**
     * Abstract methods for reference element management
     */
    protected abstract shouldUpdateReferencePreview(referenceElement: GeoRenderElement): boolean;
    protected abstract updateSelectedReference(referenceElement: GeoRenderElement): void;
    protected abstract updateSelectionState(selected: boolean): void;
    protected abstract createSymmetryPreview(
        referenceElement: GeoRenderElement,
        targetElement: GeoRenderElement,
        ctrl: GeoDocCtrl
    ): any;

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        // No keyboard handling needed - always select reference element first
        return event;
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, async event =>
                handleIfPointerNotInError(this, async () => await this.doTrySelection(event, ctrl))
            );
        else handleIfPointerNotInError(this, async () => await this.doTrySelection(event, ctrl));

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    /**
     * Helper function to clean up preview elements and flush preview queue
     */
    protected async cleanupPreviews(ctrl: GeoDocCtrl) {
        // Remove previous preview elements
        if (this.previousPreviewRelIndexes.length > 0) {
            await syncRemovePreviewCmd(this.previousPreviewRelIndexes, ctrl);
            this.previousPreviewRelIndexes = [];
        }
        // Flush preview queue
        this.pQ.flush(ctrl);
    }

    /**
     * Extract target element from selection
     */
    protected getElementFromSelection(selection: SelectedVertex): GeoRenderElement | null {
        return Array.isArray(selection) && selection.length === 2 ? (selection[0] as GeoRenderElement) : null;
    }

    /**
     * Abstract method to get the symmetry calculation function
     */
    protected abstract getSymmetryCalculationFunction(
        referenceElement: GeoRenderElement,
        ctrl: GeoDocCtrl
    ): (coords: number[][]) => number[][];

    /**
     * Abstract method to get the symmetry description for remoteConstruct
     */
    protected abstract getSymmetryDescription(): string;

    /**
     * Abstract method to clear selected reference element
     */
    protected abstract clearSelectedReference(): void;

    /**
     * Common resetState implementation that can be overridden if needed
     */
    override resetState() {
        this.selLogic?.reset();
        this.previousPreviewRelIndexes = [];
        this.isConstructing = false;
        this.clearSelectedReference();
        this.updateSelectionState(false);
        this.toolbar.update(this.toolType, this.toolState);
        super.resetState();
    }

    /**
     * Common implementation for creating symmetric objects
     * Uses template method pattern with abstract methods for tool-specific logic
     */
    protected async createSymmetricObject(
        ctrl: GeoDocCtrl,
        referenceElement: GeoRenderElement,
        targetElement: GeoRenderElement
    ) {
        let construct: GeoElConstructionRequest;
        const sS = targetElement;
        const refElement = referenceElement;

        const getSymmetricPointCoords = this.getSymmetryCalculationFunction(referenceElement, ctrl);

        const tempVertexProps = {
            name: undefined,
            type: 'RenderVertex',
            elType: 'Point',
            renderProp: buildPreviewVertexRenderProp(),
            usable: true,
            valid: true,
        } as const;

        const nt = this.toolbar.getTool('NamingElementTool') as NamingElementTool;

        switch (sS.type) {
            case 'RenderLine':
            case 'RenderLineSegment':
            case 'RenderRay':
            case 'RenderVector': {
                const lineElement = sS as RenderLine;

                const [origP1, origP2] = [
                    ctrl.rendererCtrl.elementAt(lineElement.startPointIdx) as RenderVertex,
                    lineElement.endPointIdx !== null
                        ? (ctrl.rendererCtrl.elementAt(lineElement.endPointIdx) as RenderVertex)
                        : null,
                ];

                const symmetricCoords = getSymmetricPointCoords(
                    origP2 ? [origP1.coords, origP2.coords] : [origP1.coords]
                );
                const [symP1Coords, symP2Coords] = symmetricCoords;

                const tempSymmetricPoints = [
                    { ...tempVertexProps, coords: symP1Coords, relIndex: -1 },
                    symP2Coords ? { ...tempVertexProps, coords: symP2Coords, relIndex: -2 } : null,
                ].filter(Boolean);

                const { pcs } = await assignNames(
                    ctrl,
                    tempSymmetricPoints,
                    nt,
                    'Điểm đường',
                    'đường',
                    this.referencePreview
                );

                if (!pcs) {
                    this.resetState();
                    return;
                }

                construct = buildSymmetryConstruction(
                    sS.name,
                    refElement.name,
                    this.symmetryToolType,
                    'Line',
                    sS.elType
                );
                construct.name = pcs.map(pc => pc.name).join('');
                break;
            }
            case 'RenderVertex': {
                const originalPoint = sS as RenderVertex;
                const [newSymmetricPointCoords] = getSymmetricPointCoords([originalPoint.coords]);
                const tempSymmetricVertex = {
                    ...tempVertexProps,
                    coords: newSymmetricPointCoords,
                    relIndex: -1,
                };

                const { pcs } = await assignNames(ctrl, [tempSymmetricVertex], nt, 'Điểm', 'điểm');

                if (!pcs || pcs.length === 0) {
                    this.resetState();
                    return;
                }

                construct = buildSymmetryConstruction(sS.name, refElement.name, this.symmetryToolType, 'Point');
                construct.name = pcs[0].name;
                break;
            }
            case 'RenderCircleShape': {
                const circleElement = sS as RenderCircleShape;
                const origCenter = ctrl.rendererCtrl.elementAt(circleElement.centerPointIdx) as RenderVertex;
                const [symCenterCoords] = getSymmetricPointCoords([origCenter.coords]);
                const tempSymCenter = { ...tempVertexProps, coords: symCenterCoords, relIndex: -1 };

                const { pcs } = await assignNames(
                    ctrl,
                    [tempSymCenter],
                    nt,
                    'Điểm tâm đường tròn',
                    'đường tròn',
                    this.referencePreview
                );

                if (!pcs || pcs.length === 0) {
                    this.resetState();
                    return;
                }
                construct = buildSymmetryConstruction(sS.name, refElement.name, this.symmetryToolType, 'Circle');
                construct.name = pcs[0].name;
                break;
            }
            case 'RenderEllipseShape': {
                const ellipseElement = sS as RenderEllipseShape;
                const origF1 = ellipseElement.coord('f1', ctrl.rendererCtrl);
                const origF2 = ellipseElement.coord('f2', ctrl.rendererCtrl);

                const symmetricCoords = getSymmetricPointCoords([origF1, origF2]);
                const [symF1Coords, symF2Coords] = symmetricCoords;

                const tempSymmetricFoci = [
                    { ...tempVertexProps, coords: symF1Coords, relIndex: -1 },
                    { ...tempVertexProps, coords: symF2Coords, relIndex: -2 },
                ];

                const { pcs } = await assignNames(
                    ctrl,
                    tempSymmetricFoci,
                    nt,
                    'Điểm elip',
                    'elip',
                    this.referencePreview
                );

                if (!pcs) {
                    this.resetState();
                    return;
                }

                construct = buildSymmetryConstruction(sS.name, refElement.name, this.symmetryToolType, 'Ellipse');
                construct.name = pcs.map(pc => pc.name).join('');
                break;
            }
            case 'RenderPolygon': {
                const polygonElement = sS as RenderPolygon;
                const originalVertices = polygonElement.faces.map(
                    faceRelIdx => ctrl.rendererCtrl.elementAt(faceRelIdx) as RenderVertex
                );
                const origVerticesCoords = originalVertices.map(v => v.coords);
                const symmetricCoords = getSymmetricPointCoords(origVerticesCoords);

                const tempPolygonPlaceholder = symmetricCoords.map((coords, index) => ({
                    ...tempVertexProps,
                    coords: coords,
                    relIndex: -index,
                }));

                const { pcs } = await assignNames(
                    ctrl,
                    tempPolygonPlaceholder,
                    nt,
                    'Điểm đa giác',
                    'đa giác',
                    this.referencePreview
                );

                if (!pcs) {
                    this.resetState();
                    return;
                }

                construct = buildSymmetryConstruction(sS.name, refElement.name, this.symmetryToolType, 'Polygon');
                construct.name = pcs.map(pc => pc.name).join('');
                break;
            }
            case 'RenderSectorShape': {
                const sectorElement = sS as RenderSectorShape;
                const origCenter = ctrl.rendererCtrl.elementAt(sectorElement.centerPointIdx) as RenderVertex;
                const origStart = ctrl.rendererCtrl.elementAt(sectorElement.startPointIdx) as RenderVertex;
                const origEnd = ctrl.rendererCtrl.elementAt(sectorElement.endPointIdx) as RenderVertex;

                const symmetricCoords = getSymmetricPointCoords([origCenter.coords, origStart.coords, origEnd.coords]);
                const [symCenterCoords, symStartCoords, symEndCoords] = symmetricCoords;

                const tempSectorPlaceholders = [
                    { ...tempVertexProps, coords: symCenterCoords, relIndex: -1 },
                    { ...tempVertexProps, coords: symStartCoords, relIndex: -2 },
                    { ...tempVertexProps, coords: symEndCoords, relIndex: -3 },
                ];

                const { pcs } = await assignNames(
                    ctrl,
                    tempSectorPlaceholders,
                    nt,
                    'Điểm cung',
                    'cung',
                    this.referencePreview
                );

                if (!pcs) {
                    this.resetState();
                    return;
                }

                construct = buildSymmetryConstruction(
                    sS.name,
                    refElement.name,
                    this.symmetryToolType,
                    'CircularSector'
                );
                construct.name = pcs.map(pc => pc.name).join('');
                break;
            }
            default:
                this.resetState(); // Reset if type not handled
                return;
        }

        await remoteConstruct(ctrl, construct, [], this.editor.geoGateway, this.getSymmetryDescription());
    }
}

export class CreateSymmetricThroughLineTool extends BaseSymmetryTool<SymmetryThroughLineToolState> {
    readonly toolType: GeometryToolType = 'CreateSymmetricThroughLineTool';
    readonly symmetryToolType = 'line';

    private selectedLine?: RenderLine; // Store the selected line for red preview

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    /**
     * Implementation of abstract method to clear selected reference element
     */
    protected clearSelectedReference(): void {
        this.selectedLine = undefined;
    }

    /**
     * Implementation of abstract method to get the first selector (line selector)
     */
    protected getFirstSelector() {
        return or(
            [
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                    syncPreview: false, // Don't show vertex preview on line selection
                }),
            ],
            {
                flatten: true,
            }
        );
    }

    /**
     * Implementation of abstract method to get reference element from selection
     */
    protected getReferenceElementFromSelection(selection: SelectedVertex): RenderLine | null {
        return Array.isArray(selection) && selection.length === 2
            ? (() => {
                  const strokeElement = selection[0] as GeoRenderElement;
                  return isElementLine(strokeElement) ? (strokeElement as RenderLine) : null;
              })()
            : null;
    }

    /**
     * Implementation of abstract method to create highlighted preview for reference element
     */
    protected createReferenceElementPreview(referenceElement: GeoRenderElement, ctrl: GeoDocCtrl): RenderLine {
        const lineElement = referenceElement as RenderLine;
        const startPoint = ctrl.rendererCtrl.elementAt(lineElement.startPointIdx) as RenderVertex;
        const endPoint =
            lineElement.endPointIdx !== null
                ? (ctrl.rendererCtrl.elementAt(lineElement.endPointIdx) as RenderVertex)
                : null;

        // Create preview line as RenderLine (not line segment)
        const previewLine = pLine(
            ctrl,
            -22, // Use a specific negative index for red preview line
            RenderLine, // Always use RenderLine for preview, not the original element's constructor
            startPoint,
            endPoint,
            lineElement.vector
        );

        // Set red color for highlighting
        previewLine.renderProp = buildPreviewLineRenderProp();
        previewLine.renderProp.lineColor = '#ff0000';
        previewLine.renderProp.lineWeight = 3;

        return previewLine;
    }

    /**
     * Implementation of abstract method to check if reference preview should be updated
     */
    protected shouldUpdateReferencePreview(referenceElement: GeoRenderElement): boolean {
        const lineElement = referenceElement as RenderLine;
        return !this.selectedLine || this.selectedLine.relIndex !== lineElement.relIndex;
    }

    /**
     * Implementation of abstract method to update selected reference
     */
    protected updateSelectedReference(referenceElement: GeoRenderElement): void {
        this.selectedLine = referenceElement as RenderLine;
    }

    /**
     * Implementation of abstract method to update selection state
     */
    protected updateSelectionState(selected: boolean): void {
        this.toolState.lineSelected = selected;
        this.toolbar.update(this.toolType, this.toolState);
    }

    /**
     * Implementation of abstract method to create symmetry preview
     */
    protected createSymmetryPreview(
        referenceElement: GeoRenderElement,
        targetElement: GeoRenderElement,
        ctrl: GeoDocCtrl
    ) {
        const lineElement = referenceElement as RenderLine;
        const lineVector = lineElement.vector;
        const startPoint = ctrl.rendererCtrl.elementAt(lineElement.startPointIdx) as RenderVertex;

        return createSymmetry(
            targetElement,
            ctrl,
            ctrl.rendererCtrl,
            calculateSymmetricPointsThroughLine(startPoint.coords, lineVector)
        );
    }

    /**
     * Implementation of abstract method to get symmetry calculation function
     */
    protected getSymmetryCalculationFunction(
        referenceElement: GeoRenderElement,
        ctrl: GeoDocCtrl
    ): (coords: number[][]) => number[][] {
        const lineElement = referenceElement as RenderLine;
        return calculateSymmetricPointsThroughLine(
            (ctrl.rendererCtrl.elementAt(lineElement.startPointIdx) as RenderVertex).coords,
            lineElement.vector
        );
    }

    /**
     * Implementation of abstract method to get symmetry description
     */
    protected getSymmetryDescription(): string {
        return 'đối xứng qua đường thẳng';
    }
}

export class CreateSymmetricThroughPointTool extends BaseSymmetryTool<SymmetryThroughPointToolState> {
    readonly toolType: GeometryToolType = 'CreateSymmetricThroughPointTool';
    readonly symmetryToolType = 'point';
    private selectedPoint?: RenderVertex;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    /**
     * Implementation of abstract method to clear selected reference element
     */
    protected clearSelectedReference(): void {
        this.selectedPoint = undefined;
    }

    /**
     * Implementation of abstract method to get the first selector (point selector)
     */
    protected getFirstSelector() {
        return or(
            [
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderVertex'],
                    syncPreview: false, // Don't show vertex preview on point selection
                }),
            ],
            {
                flatten: true,
            }
        );
    }

    /**
     * Implementation of abstract method to get reference element from selection
     */
    protected getReferenceElementFromSelection(selection: SelectedVertex): RenderVertex | null {
        return Array.isArray(selection) && selection.length === 2
            ? (() => {
                  const strokeElement = selection[0] as GeoRenderElement;
                  return strokeElement.type === 'RenderVertex' ? (strokeElement as RenderVertex) : null;
              })()
            : null;
    }

    /**
     * Implementation of abstract method to create highlighted preview for reference element
     */
    protected createReferenceElementPreview(referenceElement: GeoRenderElement, ctrl: GeoDocCtrl): RenderVertex {
        const pointElement = referenceElement as RenderVertex;
        // Create preview point with highlighting
        const previewPoint = pVertex(
            -23, // Use a specific negative index for highlighted preview point
            pointElement.coords
        );

        // Set highlight color for the symmetry point
        previewPoint.renderProp = buildPreviewVertexRenderProp();
        previewPoint.renderProp.pointColor = '#ff0000';
        // previewPoint.renderProp.pointSize = 8;

        return previewPoint;
    }

    /**
     * Implementation of abstract method to check if reference preview should be updated
     */
    protected shouldUpdateReferencePreview(referenceElement: GeoRenderElement): boolean {
        const pointElement = referenceElement as RenderVertex;
        return !this.selectedPoint || this.selectedPoint.relIndex !== pointElement.relIndex;
    }

    /**
     * Implementation of abstract method to update selected reference
     */
    protected updateSelectedReference(referenceElement: GeoRenderElement): void {
        this.selectedPoint = referenceElement as RenderVertex;
    }

    /**
     * Implementation of abstract method to update selection state
     */
    protected updateSelectionState(selected: boolean): void {
        this.toolState.pointSelected = selected;
        this.toolbar.update(this.toolType, this.toolState);
    }

    /**
     * Implementation of abstract method to create symmetry preview
     */
    protected createSymmetryPreview(
        referenceElement: GeoRenderElement,
        targetElement: GeoRenderElement,
        ctrl: GeoDocCtrl
    ) {
        const pointElement = referenceElement as RenderVertex;
        return createSymmetry(
            targetElement,
            ctrl,
            ctrl.rendererCtrl,
            calculateSymmetricPointsThroughMiddlePoint(pointElement.coords)
        );
    }

    /**
     * Implementation of abstract method to get symmetry calculation function
     */
    protected getSymmetryCalculationFunction(
        referenceElement: GeoRenderElement,
        ctrl: GeoDocCtrl
    ): (coords: number[][]) => number[][] {
        const pointElement = referenceElement as RenderVertex;
        return calculateSymmetricPointsThroughMiddlePoint(pointElement.coords);
    }

    /**
     * Implementation of abstract method to get symmetry description
     */
    protected getSymmetryDescription(): string {
        return 'đối xứng qua điểm';
    }
}
