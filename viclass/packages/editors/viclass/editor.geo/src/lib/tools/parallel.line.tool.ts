import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { RenderLine, RenderVertex, RenderCircle, RenderEllipse, RenderSector } from '../model';
import { GeometryToolType } from '../model/geo.models';
import { pLine } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import {
    buildParallelLine,
    buildParallelLineSegment,
    buildParallelLineSegmentWithIntersectLine,
    buildParallelWithCircle,
    buildParallelWithEllipse,
    buildParallelWithSector,
} from './util.construction';
import { BaseParallelPerpendicularTool } from './util.parallel.perpendicular.tool';

/**
 * Parallel Line Tool - Creates parallel lines using selector pattern with preview
 * <AUTHOR>
 */
export class CreateParallelLineTool extends BaseParallelPerpendicularTool {
    readonly toolType: GeometryToolType = 'CreateParallelLineTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    /**
     * Calculate parallel vector from the selected line
     */
    protected calculateDirectionVector(line: RenderLine, ctrl: GeoDocCtrl): number[] {
        // Get line direction vector
        const lineVector = line.orderedVector(ctrl.rendererCtrl);
        if (!lineVector) throw new Error('Could not get direction vector from line');

        // Normalize the vector (parallel has same direction)
        const magnitude = Math.sqrt(lineVector[0] ** 2 + lineVector[1] ** 2);
        if (magnitude === 0) throw new Error('Line vector has zero magnitude');

        return [lineVector[0] / magnitude, lineVector[1] / magnitude];
    }

    /**
     * Show parallel line preview
     */
    protected createLinePreview(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex): void {
        try {
            // Calculate direction vector
            this.directionVector = this.calculateDirectionVector(line, ctrl);

            // Create parallel line preview passing through the selected point
            this.previewLine = pLine(ctrl, -20, RenderLine, throughPoint, undefined, this.directionVector);

            // Add to preview queue
            this.pQ.add(this.previewLine);
        } catch (error) {
            console.warn('Error creating parallel line preview:', error);
        }
    }

    /**
     * Show parallel line preview for curved elements
     */
    protected createCurvedElementPreview(
        ctrl: GeoDocCtrl,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex
    ): void {
        try {
            // Calculate tangent direction vector at the tangent point
            this.directionVector = this.calculateCurvedElementDirectionVector(curvedElement, tangentPoint, ctrl);

            // Create parallel line preview passing through the selected point
            this.previewLine = pLine(ctrl, -21, RenderLine, throughPoint, undefined, this.directionVector);

            // Add to preview queue
            this.pQ.add(this.previewLine);
        } catch (error) {
            console.warn('Error creating parallel curved element preview:', error);
        }
    }

    protected buildSimpleLineConstruction(lineName: string, baseLine: RenderLine, throughPoint: RenderVertex): any {
        return buildParallelLine(lineName, baseLine.name, baseLine.elType, throughPoint.name);
    }

    protected buildSimpleCurvedElementConstruction(
        lineName: string,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex
    ): any {
        switch (curvedElement.type) {
            case 'RenderCircle':
                return buildParallelWithCircle(lineName, curvedElement.name, throughPoint.name, tangentPoint.name);
            case 'RenderEllipse':
                return buildParallelWithEllipse(lineName, curvedElement.name, throughPoint.name, tangentPoint.name);
            case 'RenderSector':
                return buildParallelWithSector(lineName, curvedElement.name, throughPoint.name, tangentPoint.name);
            default:
                throw new Error(`Unsupported curved element type: ${(curvedElement as any).type}`);
        }
    }

    protected buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number
    ): any {
        return buildParallelLineSegment(combinedName, baseLine.name, baseLine.elType, throughPoint.name, scalingFactor);
    }

    protected buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex
    ): any {
        return buildParallelLineSegmentWithIntersectLine(
            combinedName,
            baseLine.name,
            baseLine.elType,
            intersectLine.name,
            intersectLine.elType,
            throughPoint.name
        );
    }

    protected getSimpleConstructionLabel(): string {
        return 'Tên đường thẳng song song';
    }

    protected getComplexConstructionLabel(): string {
        return 'Tên đường thẳng song song';
    }
}
