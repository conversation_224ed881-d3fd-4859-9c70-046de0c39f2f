import { point, vector } from '@flatten-js/core';
import { ToolEventData, ToolEventListener, UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { EllipseToolState, GeoElConstructionRequest, RenderEllipseShape, RenderVector, RenderVertex } from '../model';
import { GeoEpsilon, GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import {
    pEllipse,
    pEllipseFromCenterVectors,
    pEllipseShape,
    pEllipseShapeFromCenterVectors,
    pLine,
    PreviewQueue,
    pVertex,
} from '../model/util.preview';
import { nthDirectionByLine } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import { nPoints, SelectedVertex, then, ThenSelector, vert, vertex } from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    assignNames,
    buildPreviewVertexRenderProp,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    remoteConstruct,
} from './util.tool';

// Tool mode constants
const ELLIPSE_MODE = {
    FOCUS_POINTS: 0,
    CENTER_VECTORS: 1,
} as const;

export class CreateEllipseTool extends GeometryTool<EllipseToolState> {
    readonly toolType: GeometryToolType = 'CreateEllipseTool';
    protected objNameDisplay = 'Ellipse';

    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    renderEllipse: RenderEllipseShape;
    previewEllipse: any = null; // Store preview ellipse for reuse
    toolListener: ToolEventListener<GeometryToolBar, GeometryToolType>;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();

        // Register keyboard handling for Shift key - following circular sector pattern
        this.registerKeyboardHandling({
            event: 'keyup',
            keys: ['shift'],
            global: false,
        });

        // Create and register tool listener directly
        this.toolListener = new (class implements ToolEventListener<GeometryToolBar, GeometryToolType> {
            constructor(private ellipseTool: CreateEllipseTool) {}
            onEvent(
                eventData: ToolEventData<GeometryToolBar, GeometryToolType>
            ):
                | ToolEventData<GeometryToolBar, GeometryToolType>
                | Promise<ToolEventData<GeometryToolBar, GeometryToolType>> {
                // Check if this event is for our tool type and is a change event
                if (
                    eventData.toolType === this.ellipseTool.toolType &&
                    eventData.eventType === 'change' &&
                    eventData.state
                ) {
                    const state = eventData.state as EllipseToolState;
                    if (state.mode === ELLIPSE_MODE.FOCUS_POINTS || state.mode === ELLIPSE_MODE.CENTER_VECTORS) {
                        this.ellipseTool.resetState();
                        this.ellipseTool.createSelLogic();
                    }
                }
                return eventData;
            }
        })(this);
        this.toolbar.registerToolListener(this.toolListener);
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.previewEllipse = null; // Reset preview ellipse
        super.resetState();
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            // Toggle between FOCUS_POINTS and CENTER_VECTORS modes
            this.toolState.mode =
                this.toolState.mode === ELLIPSE_MODE.FOCUS_POINTS
                    ? ELLIPSE_MODE.CENTER_VECTORS
                    : ELLIPSE_MODE.FOCUS_POINTS;
            this.toolbar.update(this.toolType, this.toolState);
            return event;
        }
        super.handleKeyboardEvent(event);
        return event;
    }

    /**
     * Apply perpendicular projection for CENTER_VECTORS mode
     * @param previewEl Preview element to transform
     * @param center Center point
     * @param vectorAPoint Vector A endpoint
     * @returns Transformed preview element
     */
    private applyPerpendicularProjection(
        previewEl: RenderVertex,
        center: { x: number; y: number },
        vectorAPoint: { x: number; y: number }
    ): RenderVertex {
        try {
            const p = { x: previewEl.coords[0], y: previewEl.coords[1] };

            // Vector from center to vectorAPoint
            const centerPoint = point(center.x, center.y);
            const vectorAPoint_coords = point(vectorAPoint.x, vectorAPoint.y);
            const vectorA = vector(centerPoint, vectorAPoint_coords);

            // Create perpendicular vector (rotate 90 degrees)
            const perpVector = vector(-vectorA.y, vectorA.x);

            const perpUnit = perpVector.normalize();

            // Project the current point onto the perpendicular line through center
            const centerToP = vector(p.x - center.x, p.y - center.y);
            const projectionLength = centerToP.dot(perpUnit);

            // Calculate the perpendicular point
            const projectedPoint = perpUnit.multiply(projectionLength);
            previewEl.coords[0] = center.x + projectedPoint.x;
            previewEl.coords[1] = center.y + projectedPoint.y;
        } catch (error) {
            console.warn('Error in perpendicular projection:', error);
        }

        return previewEl;
    }

    public createSelLogic() {
        // Different selection patterns based on mode
        const f2pSelector = nPoints(this.pQ, this.pointerHandler.cursor, { count: 2 });

        let lastPointSelector;
        if (this.toolState.mode === ELLIPSE_MODE.FOCUS_POINTS) {
            // For focus points mode: use nPoints with count 1 for the final point
            lastPointSelector = nPoints(this.pQ, this.pointerHandler.cursor, { count: 1 });
        } else {
            // For center vectors mode: use vertex selector with constraints
            lastPointSelector = vertex({
                name: 'vectorBPoint',
                previewQueue: this.pQ,
                cursor: this.pointerHandler.cursor,
            });
        }

        this.selLogic = then([f2pSelector, lastPointSelector], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });

        // Set transform and check functions for CENTER_VECTORS mode only
        if (this.toolState.mode === ELLIPSE_MODE.CENTER_VECTORS) {
            const lastVertex = lastPointSelector as any; // Cast to access setOption methods
            lastVertex
                .setOption('tfunc', (previewEl: RenderVertex, doc: GeoDocCtrl) => {
                    const f2p = this.selLogic.selected[0] as SelectedVertex[];
                    const [center, vectorAPoint] = [f2p[0], f2p[1]].map(v => {
                        const vertex = vert(v);
                        return { x: vertex.coords[0], y: vertex.coords[1] };
                    });

                    return this.applyPerpendicularProjection(previewEl, center, vectorAPoint);
                })
                .setOption('cfunc', (previewEl: RenderVertex, doc: GeoDocCtrl) => {
                    const f2p = this.selLogic.selected[0] as SelectedVertex[];
                    const [center, vectorAPoint] = [f2p[0], f2p[1]].map(v => {
                        const vertex = vert(v);
                        return { x: vertex.coords[0], y: vertex.coords[1] };
                    });
                    const p = { x: previewEl.coords[0], y: previewEl.coords[1] };

                    try {
                        const centerPoint = point(center.x, center.y);
                        const vectorAPoint_coords = point(vectorAPoint.x, vectorAPoint.y);
                        const pPoint = point(p.x, p.y);
                        const vectorA = vector(centerPoint, vectorAPoint_coords);
                        const vectorB = vector(centerPoint, pPoint);
                        return Math.abs(vectorA.dot(vectorB)) < GeoEpsilon;
                    } catch (error) {
                        console.warn('Error checking perpendicular constraint:', error);
                        return false;
                    }
                });
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl));

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    /**
     * Handle preview for FOCUS_POINTS mode
     * @param selected Selected elements
     * @param ctrl Document controller
     */
    private handleFocusPointsPreview(selected: any[], ctrl: GeoDocCtrl): void {
        try {
            if (selected && selected.length >= 2 && selected[1]) {
                // Has all 3 points → preview ellipse
                const focusPoints = selected[0] as SelectedVertex[];
                const pointOnEllipse = selected[1] as SelectedVertex[]; // Now array from nPoints
                const [f1, f2] = focusPoints.map(p => vert(p));
                const pointOnEll = vert(pointOnEllipse[0]); // Take first point from array

                // Create preview ellipse from focus points and point on ellipse
                this.previewEllipse = pEllipse(ctrl, -1, f1, f2, pointOnEll);
                this.pQ.add(pEllipseShape(ctrl, -2, f1, f2, pointOnEll, this.previewEllipse));
            }
        } catch (error) {
            console.warn('Error in focus points preview:', error);
        }
    }

    /**
     * Handle preview for CENTER_VECTORS mode
     * @param selected Selected elements
     * @param ctrl Document controller
     */
    private handleCenterVectorsPreview(selected: any[], ctrl: GeoDocCtrl): void {
        try {
            if (selected && selected.length >= 2 && selected[1]) {
                // Has center, vectorAPoint, and constrained vectorBPoint → preview ellipse
                const centerAndVectorA = selected[0] as SelectedVertex[];
                const vectorBPoint = selected[1] as SelectedVertex;

                if (centerAndVectorA && centerAndVectorA.length >= 2) {
                    const [center, vectorAPoint] = centerAndVectorA.map(p => vert(p));
                    const vectorB = vert(vectorBPoint);

                    // Add preview vectors from center to both points
                    const vectorALine = pLine(ctrl, -3, RenderVector, center, vectorAPoint);
                    const vectorBLine = pLine(ctrl, -4, RenderVector, center, vectorB);
                    this.pQ.add(vectorALine);
                    this.pQ.add(vectorBLine);

                    // Add preview point at vector B endpoint with red color
                    const vectorBPreview = pVertex(-5, vectorB.coords);
                    vectorBPreview.renderProp = buildPreviewVertexRenderProp();
                    vectorBPreview.renderProp.pointColor = '#ff0000';
                    this.pQ.add(vectorBPreview);

                    // Create preview ellipse from center and vectors
                    this.previewEllipse = pEllipseFromCenterVectors(ctrl, -1, center, vectorAPoint, vectorB);
                    this.pQ.add(
                        pEllipseShapeFromCenterVectors(ctrl, -2, center, vectorAPoint, vectorB, this.previewEllipse)
                    );
                }
            }
        } catch (error) {
            console.warn('Error in center vectors preview:', error);
        }
    }

    /**
     * Handle pointer selection and show ellipse preview based on current mode
     */
    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (this.toolState.mode === ELLIPSE_MODE.FOCUS_POINTS) {
            this.handleFocusPointsPreview(selected, ctrl);
        } else {
            this.handleCenterVectorsPreview(selected, ctrl);
        }

        this.pQ.flush(ctrl);
    }

    /**
     * Perform the remote construction when user has selected all required points
     */
    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = selector.selected;
        if (!selected || selected.length < 2) {
            this.resetState();
            return;
        }

        let selectedPoints: SelectedVertex[] = [];
        const constraintGroup = this.toolState.mode === ELLIPSE_MODE.FOCUS_POINTS ? 'Points' : 'CenterVectors';

        if (this.toolState.mode === ELLIPSE_MODE.FOCUS_POINTS) {
            // FOCUS_POINTS mode: focus points + point on ellipse (using nPoints)
            const focusPoints = selected[0] as SelectedVertex[];
            const pointOnEllipse = selected[1] as SelectedVertex[]; // Now returns array from nPoints
            selectedPoints = [...focusPoints, ...pointOnEllipse];
        } else {
            // CENTER_VECTORS mode: center + vectorA + vectorB
            const centerAndVectorA = selected[0] as SelectedVertex[];
            const vectorBPoint = selected[1] as SelectedVertex;
            selectedPoints = [...centerAndVectorA, vectorBPoint];
        }

        try {
            // Build ellipse construction based on current mode
            let ellipseConstruction;
            if (this.toolState.mode === ELLIPSE_MODE.FOCUS_POINTS) {
                // FOCUS_POINTS mode: focus points + point on ellipse
                const { pcs, points: namedPoints } = await assignNames(
                    ctrl,
                    selectedPoints,
                    this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                    'Tiêu điểm',
                    'Ellipse',
                    this.previewEllipse
                );

                if (!pcs) {
                    this.resetState();
                    return;
                }

                ellipseConstruction = this.buildEllipseConstruction(
                    this.previewEllipse.name,
                    namedPoints[0].name, // f1
                    namedPoints[1].name, // f2
                    namedPoints[2].name, // point on ellipse
                    constraintGroup
                );

                await remoteConstruct(ctrl, ellipseConstruction, pcs, this.editor.geoGateway, 'ellipse');
            } else {
                // CENTER_VECTORS mode: center + vectorA + semi-minor axis length
                const centerAndVectorA = selected[0] as SelectedVertex[];
                const vectorBPoint = selected[1] as SelectedVertex;
                const [center, vectorAPoint] = centerAndVectorA.map(p => vert(p));
                const vectorB = vert(vectorBPoint);

                // For CENTER_VECTORS mode, vectorB point is virtual and will be created by backend
                // So only assign names to center and vectorA points
                const { pcs } = await assignNames(
                    ctrl,
                    [...centerAndVectorA, vectorBPoint], // Include vectorB for naming but don't construct it
                    this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                    'Tâm',
                    'Ellipse',
                    this.previewEllipse
                );

                if (!pcs) {
                    this.resetState();
                    return;
                }

                // Calculate semi-minor axis length (distance from center to vectorB)
                const centerPoint = point(center.coords[0], center.coords[1]);
                const vectorBPoint_coords = point(vectorB.coords[0], vectorB.coords[1]);
                const semiMinorAxisLength = centerPoint.distanceTo(vectorBPoint_coords)[0];

                // Determine nth value using utility method
                const vectorAPoint_coords = point(vectorAPoint.coords[0], vectorAPoint.coords[1]);
                const vectorA = vector(centerPoint, vectorAPoint_coords);
                const nthValue = nthDirectionByLine([vectorA.x, vectorA.y], center.coords, vectorB.coords);

                ellipseConstruction = this.buildEllipseCenterVectorsConstruction(
                    this.previewEllipse.name,
                    center.name, // center
                    vectorAPoint.name, // vectorA endpoint (pA)
                    vectorB.name, // vectorB endpoint name (pB) - will be created by backend
                    semiMinorAxisLength,
                    nthValue
                );

                // For CENTER_VECTORS mode, vectorB point is virtual and will be created by backend
                // So only include constructions for center and vectorA points
                const pointConstructions = pcs.filter(pc => pc.name === center.name || pc.name === vectorAPoint.name);

                await remoteConstruct(ctrl, ellipseConstruction, pointConstructions, this.editor.geoGateway, 'ellipse');
            }
        } finally {
            this.resetState();
        }
    }

    // Build construction for backend with corresponding constraint group
    private buildEllipseConstruction(
        name: string,
        p1: string,
        p2: string,
        p3: string,
        constraintGroup: string = 'Points'
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Ellipse/EllipseEC', 'Ellipse', constraintGroup);
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-EllipseFocusPoints',
                params: {
                    name: {
                        type: 'array',
                        values: [p1, p2, p3],
                    },
                },
            },
        ];

        return construction;
    }

    /**
     * Build ellipse construction for CenterVectors mode with validation
     * @param name Ellipse name
     * @param centerPoint Center point name
     * @param pAPoint Vector A endpoint name
     * @param pBPointName Vector B endpoint name (will be created by backend)
     * @param semiMinorAxisLength Semi-minor axis length
     * @param nthValue Nth value for orientation
     * @returns Construction request for ellipse
     */
    private buildEllipseCenterVectorsConstruction(
        name: string,
        centerPoint: string,
        pAPoint: string,
        pBPointName: string,
        semiMinorAxisLength: number,
        nthValue: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Ellipse/EllipseEC', 'Ellipse', 'CenterVectors');
        construction.name = name;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-EllipseCenterVectors',
                params: {
                    name: {
                        type: 'array',
                        values: [centerPoint, pAPoint],
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-EllipseSemiMinorAxisLength',
                params: {
                    value: {
                        type: 'singleValue',
                        value: semiMinorAxisLength,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-EllipseNthResult',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nthValue,
                    },
                },
            },
            {
                indexInCG: 3,
                paramDefId: 'aName',
                optional: true,
                tplStrLangId: 'tpl-EllipsePBPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: pBPointName,
                    },
                },
            },
        ];

        return construction;
    }
}
