import { Point, point, vector } from '@flatten-js/core';
import { <PERSON>rrorHandlerDecorator, UIPointerEventData } from '@viclass/editor.core';
import { syncPreviewCommands, syncRemovePreviewCmd } from '../cmd';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoElConstructionRequest,
    GeoRenderElement,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue, pVertex } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { RepeatSelector } from '../selectors';
import { nLines, SelectedStroke, strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { buildIntersectionRequest, IntersectionElementDetails } from './util.construction';
import { createExtractFlattenLine } from './util.flatten';
import {
    calculateCircleCircleIntersection,
    calculateLineCircleIntersection,
    calculateLineLineIntersection,
    intersectionCircleEllipse,
    intersectionEllipses,
    intersectionLineEllipse,
    isPointInSector,
} from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    pointsEqual,
    remoteConstruct,
} from './util.tool';
import { isLargerThan, pointsByRotation, sortOnLineV2 } from '../nth.direction';

/**
 * Intersection Point Tool - Creates intersection points between two geometric elements
 * Follows standardized geometry tool patterns for selection, preview, and construction
 */
export class IntersectionPointTool extends GeometryTool<CommonToolState> {
    override readonly toolType: GeometryToolType = 'IntersectionPointTool';

    declare selLogic: RepeatSelector<SelectedStroke>;
    private pQ = new PreviewQueue();
    private intersectionPreview: RenderVertex[] = [];
    private intersectionConstructed: RenderVertex[] = [];
    private allIntersections: { x: number; y: number }[] = []; // All intersections including outside
    private visibleIntersections: { x: number; y: number }[] = []; // Only intersections within bounds

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    /**
     * Creates selection logic using standardized selector pattern
     */
    private createSelLogic() {
        this.selLogic = nLines(this.pQ, this.pointerHandler.cursor, {
            count: 2,
            onComplete: this.performIntersectionPreview.bind(this),
        });
    }

    override resetState() {
        this.selLogic?.reset();
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        this.allIntersections = [];
        this.visibleIntersections = [];
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType === 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType === 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();
        return event;
    }

    /**
     * Handles selection attempts following standardized pattern
     */
    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        // Check if user clicked on existing intersection preview
        if (this.intersectionPreview.length > 0) {
            const hitCtx = ctrl.editor.checkHitInternal(ctrl.layers[0], event, false, true);
            const hitEl = hitCtx?.hitDetails?.el;

            if (hitEl && hitEl.type === 'RenderVertex') {
                const previewPoint = this.intersectionPreview.find(p => p.relIndex === hitEl.relIndex);
                if (previewPoint && event.eventType === 'pointerup') {
                    this.performConstruction(ctrl, previewPoint);
                    return;
                }
            }
        }

        // Try selection with the selector
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    /**
     * Generates intersection preview when two elements are selected
     * Calculates both all intersections and visible intersections
     */
    private async performIntersectionPreview(selector: RepeatSelector<SelectedStroke>, docCtrl: GeoDocCtrl) {
        const selectedElements = selector.selected || [];
        if (selectedElements.length !== 2) return;

        // Clear previous previews and constructed intersections
        this.intersectionPreview = [];
        this.intersectionConstructed = [];
        this.allIntersections = [];
        this.visibleIntersections = [];

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        // Calculate all intersections first
        this.allIntersections = this.calculateIntersections(stroke1, stroke2, docCtrl) || [];
        // Filter visible intersections based on element bounds
        this.visibleIntersections = this.filterVisibleIntersections(this.allIntersections, stroke1, stroke2, docCtrl);

        if (this.visibleIntersections.length === 0) {
            this.resetState();
            return;
        }

        // Special case for line-line: construct immediately
        if (isElementLine(stroke1) && isElementLine(stroke2)) {
            if (this.visibleIntersections.length === 0) return;
            const intersectionVertex = pVertex(-9998, [this.visibleIntersections[0].x, this.visibleIntersections[0].y]);
            this.intersectionPreview.push(intersectionVertex);
            await this.performConstruction(docCtrl, intersectionVertex);
            return;
        }

        // Create preview points for other intersection types
        // Only show visible intersections in preview
        this.createIntersectionPreviews(this.visibleIntersections, docCtrl);
    }

    private createCircleFromSector(sector: RenderSector): RenderCircle {
        const circle = new RenderCircle();

        circle.centerPointIdx = sector.centerPointIdx;
        circle.radius = sector.radius;
        circle.length = 2 * Math.PI * sector.radius;

        return circle;
    }

    /**
     * Calculates intersection points between two elements
     * @param includeOutside - Whether to include intersections outside element bounds
     */
    private calculateIntersections(
        element1: StrokeType,
        element2: StrokeType,
        docCtrl: GeoDocCtrl
    ): { x: number; y: number }[] | null {
        try {
            // Line-Line intersection
            if (isElementLine(element1) && isElementLine(element2))
                return calculateLineLineIntersection(element1 as RenderLine, element2 as RenderLine, docCtrl);

            // Line-Circle intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderCircle') ||
                (element1.type === 'RenderCircle' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
                return calculateLineCircleIntersection(line, circle, docCtrl);
            }

            // Line-Ellipse intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
                return intersectionLineEllipse(line, ellipse, docCtrl);
            }

            // Circle-Circle intersection
            if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') {
                if (!isLargerThan(element1.relIndex, element2.relIndex))
                    return calculateCircleCircleIntersection(
                        element1 as RenderCircle,
                        element2 as RenderCircle,
                        docCtrl
                    );
                else
                    return calculateCircleCircleIntersection(
                        element2 as RenderCircle,
                        element1 as RenderCircle,
                        docCtrl
                    );
            }

            // Circle-Ellipse intersection
            if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
                (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
            ) {
                const circle = (element1.type === 'RenderCircle' ? element1 : element2) as RenderCircle;
                const ellipse = (element1.type === 'RenderEllipse' ? element1 : element2) as RenderEllipse;
                return intersectionCircleEllipse(circle, ellipse, docCtrl);
            }

            // Ellipse-Ellipse intersection
            if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') {
                if (!isLargerThan(element1.relIndex, element2.relIndex))
                    return intersectionEllipses(element1 as RenderEllipse, element2 as RenderEllipse, docCtrl);
                else return intersectionEllipses(element2 as RenderEllipse, element1 as RenderEllipse, docCtrl);
            }

            // Line-Sector intersection
            if (
                (isElementLine(element1) && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && isElementLine(element2))
            ) {
                const line = (isElementLine(element1) ? element1 : element2) as RenderLine;
                const sector = (element1.type === 'RenderSector' ? element1 : element2) as RenderSector;
                return calculateLineCircleIntersection(line, this.createCircleFromSector(sector), docCtrl);
            }

            // Circle-Sector intersection
            if (
                (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
            ) {
                if (element1.type === 'RenderCircle') {
                    return calculateCircleCircleIntersection(
                        element1 as RenderCircle,
                        this.createCircleFromSector(element2 as RenderSector),
                        docCtrl
                    );
                } else {
                    return calculateCircleCircleIntersection(
                        element2 as RenderCircle,
                        this.createCircleFromSector(element1 as RenderSector),
                        docCtrl
                    );
                }
            }

            // Ellipse-Sector intersection
            if (
                (element1.type === 'RenderEllipse' && element2.type === 'RenderSector') ||
                (element1.type === 'RenderSector' && element2.type === 'RenderEllipse')
            ) {
                if (element1.type === 'RenderEllipse') {
                    return intersectionCircleEllipse(
                        this.createCircleFromSector(element2 as RenderSector),
                        element1 as RenderEllipse,
                        docCtrl
                    );
                } else {
                    return intersectionCircleEllipse(
                        this.createCircleFromSector(element1 as RenderSector),
                        element2 as RenderEllipse,
                        docCtrl
                    );
                }
            }

            // Sector-Sector intersection
            if (element1.type === 'RenderSector' && element2.type === 'RenderSector') {
                if (!isLargerThan(element1.relIndex, element2.relIndex))
                    return calculateCircleCircleIntersection(
                        this.createCircleFromSector(element1 as RenderSector),
                        this.createCircleFromSector(element2 as RenderSector),
                        docCtrl
                    );
                else
                    return calculateCircleCircleIntersection(
                        this.createCircleFromSector(element2 as RenderSector),
                        this.createCircleFromSector(element1 as RenderSector),
                        docCtrl
                    );
            }

            return null;
        } catch (error) {
            console.warn('Error calculating intersections:', error);
            return null;
        }
    }

    /**
     * Filters intersection points to only include those within element bounds
     * Uses isPointOn* functions to check if intersections are on the actual elements
     */
    private filterVisibleIntersections(
        intersections: { x: number; y: number }[],
        element1: StrokeType,
        element2: StrokeType,
        docCtrl: GeoDocCtrl
    ): { x: number; y: number }[] {
        if (!intersections || intersections.length === 0) return [];

        return intersections.filter(intersection => {
            const point = new Point(intersection.x, intersection.y);
            return this.isPointOnElement(point, element1, docCtrl) && this.isPointOnElement(point, element2, docCtrl);
        });
    }

    /**
     * Checks if a point is on a specific element using the appropriate isPointOn* function
     */
    private isPointOnElement(point: Point, element: StrokeType, docCtrl: GeoDocCtrl): boolean {
        try {
            switch (element.type) {
                case 'RenderLineSegment':
                case 'RenderRay':
                case 'RenderVector':
                    return createExtractFlattenLine(element as RenderLine, docCtrl).intersect(point).length > 0;
                case 'RenderSector':
                    return isPointInSector(point, element as RenderSector, docCtrl);
                default:
                    return true;
            }
        } catch (error) {
            console.warn('Error checking if point is on element:', error);
            return false;
        }
    }

    /**
     * Creates preview vertices for intersection points in the correct order
     * Order matches the backend calculation to ensure nth parameter consistency
     */
    private createIntersectionPreviews(intersections: { x: number; y: number }[], docCtrl: GeoDocCtrl) {
        // Create preview vertices in the same order as intersections array
        // This ensures nth parameter calculation matches backend behavior
        intersections.forEach((intersection, index) => {
            const previewVertex = pVertex(-9998 - index, [intersection.x, intersection.y]);
            this.intersectionPreview.push(previewVertex);
            syncPreviewCommands(previewVertex, docCtrl);
        });

        // Enable filtering for multiple intersection points
        if (this.intersectionPreview.length > 1) {
            this.editor.filterElementFunc = (el: GeoRenderElement) =>
                this.intersectionPreview.some(p => p.relIndex === el.relIndex);
        }
    }

    /**
     * Performs construction of selected intersection point
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async performConstruction(docCtrl: GeoDocCtrl, intersectionSelected: RenderVertex) {
        const { pcs, points } = await assignNames(
            docCtrl,
            [intersectionSelected],
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Giao Điểm'
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const construction = this.buildConstructionRequest(intersectionSelected, points[0].name, docCtrl);
        if (!construction) return;

        try {
            await remoteConstruct(docCtrl, construction, [], this.editor.geoGateway, 'giao điểm');
            this.intersectionConstructed.push(intersectionSelected);

            // Update remaining preview points
            if (this.intersectionPreview.length - this.intersectionConstructed.length === 0) this.resetState();
            else this.intersectionConstructed.forEach(p => syncRemovePreviewCmd(p, docCtrl));
        } catch (e) {
            this.resetState();
            throw e;
        }
    }

    private getElementConstructionDetails(
        el: RenderLine | RenderCircle | RenderEllipse | RenderSector
    ): IntersectionElementDetails {
        if (isElementLine(el)) {
            const defId = (() => {
                switch (el.elType) {
                    case 'Ray':
                        return 'aRay';
                    case 'LineSegment':
                        return 'aLineSegment';
                    case 'VectorVi':
                        return 'aVector';
                    default:
                        return 'aLine';
                }
            })();
            return { name: el.name, elType: el.elType, labelType: 'Line', defId };
        } else if (el.type === 'RenderCircle')
            return { name: el.name, elType: el.elType, labelType: 'Circle', defId: 'aCircle' };
        else if (el.type === 'RenderEllipse')
            return { name: el.name, elType: el.elType, labelType: 'Ellipse', defId: 'anEllipse' };
        else if (el.type === 'RenderSector')
            return { name: el.name, elType: el.elType, labelType: 'Sector', defId: 'aCircularSector' };

        // Should not happen due to filtering
        throw new Error(`Unknown element type for intersection: ${el.type}`);
    }

    /**
     * Builds construction request based on selected elements and intersection point
     */
    private buildConstructionRequest(
        intersectionPoint: RenderVertex,
        pointName: string,
        doc: GeoDocCtrl
    ): GeoElConstructionRequest | undefined {
        const selectedElements = this.selLogic.selected;
        if (!selectedElements || selectedElements.length !== 2) return undefined;

        const [element1, element2] = selectedElements;

        // Extract StrokeType from SelectedStroke using strk helper
        const stroke1 = strk(element1);
        const stroke2 = strk(element2);

        const paramA = this.getElementConstructionDetails(stroke1);
        const paramB = this.getElementConstructionDetails(stroke2);

        // Determine intersection type
        const cgName = this.getIntersectionTypeName(stroke1, stroke2);
        if (!cgName) return undefined;

        // Calculate nth parameter for multiple intersections
        const currentNth = this.calculateNthParameter(intersectionPoint.relIndex, doc);

        return buildIntersectionRequest({
            cgName,
            outputName: pointName,
            paramA,
            paramB,
            nth: cgName === 'LineLine' ? undefined : currentNth,
        });
    }

    /**
     * Gets intersection type name for construction
     */
    private getIntersectionTypeName(element1: StrokeType, element2: StrokeType): string | undefined {
        if (isElementLine(element1) && isElementLine(element2)) return 'LineLine';
        if (
            (isElementLine(element1) && element2.type === 'RenderCircle') ||
            (element1.type === 'RenderCircle' && isElementLine(element2))
        )
            return 'LineCircle';
        if (
            (isElementLine(element1) && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && isElementLine(element2))
        )
            return 'LineSector';
        if (
            (isElementLine(element1) && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && isElementLine(element2))
        )
            return 'LineEllipse';
        if (element1.type === 'RenderCircle' && element2.type === 'RenderCircle') return 'CircleCircle';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderSector') ||
            (element1.type === 'RenderSector' && element2.type === 'RenderCircle')
        )
            return 'CircleSector';
        if (
            (element1.type === 'RenderCircle' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderCircle')
        )
            return 'CircleEllipse';
        if (element1.type === 'RenderSector' && element2.type === 'RenderSector') return 'SectorSector';
        if (
            (element1.type === 'RenderSector' && element2.type === 'RenderEllipse') ||
            (element1.type === 'RenderEllipse' && element2.type === 'RenderSector')
        )
            return 'SectorEllipse';
        if (element1.type === 'RenderEllipse' && element2.type === 'RenderEllipse') return 'EllipseEllipse';
        return undefined;
    }

    /**
     * Calculates nth parameter for intersection point selection
     * Uses the same ordering logic as the backend to ensure consistent nth parameter calculation
     */
    private calculateNthParameter(relIdx: number, doc: GeoDocCtrl): number | undefined {
        if (this.allIntersections.length === 1) return undefined;

        // Find which visible intersection was selected
        const selectedVisibleIndex = this.intersectionPreview.findIndex(p => p.relIndex === relIdx);
        if (selectedVisibleIndex < 0) return undefined;

        const selectedVisiblePoint = point(
            this.visibleIntersections[selectedVisibleIndex].x,
            this.visibleIntersections[selectedVisibleIndex].y
        );
        if (!selectedVisiblePoint) return undefined;

        const selectedElements = this.selLogic.selected || [];
        if (selectedElements.length !== 2) return undefined;

        const stroke1 = strk(selectedElements[0]);
        const stroke2 = strk(selectedElements[1]);
        const orderedIntersections = this.getOrderedIntersections(stroke1, stroke2, doc);
        if (!orderedIntersections) return undefined;

        // Find the position in the ordered intersections array
        const orderedIndex = orderedIntersections.findIndex(pt => pointsEqual(pt, selectedVisiblePoint));

        // Return 0-based index to match backend expectation (backend uses nth directly)
        return orderedIndex >= 0 ? orderedIndex : undefined;
    }

    /**
     * Orders intersections using the same logic as the backend based on intersection type
     */
    private getOrderedIntersections(
        stroke1: StrokeType,
        stroke2: StrokeType,
        docCtrl: GeoDocCtrl
    ): Point[] | undefined {
        if (!this.allIntersections || this.allIntersections.length === 0) return undefined;

        // Convert intersection points to Point objects
        const intersectionPoints = this.allIntersections.map(pt => point(pt.x, pt.y));

        // Apply the same ordering logic as the backend
        if (isElementLine(stroke1) && isElementLine(stroke2)) {
            // Line-Line: no ordering needed (single intersection)
            return intersectionPoints;
        } else if (
            (isElementLine(stroke1) && (stroke2.type === 'RenderCircle' || stroke2.type === 'RenderSector')) ||
            ((stroke1.type === 'RenderCircle' || stroke1.type === 'RenderSector') && isElementLine(stroke2)) ||
            (isElementLine(stroke1) && stroke2.type === 'RenderEllipse') ||
            (stroke1.type === 'RenderEllipse' && isElementLine(stroke2))
        ) {
            // Line-Circle, Line-Sector, Line-Ellipse: order by parallel vector
            const line = (isElementLine(stroke1) ? stroke1 : stroke2) as RenderLine;
            const orderedVector = line.orderedVector(docCtrl.rendererCtrl);
            const directionVector = vector(orderedVector[0], orderedVector[1]);
            return sortOnLineV2(directionVector, intersectionPoints);
        } else if (
            (stroke1.type === 'RenderCircle' && stroke2.type === 'RenderCircle') ||
            (stroke1.type === 'RenderCircle' && stroke2.type === 'RenderSector') ||
            (stroke1.type === 'RenderSector' && stroke2.type === 'RenderCircle') ||
            (stroke1.type === 'RenderSector' && stroke2.type === 'RenderSector') ||
            (stroke1.type === 'RenderEllipse' && stroke2.type === 'RenderEllipse') ||
            (stroke1.type === 'RenderCircle' && stroke2.type === 'RenderEllipse') ||
            (stroke1.type === 'RenderEllipse' && stroke2.type === 'RenderCircle') ||
            (stroke1.type === 'RenderEllipse' && stroke2.type === 'RenderSector') ||
            (stroke1.type === 'RenderSector' && stroke2.type === 'RenderEllipse')
        ) {
            const first = isLargerThan(stroke1.relIndex, stroke2.relIndex) ? stroke2 : stroke1;
            const second = first === stroke1 ? stroke2 : stroke1;

            // Circle-Circle, Ellipse-Ellipse, Circle-Ellipse: order by rotation
            const center1 = this.getCenterPoint(first, docCtrl);
            const center2 = this.getCenterPoint(second, docCtrl);
            if (!center1 || !center2) return intersectionPoints;

            const connectionVector = point(center2.x - center1.x, center2.y - center1.y);
            return pointsByRotation(connectionVector, center1, intersectionPoints);
        }

        return intersectionPoints;
    }

    private getCenterPoint(element: StrokeType, docCtrl: GeoDocCtrl): Point | undefined {
        const centerCoords = (element as RenderCircle | RenderSector | RenderEllipse).coord(
            'center',
            docCtrl.rendererCtrl
        );
        return point(centerCoords[0], centerCoords[1]);
    }
}
