import { point } from '@flatten-js/core';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    CommonToolState,
    GeoRenderElement,
    RenderCircle,
    RenderEllipse,
    RenderLine,
    RenderSector,
    RenderVertex,
    StrokeType,
} from '../model';
import { GeoEpsilon, GeoPointerEvent } from '../model/geo.models';
import { PreviewQueue } from '../model/util.preview';
import { GeoDocCtrl } from '../objects';
import { or, stroke, then, ThenSelector, vert, vertex, vertexOnStroke, VertexOnStroke } from '../selectors';
import { strk } from '../selectors/common.selection';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import {
    calculateLineCircleIntersection,
    calculateLineLineIntersection,
    intersectionLineEllipse,
    isPointInSector,
} from './util.intersections';
import {
    assignNames,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    isElementLine,
    projectPointOntoLine,
    remoteConstruct,
} from './util.tool';

/**
 * Base class for line-based geometry tools (parallel, perpendicular, etc.)
 * Contains shared functionality for tools that create lines based on existing lines and points
 */
export abstract class BaseParallelPerpendicularTool extends GeometryTool<CommonToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();
    selectedLine: RenderLine | undefined;
    selectedCurvedElement: RenderCircle | RenderEllipse | RenderSector | undefined;
    selectedPoint: RenderVertex | undefined;
    selectedTangentPoint: RenderVertex | undefined;
    previewLine: RenderLine | undefined;
    directionVector: number[] | undefined;

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();
        this.createSelLogic();
    }

    override resetState() {
        if (this.selLogic) this.selLogic.reset();
        this.selectedLine = undefined;
        this.selectedCurvedElement = undefined;
        this.selectedPoint = undefined;
        this.selectedTangentPoint = undefined;
        this.previewLine = undefined;
        this.directionVector = undefined;
        super.resetState();
    }

    /**
     * Abstract methods that must be implemented by subclasses
     */
    protected abstract createLinePreview(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex): void;

    protected abstract createCurvedElementPreview(
        ctrl: GeoDocCtrl,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex,
    ): void;

    protected abstract buildSimpleLineConstruction(
        lineName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
    ): any;

    protected abstract buildSimpleCurvedElementConstruction(
        lineName: string,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex,
    ): any;

    protected abstract buildLineSegmentConstruction(
        combinedName: string,
        baseLine: RenderLine,
        throughPoint: RenderVertex,
        scalingFactor: number,
    ): any;

    protected abstract buildLineSegmentWithIntersectionConstruction(
        combinedName: string,
        baseLine: RenderLine,
        intersectLine: RenderLine,
        throughPoint: RenderVertex,
    ): any;

    protected abstract getSimpleConstructionLabel(): string;

    protected abstract getComplexConstructionLabel(): string;

    /**
     * Creates the selection logic using selector pattern with preview
     * Following Pattern: Line/Curved Element -> Point -> [Tangent Point] -> Preview -> Final Point Selection
     */
    protected createSelLogic() {
        // First selector: select a line or curved element
        const elementSelector = stroke({
            selectableStrokeTypes: [
                'RenderVector',
                'RenderLine',
                'RenderLineSegment',
                'RenderRay',
                'RenderCircle',
                'RenderEllipse',
                'RenderSector',
            ],
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Second selector: select a point to define through which the line passes
        const firstPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Third selector: for curved elements, select tangent point
        const tangentPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.pointerHandler.cursor,
        });

        // Fourth selector: enhanced vertex selector with projection for final point on line
        const finalVertexSelector = or(
            [
                // Option 1: Select free vertex with projection onto line
                vertex({
                    preview: true, // Allow selecting preview elements (including first point if it was a preview)
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectOnLine(previewEl, doc),
                }),
                // Option 2: Select vertex on stroke with intersection projection
                vertexOnStroke({
                    preview: true,
                    previewQueue: this.pQ,
                    cursor: this.pointerHandler.cursor,
                    tfunc: (stroke, previewVertex, doc) =>
                        this.projectVertexOnStrokeToIntersection(stroke, previewVertex, doc),
                    cfunc: (stroke, doc) => this.checkStrokeIntersection(stroke, doc),
                    refinedFilter: (el: GeoRenderElement) =>
                        isElementLine(el) ||
                        el.type === 'RenderCircle' ||
                        el.type === 'RenderEllipse' ||
                        el.type === 'RenderSector',
                }),
            ],
            { flatten: true },
        );

        // Main selection logic: element -> point -> [tangent point] -> final vertex
        this.selLogic = then([elementSelector, firstPointSelector, tangentPointSelector, finalVertexSelector], {
            onComplete: async (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [element, throughPoint, tangentPoint, finalVertexSelection] = selector.selected;

                const selectedElement = strk(element as any);
                this.selectedPoint = vert(throughPoint as RenderVertex);

                // Check if it's a line or curved element
                if (isElementLine(selectedElement)) {
                    // Handle line-based construction (skip tangent point)
                    this.selectedLine = selectedElement as RenderLine;
                    const finalVertex = vert(tangentPoint as RenderVertex | VertexOnStroke); // tangentPoint becomes finalVertex for lines

                    if (this.selectedPoint.relIndex === finalVertex.relIndex) {
                        if (!this.previewLine) {
                            console.error('Preview line not available for same point construction');
                            this.resetState();
                            return;
                        }
                        await this.handleSimpleLineConstruction(doc, this.selectedLine, this.selectedPoint);
                    } else {
                        await this.handleComplexLineConstruction(
                            doc,
                            this.selectedLine,
                            this.selectedPoint,
                            finalVertex,
                            tangentPoint, // Pass tangentPoint as finalVertexSelection for lines
                        );
                    }
                } else {
                    // Handle curved element construction
                    this.selectedCurvedElement = selectedElement as RenderCircle | RenderEllipse | RenderSector;
                    this.selectedTangentPoint = vert(tangentPoint as RenderVertex);
                    const finalVertex = vert(finalVertexSelection as RenderVertex | VertexOnStroke);

                    if (this.selectedPoint.relIndex === finalVertex.relIndex) {
                        if (!this.previewLine) {
                            console.error('Preview line not available for same point construction');
                            this.resetState();
                            return;
                        }
                        await this.handleSimpleCurvedElementConstruction(
                            doc,
                            this.selectedCurvedElement,
                            this.selectedPoint,
                            this.selectedTangentPoint,
                        );
                    } else {
                        await this.handleComplexCurvedElementConstruction(
                            doc,
                            this.selectedCurvedElement,
                            this.selectedPoint,
                            this.selectedTangentPoint,
                            finalVertex,
                            finalVertexSelection,
                        );
                    }
                }
            },
        });
    }

    /**
     * Check function to validate that stroke has intersection with preview line
     * or contains the selected point
     */
    protected checkStrokeIntersection(stroke: StrokeType, doc: GeoDocCtrl): boolean {
        if (!this.previewLine) return false; // No preview line available

        if (strk(stroke).relIndex === this.previewLine.relIndex) return true;

        try {
            let intersections: any[] = [];

            // Calculate intersections based on stroke type
            if (isElementLine(stroke)) {
                intersections = calculateLineLineIntersection(this.previewLine, stroke as RenderLine, doc);
            } else if (stroke.type === 'RenderCircle') {
                intersections = calculateLineCircleIntersection(this.previewLine, stroke as RenderCircle, doc);
            } else if (stroke.type === 'RenderEllipse') {
                intersections = intersectionLineEllipse(this.previewLine, stroke as RenderEllipse, doc);
            } else if (stroke.type === 'RenderSector') {
                const sector = stroke as RenderSector;

                // Create a temporary circle for intersection calculation
                const tempCircle = new RenderCircle();
                tempCircle.centerPointIdx = sector.centerPointIdx;
                tempCircle.radius = sector.radius;

                intersections = calculateLineCircleIntersection(this.previewLine, tempCircle, doc);

                // Filter intersections to only include points that are within the sector
                if (intersections && intersections.length > 0) {
                    intersections = intersections.filter(intersection => {
                        const pt = point(intersection.x, intersection.y);
                        return isPointInSector(pt, sector, doc);
                    });
                }
            }

            return intersections && intersections.length > 0;
        } catch (error) {
            console.warn('Error checking stroke intersection:', error);
            return false;
        }
    }

    /**
     * Transform function to project any point onto the line preview
     */
    protected projectOnLine(previewEl: RenderVertex, _doc: GeoDocCtrl): RenderVertex {
        if (previewEl.relIndex >= 0 || !this.selectedLine || !this.selectedPoint || !this.previewLine || !this.directionVector)
            return previewEl;

        try {
            const throughPointCoords = this.selectedPoint.coords;

            // Check if the preview point is very close to the through point (same point selection)
            const distance = Math.hypot(
                previewEl.coords[0] - throughPointCoords[0],
                previewEl.coords[1] - throughPointCoords[1],
            );

            // If user clicks on the same point as through point, keep it there
            if (distance < GeoEpsilon) {
                previewEl.coords[0] = throughPointCoords[0];
                previewEl.coords[1] = throughPointCoords[1];
                if (previewEl.coords.length > 2) previewEl.coords[2] = 0;
                return previewEl;
            }

            const projectedCoords = projectPointOntoLine(previewEl.coords, throughPointCoords, this.directionVector);

            if (projectedCoords) {
                previewEl.coords[0] = projectedCoords[0];
                previewEl.coords[1] = projectedCoords[1];
                if (previewEl.coords.length > 2) previewEl.coords[2] = 0; // Z coordinate
            }

            return previewEl;
        } catch (error) {
            console.warn('Error projecting point onto line:', error);
            return previewEl;
        }
    }

    /**
     * Transform function to project vertex on stroke to intersection with line
     */
    protected projectVertexOnStrokeToIntersection(
        stroke: StrokeType,
        previewVertex: RenderVertex,
        doc: GeoDocCtrl,
    ): RenderVertex {
        const s = stroke as RenderLine;

        if (this.previewLine.relIndex === s.relIndex) return previewVertex;

        try {
            let intersections: any[] = [];

            // Calculate intersections based on stroke type
            if (isElementLine(stroke)) {
                intersections = calculateLineLineIntersection(this.previewLine, stroke as RenderLine, doc);
            } else if (stroke.type === 'RenderCircle') {
                intersections = calculateLineCircleIntersection(this.previewLine, stroke as RenderCircle, doc);
            } else if (stroke.type === 'RenderEllipse') {
                intersections = intersectionLineEllipse(this.previewLine, stroke as RenderEllipse, doc);
            } else if (stroke.type === 'RenderSector') {
                // For sector, calculate intersection with underlying circle first
                const sector = stroke as RenderSector;
                const centerCoords = sector.coord('center', doc.rendererCtrl);

                // Create a temporary circle for intersection calculation
                const tempCircle = new RenderCircle();
                tempCircle.centerPointIdx = sector.centerPointIdx;
                tempCircle.radius = sector.radius;
                tempCircle.pInfo = { refPEl: [], cCoords: centerCoords };

                intersections = calculateLineCircleIntersection(this.previewLine, tempCircle, doc);

                // Filter intersections to only include points that are within the sector
                if (intersections && intersections.length > 0) {
                    intersections = intersections.filter(intersection => {
                        const pt = point(intersection.x, intersection.y);
                        return isPointInSector(pt, sector, doc);
                    });
                }
            }

            if (intersections?.length) {
                // For multiple intersections (circle, ellipse, sector), find the closest one to current position
                if (
                    intersections.length > 1 &&
                    (stroke.type === 'RenderCircle' ||
                        stroke.type === 'RenderEllipse' ||
                        stroke.type === 'RenderSector')
                ) {
                    const currentPos = point(previewVertex.coords[0], previewVertex.coords[1]);
                    let closestIntersection = intersections[0];
                    let minDistance = currentPos.distanceTo(point(intersections[0].x, intersections[0].y))[0];

                    for (let i = 1; i < intersections.length; i++) {
                        const intersectionPoint = point(intersections[i].x, intersections[i].y);
                        const distance = currentPos.distanceTo(intersectionPoint)[0];

                        if (distance < minDistance) {
                            minDistance = distance;
                            closestIntersection = intersections[i];
                        }
                    }

                    previewVertex.coords = [closestIntersection.x, closestIntersection.y];
                } else {
                    // For single intersection or line intersections, use the first intersection
                    previewVertex.coords = [intersections[0].x, intersections[0].y];
                }
            } else {
                return undefined;
            }
        } catch (error) {
            console.warn('Error projecting vertex on stroke to intersection:', error);
        }

        return previewVertex;
    }

    /**
     * Handle simple line construction when final vertex is same as through point
     */
    protected async handleSimpleLineConstruction(ctrl: GeoDocCtrl, line: RenderLine, throughPoint: RenderVertex) {
        try {
            // Validate essential prerequisites
            if (!this.previewLine) {
                console.error('previewLine is not available for construction');
                this.resetState();
                return;
            }

            // Use assignNames with previewLine as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                this.previewLine,
            );

            // Use build line construction
            const construction = this.buildSimpleLineConstruction(this.previewLine.name, line, throughPoint);

            await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, this.getSimpleConstructionLabel());
        } catch (error) {
            console.error('Error in simple line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex line construction when final vertex is different from through point
     */
    protected async handleComplexLineConstruction(
        ctrl: GeoDocCtrl,
        line: RenderLine,
        throughPoint: RenderVertex,
        finalVertex: RenderVertex,
        finalVertexSelection: any,
    ) {
        try {
            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                ctrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel(),
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;

            // Check if final vertex is from a stroke intersection
            const stroke = strk(finalVertexSelection);
            if (isElementLine(stroke)) {
                // Use intersection construction
                const construction = this.buildLineSegmentWithIntersectionConstruction(
                    combinedName,
                    line,
                    stroke as RenderLine,
                    throughPoint,
                );
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel(),
                );
            } else {
                // Use segment construction with scaling factor
                const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
                const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

                let k = 0;
                if (startPt && endPt && this.directionVector) {
                    // Calculate vector from through point to final vertex
                    const toEndVector = [
                        finalVertex.coords[0] - throughPoint.coords[0],
                        finalVertex.coords[1] - throughPoint.coords[1],
                    ];

                    // Calculate dot product to determine direction
                    const dotProduct =
                        toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                    // Distance from through point to final vertex
                    const distance = startPt.distanceTo(endPt)[0];

                    // k is positive if same direction, negative if opposite direction
                    k = dotProduct >= 0 ? distance : -distance;
                }

                const construction = this.buildLineSegmentConstruction(combinedName, line, throughPoint, k);
                await remoteConstruct(
                    ctrl,
                    construction,
                    pcs.filter(pc => pc.name === throughPoint.name),
                    this.editor.geoGateway,
                    this.getComplexConstructionLabel(),
                );
            }
        } catch (error) {
            console.error('Error in complex line construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle simple curved element construction when final vertex is same as through point
     */
    protected async handleSimpleCurvedElementConstruction(
        ctrl: GeoDocCtrl,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex,
    ) {
        try {
            // Validate essential prerequisites
            if (!this.previewLine) {
                console.error('previewLine is not available for construction');
                this.resetState();
                return;
            }

            // Use assignNames with previewLine as target object
            await assignNames(
                ctrl,
                [],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                '',
                this.getSimpleConstructionLabel(),
                this.previewLine,
            );

            // Use build curved element construction
            const construction = this.buildSimpleCurvedElementConstruction(
                this.previewLine.name,
                curvedElement,
                throughPoint,
                tangentPoint,
            );

            await remoteConstruct(ctrl, construction, [], this.editor.geoGateway, this.getSimpleConstructionLabel());
        } catch (error) {
            console.error('Error in simple curved element construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    /**
     * Handle complex curved element construction when final vertex is different from through point
     */
    protected async handleComplexCurvedElementConstruction(
        ctrl: GeoDocCtrl,
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        throughPoint: RenderVertex,
        tangentPoint: RenderVertex,
        finalVertex: RenderVertex,
        finalVertexSelection: any,
    ) {
        try {
            // Line segment to point - assign names for both line and endpoint
            const { pcs, points } = await assignNames(
                ctrl,
                [throughPoint, finalVertex],
                this.toolbar.getTool('NamingElementTool') as NamingElementTool,
                'Tên điểm cuối',
                this.getComplexConstructionLabel(),
            );

            if (!pcs || !points) {
                this.resetState();
                return;
            }

            throughPoint.name = points.find(p => p.relIndex === throughPoint.relIndex)?.name;
            finalVertex.name = points.find(p => p.relIndex === finalVertex.relIndex)?.name;
            tangentPoint.name = tangentPoint.name || 'T'; // Default tangent point name

            // Create combined name for through point and end element
            const combinedName = `${throughPoint.name}${finalVertex.name}`;

            // For curved elements, we typically use segment construction with scaling factor
            const startPt = point(throughPoint.coords[0], throughPoint.coords[1]);
            const endPt = point(finalVertex.coords[0], finalVertex.coords[1]);

            let k = 0;
            if (startPt && endPt && this.directionVector) {
                // Calculate vector from through point to final vertex
                const toEndVector = [
                    finalVertex.coords[0] - throughPoint.coords[0],
                    finalVertex.coords[1] - throughPoint.coords[1],
                ];

                // Calculate dot product to determine direction
                const dotProduct = toEndVector[0] * this.directionVector[0] + toEndVector[1] * this.directionVector[1];

                // Distance from through point to final vertex
                const distance = startPt.distanceTo(endPt)[0];

                // k is positive if same direction, negative if opposite direction
                k = dotProduct >= 0 ? distance : -distance;
            }

            // For now, use simple construction - can be extended later for segment construction
            const construction = this.buildSimpleCurvedElementConstruction(
                combinedName,
                curvedElement,
                throughPoint,
                tangentPoint,
            );

            await remoteConstruct(
                ctrl,
                construction,
                pcs.filter(pc => pc.name === throughPoint.name),
                this.editor.geoGateway,
                this.getComplexConstructionLabel(),
            );
        } catch (error) {
            console.error('Error in complex curved element construction:', error);
            this.resetState();
            throw error;
        } finally {
            this.resetState();
        }
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') if (!this.shouldHandleClick(event)) return event;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) return event;

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, (event: any) =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl)),
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    protected doTrySelection(event: GeoPointerEvent, ctrl: GeoDocCtrl) {
        this.selLogic.trySelect(event, ctrl);

        // Show preview based on current selection state
        if (this.selLogic.selected && Array.isArray(this.selLogic.selected)) {
            const selections = this.selLogic.selected;
            const [element, throughPoint, tangentPointOrFinal, finalSelection] = selections;

            if (element && throughPoint && tangentPointOrFinal && !finalSelection) {
                // Check if it's a line or curved element
                const selectedElement = strk(element as any);
                this.selectedPoint = vert(throughPoint as RenderVertex);

                if (isElementLine(selectedElement)) {
                    // For lines: element -> throughPoint -> finalPoint (tangentPointOrFinal is actually final)
                    this.selectedLine = selectedElement as RenderLine;
                    this.createLinePreview(ctrl, this.selectedLine, this.selectedPoint);
                } else {
                    // For curved elements: element -> throughPoint -> tangentPoint -> [finalPoint]
                    this.selectedCurvedElement = selectedElement as RenderCircle | RenderEllipse | RenderSector;
                    this.selectedTangentPoint = vert(tangentPointOrFinal as RenderVertex);
                    this.createCurvedElementPreview(
                        ctrl,
                        this.selectedCurvedElement,
                        this.selectedPoint,
                        this.selectedTangentPoint,
                    );
                }
            }
        } else if (this.selLogic.selected && !Array.isArray(this.selLogic.selected)) {
            // Only element selected (not array yet)
            const element = this.selLogic.selected as any;
            if (
                element &&
                [
                    'RenderLine',
                    'RenderLineSegment',
                    'RenderRay',
                    'RenderVector',
                    'RenderCircle',
                    'RenderEllipse',
                    'RenderSector',
                ].includes(element.type)
            ) {
                if (isElementLine(element)) {
                    this.selectedLine = element as RenderLine;
                } else {
                    this.selectedCurvedElement = element as RenderCircle | RenderEllipse | RenderSector;
                }
            }
        }

        // Always flush at the end
        this.pQ.flush(ctrl);
    }

    /**
     * Calculate tangent direction vector for a circle at a given point
     */
    protected calculateCircleTangentVector(
        circle: RenderCircle,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl,
    ): number[] {
        const centerCoords = circle.coord('center', ctrl.rendererCtrl);
        const pointCoords = tangentPoint.coords;

        // Vector from center to point
        const radiusVector = [pointCoords[0] - centerCoords[0], pointCoords[1] - centerCoords[1]];

        // Tangent vector is perpendicular to radius vector
        const tangentVector = [-radiusVector[1], radiusVector[0]];

        // Normalize the vector
        const magnitude = Math.sqrt(tangentVector[0] ** 2 + tangentVector[1] ** 2);
        if (magnitude === 0) throw new Error('Circle tangent vector has zero magnitude');

        return [tangentVector[0] / magnitude, tangentVector[1] / magnitude];
    }

    /**
     * Calculate tangent direction vector for an ellipse at a given point
     */
    protected calculateEllipseTangentVector(
        ellipse: RenderEllipse,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl,
    ): number[] {
        // Get ellipse parameters
        const centerCoords = ellipse.coord('center', ctrl.rendererCtrl);
        const pointCoords = tangentPoint.coords;

        // For simplicity, approximate ellipse tangent using the same approach as circle
        // This is a simplified implementation - a more accurate version would require
        // the ellipse's rotation angle and semi-major/minor axes
        const radiusVector = [pointCoords[0] - centerCoords[0], pointCoords[1] - centerCoords[1]];
        const tangentVector = [-radiusVector[1], radiusVector[0]];

        // Normalize the vector
        const magnitude = Math.sqrt(tangentVector[0] ** 2 + tangentVector[1] ** 2);
        if (magnitude === 0) throw new Error('Ellipse tangent vector has zero magnitude');

        return [tangentVector[0] / magnitude, tangentVector[1] / magnitude];
    }

    /**
     * Calculate tangent direction vector for a sector at a given point
     */
    protected calculateSectorTangentVector(
        sector: RenderSector,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl,
    ): number[] {
        // For sectors, use the same approach as circles since sectors are portions of circles
        const centerCoords = sector.coord('center', ctrl.rendererCtrl);
        const pointCoords = tangentPoint.coords;

        // Vector from center to point
        const radiusVector = [pointCoords[0] - centerCoords[0], pointCoords[1] - centerCoords[1]];

        // Tangent vector is perpendicular to radius vector
        const tangentVector = [-radiusVector[1], radiusVector[0]];

        // Normalize the vector
        const magnitude = Math.sqrt(tangentVector[0] ** 2 + tangentVector[1] ** 2);
        if (magnitude === 0) throw new Error('Sector tangent vector has zero magnitude');

        return [tangentVector[0] / magnitude, tangentVector[1] / magnitude];
    }

    /**
     * Calculate direction vector for curved elements
     */
    protected calculateCurvedElementDirectionVector(
        curvedElement: RenderCircle | RenderEllipse | RenderSector,
        tangentPoint: RenderVertex,
        ctrl: GeoDocCtrl,
    ): number[] {
        switch (curvedElement.type) {
            case 'RenderCircle':
                return this.calculateCircleTangentVector(curvedElement as RenderCircle, tangentPoint, ctrl);
            case 'RenderEllipse':
                return this.calculateEllipseTangentVector(curvedElement as RenderEllipse, tangentPoint, ctrl);
            case 'RenderSector':
                return this.calculateSectorTangentVector(curvedElement as RenderSector, tangentPoint, ctrl);
            default:
                throw new Error(`Unsupported curved element type: ${(curvedElement as any).type}`);
        }
    }
}
