import { line, point, vector } from '@flatten-js/core';
import { UIPointerEventData } from '@viclass/editor.core';
import { GeoPointerNotInError } from '../error-handler';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import { GeoElConstructionRequest, RenderLineSegment, RenderVertex, TriangleToolState } from '../model';
import { GeoKeyboardEvent, GeometryToolType, GeoPointerEvent } from '../model/geo.models';
import { pLine, PreviewQueue } from '../model/util.preview';
import { nthSideOfVector } from '../nth.direction';
import { GeoDocCtrl } from '../objects';
import {
    circleCheck,
    circleTransform,
    closerPoint,
    equilateralCheck,
    equilateralTransform,
    halfCircleCheck,
    halfCircleTransform,
    nPoints,
    perpBisectorCheck,
    perpBisectorTransform,
    perpLinesCheck,
    perpLinesTransform,
    SelectedVertex,
    then,
    ThenSelector,
    triangleWithProj,
    vert,
    vertexS,
} from '../selectors';
import { GeometryTool } from './geo.tool';
import { NamingElementTool } from './naming.element.tool';
import { assignNames, getFocusDocCtrl, handleIfPointerNotInError, posInGeo, remoteConstruct } from './util.tool';

export abstract class BaseTriangleTool extends GeometryTool<TriangleToolState> {
    declare selLogic: ThenSelector;
    pQ = new PreviewQueue();

    protected abstract createSelLogic();

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        this.doRegisterPointer();

        this.createSelLogic();
    }

    override resetState() {
        this.selLogic.reset();
        super.resetState();
    }

    override handlePointerEvent(event: GeoPointerEvent): GeoPointerEvent {
        if (event.eventType == 'pointerdown') {
            if (!this.shouldHandleClick(event)) return event;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        if (event.eventType == 'pointermove')
            this.pointerMoveCachingReflowSync.handleEvent(event, event =>
                handleIfPointerNotInError(this, () => this.doTrySelection(event, ctrl))
            );
        else this.doTrySelection(event, ctrl);

        event.continue = false;
        event.nativeEvent.preventDefault();

        return event;
    }

    private doTrySelection(event: UIPointerEventData<any>, ctrl: GeoDocCtrl) {
        try {
            const selected = this.selLogic.trySelect(event, ctrl);

            if (selected) {
                if (selected.length == 2) {
                    const geoPos = posInGeo(event, ctrl, true);
                    // has all data, preview triangle
                    const [p1, p2, p3] = [...(selected[0] as SelectedVertex[]), selected[1] as RenderVertex].map(v =>
                        vert(v)
                    );

                    this.pQ.add(pLine(ctrl, -21, RenderLineSegment, p1, p3));
                    this.pQ.add(pLine(ctrl, -22, RenderLineSegment, p2, p3));
                } else if (selected.length == 1 && Array.isArray(selected[0]) && selected[0].length == 2) {
                    // preview the base
                    this.pQ.add(
                        pLine(
                            ctrl,
                            -20,
                            RenderLineSegment,
                            vert(selected[0][0] as SelectedVertex),
                            vert(selected[0][1] as SelectedVertex)
                        )
                    );
                }
            }
            this.pQ.flush(ctrl);
        } catch (e) {
            console.error(e.state);
        }
    }

    override handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.createFromBase = !this.toolState.createFromBase;
            this.toolbar.update(this.toolType, this.toolState);
            return event;
        }

        super.handleKeyboardEvent(event);
        return event;
    }

    protected abstract performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl);
}

export class CreateTriangleTool extends BaseTriangleTool {
    readonly toolType: GeometryToolType = 'CreateTriangleTool';

    protected override createSelLogic() {
        this.selLogic = then(
            [nPoints(this.pQ, this.pointerHandler.cursor, { count: 2 }), vertexS(this.pQ, this.pointerHandler.cursor)],
            {
                onComplete: this.performConstruction.bind(this),
            }
        );
    }

    protected async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [...(selector.selected[0] as SelectedVertex[]), selector.selected[1] as SelectedVertex];
        const { pcs, points } = await assignNames(
            ctrl,
            selected,
            this.toolbar.getTool('NamingElementTool') as NamingElementTool,
            'Tam giác'
        );

        if (!pcs) {
            this.resetState();
            return;
        }

        const triangleName = `${points[0].name}${points[1].name}${points[2].name}`;
        const constructionTriangle = this.buildTriangleConstruction(triangleName);

        try {
            await remoteConstruct(ctrl, constructionTriangle, pcs, this.editor.geoGateway, 'tam giác');
        } finally {
            this.resetState();
        }
    }

    private buildTriangleConstruction(name: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest('Triangle/TriangleBaseEC', 'Triangle', 'ByPointsName');
        construction.name = name;
        construction.paramSpecs = [];

        return construction;
    }
}
export class CreateRightTriangleTool extends BaseTriangleTool {
    readonly toolType: GeometryToolType = 'CreateRightTriangleTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.registerKeyboardHandling({
            event: 'keyup',
            keys: ['shift'],
            global: false,
        });
    }

    protected override createSelLogic(): void {
        this.selLogic = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) => (!this.toolState.createFromBase ? perpLinesTransform(f2p, el) : halfCircleTransform(f2p, el)),
            (f2p, el) => (!this.toolState.createFromBase ? perpLinesCheck(f2p, el) : halfCircleCheck(f2p, el)),
            {
                onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
            }
        );
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [
            ...(selector.selected[0] as SelectedVertex[]), // first two point from repeat selector
            selector.selected[1] as SelectedVertex, // last point
        ];
        // The list of render vertices in order of clicking
        const rv = selected.map(v => vert(v));
        const ps = rv.map(v => point(v.coords[0], v.coords[1]));
        const [apex, b1, b2] = rightTriangleBaseNApex(rv, this.toolState.createFromBase);

        /**
         * When do we need to create from 3 points?
         *
         * When ALL THREE POINTS are existing points, then we must create this triangle from 3 points, because they cannot
         * be established by any other rules. If one of the point is unknown, then we can use either of this constructions:
         *
         * - base and height
         * - side angle and hypothenuse
         *
         * To establish the position of the unknown point.
         *
         */
        const need3PointsConstruct = rv.every(p => p.name);
        let constructionTriangle: GeoElConstructionRequest;
        // constructions for possible unknown points
        const constructionPoints: GeoElConstructionRequest[] = [];

        if (need3PointsConstruct) {
            // if we need to construct 3 points, do it directly
            constructionTriangle = this.from3Points(
                rv.map(p => p.name),
                rv[apex].name
            );
        } else {
            const isBase = isBaseConstruct(rv[b1], rv[b2], rv[apex], this.toolState.createFromBase);
            const { pcs } = await assignNameAndCreatePointConstructions(
                ctrl,
                selected[b1],
                selected[b2],
                selected[apex],
                isBase,
                this.toolbar.getTool('NamingElementTool') as NamingElementTool
            );

            constructionPoints.push(...pcs);

            if (isBase) {
                const nth = nthSideOfVector(ps, b1, b2, apex, rv[b1].relIndex, rv[b2].relIndex);
                const sideAngle = (Math.abs(vector(ps[b1], ps[apex]).angleTo(vector(ps[b1], ps[b2]))) / Math.PI) * 180;
                constructionTriangle = this.fromBaseNSideAngleConstruction(
                    `${rv[apex].name}${rv[b1].name}${rv[b2].name}`,
                    `${rv[b1].name}${rv[b2].name}`,
                    sideAngle,
                    rv[b1].name,
                    nth
                );
            } else {
                const nth = nthSideOfVector(ps, apex, b1, b2, rv[apex].relIndex, rv[b1].relIndex);
                const length = ps[b2].distanceTo(line(ps[apex], ps[b1]))[0];
                constructionTriangle = this.fromSideNSideLengthConstruction(
                    `${rv[apex].name}${rv[b1].name}${rv[b2].name}`,
                    `${rv[apex].name}${rv[b1].name}`, // side 1
                    `${rv[apex].name}${rv[b2].name}`, // side 2
                    length,
                    nth
                );
            }
        }

        try {
            await remoteConstruct(
                ctrl,
                constructionTriangle,
                constructionPoints,
                this.editor.geoGateway,
                'tam giác vuông'
            );
        } finally {
            this.resetState();
        }
    }

    private fromBaseNSideAngleConstruction(
        triangleName: string,
        lineName: string,
        sideAngle: number,
        anglePointName: string,
        nth: number
    ): GeoElConstructionRequest {
        if (sideAngle > 180) sideAngle = 360 - sideAngle;
        const construction = new GeoElConstructionRequest(
            'RightTriangle/RightTriangleEC',
            'RightTriangle',
            'HypotenuseAndAdjacentAngle'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-Hypotenuse',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'nameWithValue',
                optional: true,
                tplStrLangId: 'tpl-AdjacentAngle',
                params: {
                    value: {
                        type: 'singleValue',
                        value: sideAngle,
                    },
                    name: {
                        type: 'singleValue',
                        value: anglePointName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private fromSideNSideLengthConstruction(
        triangleName: string,
        side1: string,
        side2: string,
        length: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RightTriangle/RightTriangleEC',
            'RightTriangle',
            'AdjacentSideAndAdjacentSideLength'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-AdjacentSide',
                params: {
                    name: {
                        type: 'singleValue',
                        value: side1,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'lengthAssignment',
                optional: false,
                tplStrLangId: 'tpl-AdjacentSideLength',
                params: {
                    name: {
                        type: 'singleValue',
                        value: side2,
                    },
                    expression: {
                        type: 'singleValue',
                        value: length,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private from3Points(pointNames: string[], apexPointName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'RightTriangle/RightTriangleEC',
            'RightTriangle',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-AtPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: apexPointName,
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateIsoscelesTriangleTool extends BaseTriangleTool {
    readonly toolType: GeometryToolType = 'CreateRightTriangleTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.registerKeyboardHandling({
            event: 'keyup',
            keys: ['shift'],
            global: false,
        });
    }

    protected override createSelLogic(): void {
        this.selLogic = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) => (this.toolState.createFromBase ? perpBisectorTransform(f2p, el) : circleTransform(f2p, el)),
            (f2p, el) => (this.toolState.createFromBase ? perpBisectorCheck(f2p, el) : circleCheck(f2p, el)),
            {
                onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
            }
        );
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [
            ...(selector.selected[0] as SelectedVertex[]), // first two point from repeat selector
            selector.selected[1] as SelectedVertex, // last point
        ];

        // The list of render vertices in order of clicking
        const rv = selected.map(v => vert(v));
        const ps = rv.map(v => point(v.coords[0], v.coords[1]));
        const [apex, b1, b2] = isocelesTriangleBaseNApex(rv, this.toolState.createFromBase);

        /**
         * When do we need to create from 3 points?
         *
         * When ALL THREE POINTS are existing points, then we must create this triangle from 3 points, because they cannot
         * be established by any other rules. If one of the point is unknown, then we can use either of this constructions:
         *
         * - base and height
         * - side and apex angle
         *
         * To establish the position of the unknown point.
         *
         */
        const need3PointsConstruct = rv.every(p => p.name);
        let constructionTriangle: GeoElConstructionRequest;
        // constructions for possible unknown points

        const constructionPoints: GeoElConstructionRequest[] = [];

        if (need3PointsConstruct) {
            // if we need to construct 3 points, do it directly
            constructionTriangle = this.from3Points(
                rv.map(p => p.name),
                rv[apex].name
            );
        } else {
            // otherwise, determine the suitable base on the points we have

            const isBase = isBaseConstruct(rv[b1], rv[b2], rv[apex], this.toolState.createFromBase);

            const { pcs } = await assignNameAndCreatePointConstructions(
                ctrl,
                selected[b1],
                selected[b2],
                selected[apex],
                isBase,
                this.toolbar.getTool('NamingElementTool') as NamingElementTool
            );

            constructionPoints.push(...pcs);

            if (isBase) {
                const nth = nthSideOfVector(ps, b1, b2, apex, rv[b1].relIndex, rv[b2].relIndex);
                const height = vector(ps[apex], ps[b1]).add(vector(ps[b1], ps[b2]).multiply(0.5)).length;

                constructionTriangle = this.fromBaseNHeightConstruction(
                    `${rv[b1].name}${rv[b2].name}${rv[apex].name}`,
                    `${rv[b1].name}${rv[b2].name}`,
                    height,
                    nth
                );
            } else {
                const nth = nthSideOfVector(ps, apex, b1, b2, rv[apex].relIndex, rv[b1].relIndex);
                const angle = (Math.abs(vector(ps[apex], ps[b1]).angleTo(vector(ps[apex], ps[b2]))) / Math.PI) * 180;
                constructionTriangle = this.fromSideNApexAngle(
                    `${rv[apex].name}${rv[b1].name}${rv[b2].name}`,
                    `${rv[apex].name}${rv[b1].name}`,
                    angle,
                    rv[apex].name,
                    nth
                );
            }
        }

        try {
            await remoteConstruct(
                ctrl,
                constructionTriangle,
                constructionPoints,
                this.editor.geoGateway,
                'tam giác cân'
            );
        } finally {
            this.resetState();
        }
    }

    private fromBaseNHeightConstruction(
        triangleName: string,
        lineName: string,
        height: number,
        nth: number
    ): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'BaseLineSegmentAndHeightValue'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-BaseSideIs',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aValue',
                optional: false,
                tplStrLangId: 'tpl-HeightValue',
                params: {
                    value: {
                        type: 'singleValue',
                        value: height,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private fromSideNApexAngle(
        triangleName: string,
        lineName: string,
        apexAngle: number,
        apexName: string,
        nth: number
    ): GeoElConstructionRequest {
        if (apexAngle > 180) apexAngle = 360 - apexAngle;
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'SideLineSegmentApexAngle'
        );
        construction.name = triangleName;
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aLineSegment',
                optional: false,
                tplStrLangId: 'tpl-SideIs',
                params: {
                    name: {
                        type: 'singleValue',
                        value: lineName,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'nameWithValue',
                optional: true,
                tplStrLangId: 'tpl-ApexAngle',
                params: {
                    value: {
                        type: 'singleValue',
                        value: apexAngle,
                    },
                    name: {
                        type: 'singleValue',
                        value: apexName,
                    },
                },
            },
            {
                indexInCG: 2,
                paramDefId: 'aValue',
                optional: true,
                tplStrLangId: 'tpl-thShape',
                params: {
                    value: {
                        type: 'singleValue',
                        value: nth,
                    },
                },
            },
        ];

        return construction;
    }

    private from3Points(pointNames: string[], apexPointName: string): GeoElConstructionRequest {
        const construction = new GeoElConstructionRequest(
            'IsoscelesTriangle/IsoscelesTriangleEC',
            'IsoscelesTriangle',
            'FromPoints'
        );
        construction.name = pointNames.join('');
        construction.paramSpecs = [
            {
                indexInCG: 0,
                paramDefId: 'aPoint',
                optional: false,
                tplStrLangId: 'tpl-Points',
                params: {
                    name: {
                        type: 'array',
                        values: pointNames,
                    },
                },
            },
            {
                indexInCG: 1,
                paramDefId: 'aName',
                optional: false,
                tplStrLangId: 'tpl-AtPoint',
                params: {
                    name: {
                        type: 'singleValue',
                        value: apexPointName,
                    },
                },
            },
        ];

        return construction;
    }
}

export class CreateIsoscelesRightTriangleTool extends BaseTriangleTool {
    readonly toolType: GeometryToolType = 'CreateIsoscelesRightTriangleTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);

        this.registerKeyboardHandling({
            event: 'keyup',
            keys: ['shift'],
            global: false,
        });
    }

    protected override createSelLogic(): void {
        this.selLogic = triangleWithProj(
            this.pQ,
            this.pointerHandler.cursor,
            (f2p, el) => {
                if (this.toolState.createFromBase) return halfCircleTransform(f2p, perpBisectorTransform(f2p, el));
                else {
                    // use the closer point as the center
                    const ctIdx = closerPoint(f2p, el);
                    return circleTransform(f2p, perpLinesTransform(f2p, el), ctIdx);
                }
            },
            (f2p, el) =>
                this.toolState.createFromBase
                    ? perpBisectorCheck(f2p, el) && halfCircleCheck(f2p, el)
                    : perpLinesCheck(f2p, el) && circleCheck(f2p, el),
            {
                onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
            }
        );
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [
            ...(selector.selected[0] as SelectedVertex[]), // first two point from repeat selector
            selector.selected[1] as SelectedVertex, // last point
        ];

        // The list of render vertices in order of clicking
        const rv = selected.map(v => vert(v));
        const ps = rv.map(v => point(v.coords[0], v.coords[1]));
        const [apex, b1, b2] = isocelesTriangleBaseNApex(rv, this.toolState.createFromBase); // determine base order

        /**
         * When do we need to create from 3 points?
         *
         * When ALL THREE POINTS are existing points, then we must create this triangle from 3 points, because they cannot
         * be established by any other rules. If one of the point is unknown, then we can use either of this constructions:
         *
         * - base and height
         * - side and apex angle
         *
         * To establish the position of the unknown point.
         *
         */
        const need3PointsConstruct = rv.every(p => p.name);
        let constructionTriangle: GeoElConstructionRequest;
        // constructions for possible unknown points

        const constructionPoints: GeoElConstructionRequest[] = [];

        if (need3PointsConstruct) {
            // if we need to construct 3 points, do it directly
            // constructionTriangle = this.from3Points(
            //     rv.map(p => p.name),
            //     rv[apex].name
            // );
        } else {
            // otherwise, determine the suitable base on the points we have

            const isBase = isBaseConstruct(rv[b1], rv[b2], rv[apex], this.toolState.createFromBase);

            const { pcs } = await assignNameAndCreatePointConstructions(
                ctrl,
                selected[b1],
                selected[b2],
                selected[apex],
                isBase,
                this.toolbar.getTool('NamingElementTool') as NamingElementTool
            );

            constructionPoints.push(...pcs);

            if (isBase) {
                const nth = nthSideOfVector(ps, b1, b2, apex, rv[b1].relIndex, rv[b2].relIndex);
                const height = vector(ps[apex], ps[b1]).add(vector(ps[b1], ps[b2]).multiply(0.5)).length;

                // constructionTriangle = this.fromBaseNHeightConstruction(
                //     `${rv[b1].name}${rv[b2].name}${rv[apex].name}`,
                //     `${rv[b1].name}${rv[b2].name}`,
                //     height,
                //     nth
                // );
            } else {
                const nth = nthSideOfVector(ps, apex, b1, b2, rv[apex].relIndex, rv[b1].relIndex);
                const angle = (Math.abs(vector(ps[apex], ps[b1]).angleTo(vector(ps[apex], ps[b2]))) / Math.PI) * 180;
                // constructionTriangle = this.fromSideNApexAngle(
                //     `${rv[apex].name}${rv[b1].name}${rv[b2].name}`,
                //     `${rv[apex].name}${rv[b1].name}`,
                //     angle,
                //     rv[apex].name,
                //     nth
                // );
            }
        }

        try {
            // await remoteConstruct(
            //     ctrl,
            //     constructionTriangle,
            //     constructionPoints,
            //     this.editor.geoGateway,
            //     'tam giác vuông cân'
            // );
        } finally {
            this.resetState();
        }
    }
}

export class CreateEquilateralTriangleTool extends BaseTriangleTool {
    readonly toolType: GeometryToolType = 'CreateEquilateralTriangleTool';

    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
    }

    protected override createSelLogic(): void {
        this.selLogic = triangleWithProj(this.pQ, this.pointerHandler.cursor, equilateralTransform, equilateralCheck, {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    async performConstruction(selector: ThenSelector, ctrl: GeoDocCtrl) {
        const selected = [
            ...(selector.selected[0] as SelectedVertex[]), // first two point from repeat selector
            selector.selected[1] as SelectedVertex, // last point
        ];

        const rv = selected.map(v => vert(v));

        try {
            // await remoteConstruct(
            //     ctrl,
            //     constructionTriangle,
            //     constructionPoints,
            //     this.editor.geoGateway,
            //     'tam giác vuông cân'
            // );
        } finally {
            this.resetState();
        }
    }
}

function isBaseConstruct(b1: RenderVertex, b2: RenderVertex, apex: RenderVertex, userChooseBase: boolean) {
    const knownBasePoints = [b1, b2].filter(p => p.relIndex >= 0).length;

    // if both base points are existing points, there is no way but to create from base
    // if one of the base point is not known, and apex IS KNOWN, we must create from side
    // otherwise, we create from user instruction
    let isBase = userChooseBase;
    if (knownBasePoints == 2) isBase = true;
    else if (apex.relIndex >= 0) isBase = false;
    return isBase;
}

async function assignNameAndCreatePointConstructions(
    ctrl: GeoDocCtrl,
    b1: SelectedVertex,
    b2: SelectedVertex,
    apex: SelectedVertex,
    isBase: boolean,
    namingTool: NamingElementTool
): Promise<{ pcs: GeoElConstructionRequest[]; points: RenderVertex[] }> {
    const { pcs, points } = await assignNames(ctrl, [apex, b1, b2], namingTool, 'Tam giac'); // we assign names for all points

    if (!pcs) return { pcs: undefined, points: undefined };

    const pointsNeedConstructions = isBase ? [vert(b1).name, vert(b2).name] : [vert(apex).name, vert(b1).name];

    // need to filter out all points that needs not construct
    return { pcs: pcs.filter(c => pointsNeedConstructions.includes(c.name)), points: points };
}

/**
 * Given a list of selected render vertex, and the first two vertex is base or not,
 * determine which point is the vertex, and which points are the two points at the base
 * of the right triangle. If one and only one of the two points among the bases are known,
 *  use it as the first base point.
 *
 * @param rv List of render vertex
 * @param fromBase hint of user action. true: user create first two point as base. false, user create first two point as a side
 * @returns the index of the apex, base 1, base 2 poitns in the original array
 */
export function rightTriangleBaseNApex(rv: RenderVertex[], fromBase: boolean): [number, number, number] {
    // otherwise, determine the suitable base on the points we have
    const ps = rv.map(v => point(v.coords[0], v.coords[1]));
    let apex: number, b1: number, b2: number; // apex, base point 1, base point 2

    // determine the apex, and two base point base on the user input
    if (fromBase) {
        //
        // if create from base, the 3rd point is the apex
        apex = 2;
        b1 = 0;
        b2 = 1;
    } else {
        const d0 = Math.abs(ps[2].distanceTo(ps[0])[0]);
        const d1 = Math.abs(ps[2].distanceTo(ps[1])[0]);

        if (d0 < d1) {
            // ps[2] distance to point 0 is the side length, so point 0 is the apex
            apex = 0;
            b1 = 1;
            b2 = 2;
        } else {
            apex = 1;
            b1 = 0;
            b2 = 2;
        }
    }

    // if we create from base, and one and only one of the points is known, make sure it is used in the base and the other one is constrained
    // if we create from the side, and one and only one of the points is known, make sure it is used in the side and the other one is constrained
    // hence, we swap b1 and b2 if b2 is known and b1 is not known to ensure b1 is the known point, if both are unknown, the order remains the same
    if (rv[b2].relIndex >= 0 && rv[b1].relIndex < 0) {
        const temp = b2;
        b2 = b1;
        b1 = temp;
    }

    return [apex, b1, b2];
}

export function isocelesTriangleBaseNApex(rv: RenderVertex[], userChooseBase: boolean): [number, number, number] {
    const ps = rv.map(v => point(v.coords[0], v.coords[1]));
    let apex: number, b1: number, b2: number; // apex, base point 1, base point 2

    // determine the apex, and two base point base on the user input
    if (userChooseBase) {
        // if create from base, the 3rd point is the apex
        apex = 2;
        b1 = 0;
        b2 = 1;
    } else {
        // deternmin the apex by checking distance of the point 3 to the other two points
        const sideLength = ps[0].distanceTo(ps[1])[0];
        const diff1 = Math.abs(ps[2].distanceTo(ps[0])[0] - sideLength);
        const diff2 = Math.abs(ps[2].distanceTo(ps[1])[0] - sideLength);

        if (diff1 < diff2) {
            // ps[2] distance to point 0 is the side length, so point 0 is the apex
            apex = 0;
            b1 = 1;
            b2 = 2;
        } else {
            apex = 1;
            b1 = 0;
            b2 = 2;
        }
    }

    // if we create from base, and one and only one of the points is known, make sure it is used in the base and the other one is constrained
    // if we create from the side, and one and only one of the points is known, make sure it is used in the side and the other one is constrained
    // hence, we swap b1 and b2 if b2 is known and b1 is not known to ensure b1 is the known point, if both are unknown, the order remains the same
    if (rv[b2].relIndex >= 0 && rv[b1].relIndex < 0) {
        const temp = b2;
        b2 = b1;
        b1 = temp;
    }

    return [apex, b1, b2];
}
