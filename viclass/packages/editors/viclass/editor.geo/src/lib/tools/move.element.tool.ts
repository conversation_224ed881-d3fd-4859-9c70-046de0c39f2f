import {
    ErrorHandlerDecorator,
    mouseLocation,
    PointerEventData,
    pointerTypeDyn,
    pointerType<PERSON>en,
    pointerType<PERSON>enMouse,
    Position,
    ScreenPosition,
    START_PAN_THRESHOLD,
    UIPointerEventData,
} from '@viclass/editor.core';
import { from, Subject, Subscription, throttleTime } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { syncEndPreviewModeCommand, syncRenderCommands } from '../cmd';
import { geoDefaultHandlerFn, GeoPointerNotInError } from '../error-handler';
import { GeometryTool, GeometryToolType, GeoPointerEvent, GeoRenderElement, RenderLine } from '../geo.api';
import { GeometryEditor } from '../geo.editor';
import { GeometryToolBar } from '../geo.toolbar';
import {
    GeoElConstructionRequest,
    RenderCircle,
    RenderEllipse,
    RenderLineSegment,
    RenderSector,
    RenderVertex,
} from '../model';
import {
    MovementCirclePath,
    MovementEllipsePath,
    MovementLinePath,
    MovementLineSegmentPath,
    MovementPathType,
    MovementSectorPath,
} from '../model/render.movement.path';
import { GeoDocCtrl } from '../objects';
import {
    addReconstructionHistoryItem,
    calculatePosInLayer,
    convertPointBySnapTool,
    getFocusDocCtrl,
    handleIfPointerNotInError,
    projectOnEl,
    validatePointerPos,
} from './util.tool';
import { pCircle, pEllipse, pLine, PreviewQueue, pVertex } from '../model/util.preview';

// Define the threshold constant
// pixels needs to be smaller or equal to start pan threshold of pan tool to give priority to this move element tool
const MOVE_THRESHOLD = START_PAN_THRESHOLD - 2;

/**
 * Interface to group related initial hit data for clarity and easier state management.
 */
interface InitialHitInfo {
    el: RenderVertex;
    ctrl: GeoDocCtrl;
    screenPos: ScreenPosition;
}

/**
 * Tool for moving geometric elements with various movement constraints
 * Supports movement along:
 * - Line segments
 * - Infinite lines
 * - Circular arcs/sectors
 * - Full circles
 * - Ellipses
 */
export class MoveElementTool extends GeometryTool<any> {
    readonly toolType: GeometryToolType = 'MoveElementTool';

    // Preview elements for visual feedback during movement
    private previewPoint?: RenderVertex; // Shows where element will move to
    private movementPath?: GeoRenderElement; // Visual guide showing allowed movement path

    // RxJS subjects for handling movement events
    private src!: Subject<{ doc: GeoDocCtrl; pos: number[] }>;
    private dest!: Subscription;
    private moved: boolean = false; // Tracks if element has been moved from original position

    private thresholdPassed: boolean = false; // Track if threshold is met
    private pickedEl?: RenderVertex; // Currently selected element being moved

    // Group initial hit information into a single object
    private initialHit?: InitialHitInfo;

    private pQ = new PreviewQueue();

    /**
     * Initializes the Move Element Tool and registers all necessary mouse event handlers
     * @param editor The geometry editor instance
     * @param toolbar The geometry toolbar instance
     */
    constructor(editor: GeometryEditor, toolbar: GeometryToolBar) {
        super(editor, toolbar);
        // Register handlers for mouse events with and without button pressed
        this.registerPointerHandling(
            // move element tool only listening to trigger event globally
            // once element is picked, it should focus transiently to receive movement event.
            { event: 'pointerdown', keys: ['nokey'], button: 0, pointerTypes: pointerTypePenMouse, global: true },
            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1, global: true },
            { event: 'pointerdown', pointerTypes: pointerTypePen, numPointer: 1, global: true },

            // Listen globally for mouse move *with primary button down* to check threshold
            {
                event: 'pointermove',
                pressedButtons: 1, // Primary button pressed
                pointerTypes: ['mouse'],
                keys: ['nokey'],
                global: true, // Listen globally initially
            },

            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn, global: true },
            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen, global: true },

            // --- Local Handlers (Require Tool Focus - after threshold) ---
            { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypePenMouse, keys: ['nokey'] },
            { event: 'pointerup', button: 0, pointerTypes: pointerTypePenMouse },

            { event: 'pointermove', numPointer: 1, pointerTypes: pointerTypePen },
            { event: 'pointermove', numTouch: 1, pointerTypes: pointerTypeDyn },
            { event: 'pointerup', numTouch: 0, pointerTypes: pointerTypeDyn },
            { event: 'pointerup', pointerTypes: pointerTypePen }
        );
    }

    protected override readonly filterElementFunc = (el: GeoRenderElement) => el.type === 'RenderVertex';

    override onFocus() {
        console.log('Focus move element tool');
    }

    override onBlur() {
        console.log('Blur move element tool');
    }

    override resetState() {
        if (this.toolbar?.isToolActive(this.toolType)) {
            this.toolbar.blur(this.toolType, true); // Ensure transient focus is removed
        }
        this.internalResetState();
        super.resetState();
    }

    /**
     * Cleans up the tool's internal state variables and ends any active sequences
     */
    private internalResetState() {
        console.log('Remove internal state');
        this.moved = false; // Reset movement flag
        this.previewPoint = undefined; // Clear preview point
        this.pickedEl = undefined; // Clear selected element
        this.movementPath = undefined; // Clear movement path guide
        this.initialHit = undefined; // Clear initial hit info
        this.thresholdPassed = false; // Reset threshold flag
        this.endSequenceAndFlush(); // Clean up RxJS subscriptions
        this.editor.resetFilterElementFunc(); // Reset element filtering
    }

    /**
     * Starts a throttled sequence for processing move operations.
     * Uses RxJS throttleTime to limit the rate of server calls during rapid move events.
     * @param dueTime The throttle duration in milliseconds.
     */
    startSequence(dueTime: number) {
        this.src = new Subject();
        this.dest = this.src
            .pipe(
                // throttleTime phù hợp hơn cho các sự kiện liên tục như kéo thả. Nó giới hạn
                // tần suất thực thi ở mức tối đa một sự kiện trong mỗi `dueTime` mili giây.
                // Cấu hình `{ leading: false, trailing: true }` đảm bảo rằng sự kiện cuối cùng
                // trong một chuỗi di chuyển luôn được xử lý, mang lại cảm giác phản hồi nhanh
                // trong khi vẫn rất hiệu quả.
                throttleTime(dueTime, undefined, { leading: false, trailing: true }),

                // switchMap vẫn là lựa chọn tốt nhất ở đây để xử lý hoạt động bất đồng bộ.
                // Nếu một sự kiện mới (đã được throttle) đến trong khi `doMove` trước đó
                // vẫn đang chạy, nó sẽ hủy bỏ cái cũ và chuyển sang cái mới.
                switchMap(e => from(this.doMove(e.doc, e.pos)))
            )
            .subscribe();
    }

    /**
     * Adds a new position to the movement sequence
     * @param e Object containing document controller and position data
     */
    combine(e: { doc: GeoDocCtrl; pos: number[] }) {
        this.src?.next(e); // Push new position to subject
    }

    /**
     * Cleans up and unsubscribes from active sequences and subscriptions
     */
    endSequenceAndFlush() {
        // Clean up subject if it exists
        if (this.src) {
            this.src.complete();
            if (!this.src.closed) this.src.unsubscribe(); // Ensure unsubscribe if not already closed
            delete this.src;
        }
        // Clean up subscription if it exists
        if (this.dest) {
            this.dest.unsubscribe();
            delete this.dest;
        }
    }

    /**
     * Routes mouse events to appropriate handlers based on event type
     * @param event The geometric mouse event to handle
     * @returns The original or modified event
     */
    override handlePointerEvent(event: PointerEventData<any>): PointerEventData<any> {
        if (this.toolbar.activeTool && this.toolbar.curTool != this.toolType) return event; // Ignore if another tool is active
        switch (event.eventType) {
            case 'pointerdown': {
                // If an element is already picked, it means we are in a drag state
                // and a new pointerdown should not interrupt it.
                // This can happen if a second finger touches down during a drag.
                if (this.pickedEl) return event;
                this.onPointerDown(event);
                break;
            }
            case 'pointermove': {
                this.onPointerMove(event);
                break;
            }
            case 'pointerup': {
                this.onPointerUp(event);
                break;
            }
            default:
                break;
        }
        return event;
    }

    /**
     * Handles element selection and initializes movement path previews on mouse down
     * Sets up appropriate preview elements based on the type of movement path
     * @param event The mouse down event
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private onPointerDown(event: UIPointerEventData<any>) {
        if (!this.shouldHandleClick(event)) return;

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();

        const hitEl = this.editor.checkHitInternal(ctrl.layers[0], event)?.hitDetails?.el;

        // If there is an existing state from a previous interaction (e.g., pointerup was missed), reset it.
        if (this.initialHit || this.pickedEl) {
            this.resetState();
        }

        if (!this.isValidVertex(hitEl)) {
            // If no valid vertex is hit, and we had an initial hit that didn't lead to a drag,
            // ensure state is clean.
            if (this.initialHit) {
                this.resetState();
            }
            return;
        }

        // Store all initial hit information in one object
        this.initialHit = {
            el: hitEl as RenderVertex,
            ctrl: ctrl,
            screenPos: { x: event.nativeEvent.clientX, y: event.nativeEvent.clientY },
        };
        this.thresholdPassed = false;

        // Prevent other tools from processing this down event further
        event.continue = false;
    }

    /**
     * Checks if the hit element is a valid vertex with a movement path
     */
    private isValidVertex(hitEl: GeoRenderElement | undefined): boolean {
        return hitEl?.type === 'RenderVertex' && !!(hitEl as RenderVertex).movementPath;
    }

    /**
     * Sets up initial state for movement operation
     */
    private setupInitialState(vertex: RenderVertex, ctrl: GeoDocCtrl) {
        this.editor.clearSelectedElInDoc(ctrl);
        this.pickedEl = vertex;
        // Giảm các lệnh gọi đến máy chủ.
        // Giá trị này có thể được điều chỉnh để cân bằng giữa độ phản hồi và hiệu suất.
        this.startSequence(200);
    }

    /**
     * Creates preview visualization for movement path based on path type
     */
    private async createMovementPathPreview(vertex: RenderVertex, ctrl: GeoDocCtrl) {
        switch (vertex.movementPath.type) {
            case 'MovementCirclePath': {
                const path = vertex.movementPath as MovementCirclePath;
                this.movementPath = pCircle(ctrl, -2000, path.pc, undefined, path.radius);
                break;
            }
            case 'MovementEllipsePath': {
                const path = vertex.movementPath as MovementEllipsePath;
                this.movementPath = pEllipse(ctrl, -2000, path.f1, path.f2, null, path.a, path.b, path.rotate);
                break;
            }
            case 'MovementSectorPath': {
                const path = vertex.movementPath as MovementSectorPath;
                // this.movementPath = pSector(ctrl, -2000, path.ps, path.pc, path.pe);
                break;
            }
            case 'MovementLinePath': {
                const path = vertex.movementPath as MovementLinePath;
                this.movementPath = pLine(ctrl, -2000, RenderLine, path.root, undefined, path.parallelVector);
                break;
            }
            case 'MovementLineSegmentPath': {
                const path = vertex.movementPath as MovementLineSegmentPath;
                this.movementPath = pLine(ctrl, -2000, RenderLineSegment, path.p1, path.p2);
                break;
            }
        }
    }

    /**
     * Handles mouse movement with caching to prevent reflow
     * Delegates to mouseMoveCallback for actual processing
     * @param event The mouse move event
     */
    private onPointerMove(event: UIPointerEventData<any>) {
        // If we are already dragging (pickedEl is set), continue with drag handling.
        if (this.pickedEl) {
            this.handleDrag(event);
            return;
        }

        // If there was no initial hit, do nothing.
        if (!this.initialHit) return;

        // Check if the movement threshold has been passed.
        const currentPos = { x: event.nativeEvent.clientX, y: event.nativeEvent.clientY };
        const dx = Math.abs(currentPos.x - this.initialHit.screenPos.x);
        const dy = Math.abs(currentPos.y - this.initialHit.screenPos.y);

        if (dx > MOVE_THRESHOLD || dy > MOVE_THRESHOLD) {
            this.beginDrag(event);
        }
        // If threshold not met, allow event to propagate for other tools like panning.
    }

    /** Begins the drag operation once the move threshold is passed. */
    private beginDrag(event: UIPointerEventData<any>) {
        if (!this.initialHit) return; // Should not happen, but a good safeguard

        this.thresholdPassed = true;
        this.toolbar.focus(this.toolType, true); // Gain transient focus

        this.setupInitialState(this.initialHit.el, this.initialHit.ctrl);
        this.createMovementPathPreview(this.initialHit.el, this.initialHit.ctrl);
        this.previewPoint = pVertex(-1000, this.initialHit.el.coords);
        this.editor.filterElementFunc = el => el === this.pickedEl;

        // Process this first move event immediately
        this.handleDrag(event);

        // Clear the initial hit info as we have now transitioned to the 'picked' state
        this.initialHit = undefined;
    }

    /** Handles the actual element dragging and preview updates. */
    private handleDrag(event: UIPointerEventData<any>) {
        this.pointerMoveCachingReflowSync.handleEvent(event, this.pointerMoveCB);
        event.continue = false; // Tool is focused and handling it
        event.nativeEvent.preventDefault();
    }

    /**
     * Error handling wrapper for mouse move processing
     * @param event The mouse move event
     */
    pointerMoveCB = (event: GeoPointerEvent) => {
        return handleIfPointerNotInError(this, () => {
            this.processMouseMove(event);
        });
    };

    /**
     * Processes mouse movement events for the geometry editor.
     *
     * This method handles different interactions based on the current state:
     * 1. When no element is picked: Creates preview points when hovering over movable vertices
     * 2. When an element is picked: Calculates the projection of the mouse position onto
     *    the element's movement path and updates the preview point
     *
     * Supports multiple movement path types:
     * - MovementLineSegmentPath: Constrains movement to a line segment
     * - MovementLinePath: Constrains movement to a line
     * - MovementSectorPath: Constrains movement to a sector between two angles
     * - MovementCirclePath: Constrains movement to a circle
     * - MovementEllipsePath: Constrains movement to an ellipse
     *
     * Updates preview visualization and synchronizes commands with the controller.
     *
     * @param event - The geometric mouse event containing position and state information
     * @returns The hit element or the element being processed
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private processMouseMove(event: GeoPointerEvent) {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) throw new GeoPointerNotInError();
        const renderer = ctrl.rendererCtrl;
        const pointerPos = mouseLocation(event);
        if (!validatePointerPos(pointerPos, ctrl)) {
            // For the purpose of this tool, if pointer is out of bounds during drag, throw error.
            throw new GeoPointerNotInError();
        }
        const pointerPosInLayer = calculatePosInLayer(pointerPos, ctrl);
        const pointerPosInGeo = convertPointBySnapTool(ctrl, renderer.layerToGeoPos(pointerPosInLayer));

        if (!this.pickedEl || !this.pickedEl.movementPath) return;

        this.moved = true; // Mark that actual movement has occurred
        const p = this.calculateNewPosition(pointerPosInGeo, this.pickedEl.movementPath.type, ctrl);
        if (!p) return;

        this.updatePreview(p, ctrl);
    }

    private calculateNewPosition(pos: Position, type: MovementPathType, ctrl: GeoDocCtrl): number[] | null {
        let p = [pos.x, pos.y, pos.z ?? 0];

        switch (type) {
            case 'MovementLineSegmentPath':
                p = projectOnEl(this.movementPath as RenderLineSegment, p, ctrl.rendererCtrl);
                break;
            case 'MovementLinePath':
                p = projectOnEl(this.movementPath as RenderLine, p, ctrl.rendererCtrl);
                break;
            case 'MovementSectorPath':
                p = projectOnEl(this.movementPath as RenderSector, p, ctrl.rendererCtrl);
                break;
            case 'MovementCirclePath':
                p = projectOnEl(this.movementPath as RenderCircle, p, ctrl.rendererCtrl);
                break;
            case 'MovementEllipsePath':
                p = projectOnEl(this.movementPath as RenderEllipse, p, ctrl.rendererCtrl);
                break;
            default:
                break;
        }

        return p;
    }

    private updatePreview(p: number[], ctrl: GeoDocCtrl) {
        if (!this.previewPoint) return; // Safeguard
        this.previewPoint.coords = p;
        this.combine({ doc: ctrl, pos: p });
        this.pQ.add(this.previewPoint, this.movementPath);
        this.pQ.flush(ctrl);
    }

    /**
     * Executes the actual element movement operation
     * Updates the element position and refreshes preview visualizations
     * @param ctrl The document controller
     * @param pos The new position coordinates
     */
    private async doMove(ctrl: GeoDocCtrl, pos: number[]) {
        if (!this.moved || !this.pickedEl) return;
        const m = await this.editor.geoGateway.moveElement(ctrl.state.globalId, this.pickedEl.relIndex, pos);
        // Update rendering
        if (this.pickedEl?.renderProp) this.pickedEl.renderProp.hidden = true;
        await syncRenderCommands(m.render, ctrl);
    }

    /**
     * Handles mouse up events to complete the movement operation
     * Reconstructs the element at its new position and adds to history
     * Selects the modified element after movement is complete
     * @param event The mouse up event
     */
    @ErrorHandlerDecorator([geoDefaultHandlerFn])
    private async onPointerUp(event: UIPointerEventData<any>) {
        // If no element was being dragged (or threshold wasn't met), just clean up and exit.
        if (!this.pickedEl) {
            if (this.initialHit) {
                // This case is a click that didn't pass the move threshold.
                // No action is needed, just reset the state.
                this.resetState();
            }
            return;
        }

        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        if (!ctrl?.state) {
            this.resetState(); // Reset state if pointer up is outside a valid viewport
            throw new GeoPointerNotInError();
        }

        // The interaction is ending, so remove transient focus.
        this.toolbar.blur(this.toolType, true);

        let movedSuccessfully = false;
        if (this.moved) {
            // Check if actual movement occurred (beyond threshold and processMouseMove was called)
            const pointerPos = mouseLocation(event);
            if (!validatePointerPos(pointerPos, ctrl)) {
                this.resetState();
                throw new GeoPointerNotInError();
            }
            const pointerPosInLayer = calculatePosInLayer(pointerPos, ctrl);
            const finalGeoPos = convertPointBySnapTool(ctrl, ctrl.rendererCtrl.layerToGeoPos(pointerPosInLayer));

            try {
                await this.finalizeMove(ctrl, finalGeoPos);
                movedSuccessfully = true;
            } catch (err) {
                console.log('reconstruct element error', err);
            }
        } else {
            // If pickedEl was set but no actual movement occurred (e.g., just a click-and-release without drag)
            // Clean up preview elements if they were created.
            if (this.movementPath || this.previewPoint) {
                await syncEndPreviewModeCommand(ctrl);
            }
        }

        // Always reset the tool's state on pointer up.
        this.resetState();

        // After a successful move, re-evaluate the element under the cursor and select it.
        if (movedSuccessfully) {
            const hitCtx = this.editor.checkHitInternal(ctrl.layers[0], event);
            this.editor.selectElement(hitCtx);
        }
    }

    /**
     * Completes the move operation by updating the backend, reconstructing the element,
     * and adding the action to the history stack.
     * @param ctrl The document controller.
     * @param finalGeoPos The final geometric position for the element.
     */
    private async finalizeMove(ctrl: GeoDocCtrl, finalGeoPos: Position) {
        if (!this.pickedEl?.movementPath) return; // Should not happen if pickedEl is valid

        const finalCoords = this.calculateNewPosition(finalGeoPos, this.pickedEl.movementPath.type, ctrl) ?? [
            finalGeoPos.x,
            finalGeoPos.y,
            finalGeoPos.z ?? 0,
        ];

        const relIndex = this.pickedEl.relIndex;

        // 1. Move element to its final position.
        const moveResult = await this.editor.geoGateway.moveElement(ctrl.state.globalId, relIndex, finalCoords);
        const newCt = moveResult.newConstruction;

        // 2. Create a reconstruction request.
        const req = new GeoElConstructionRequest(newCt.ctId, newCt.elType, newCt.cgName, newCt.name, newCt.paramSpecs);

        // 3. Reconstruct the element at the new position.
        const reconstructResult = await this.editor.geoGateway.reconstruct(ctrl.state.globalId, newCt.ctIdx, req);
        const oldCt = reconstructResult.oldConstruction;

        // 4. Add the operation to the history stack for undo/redo.
        addReconstructionHistoryItem(
            ctrl,
            newCt.ctIdx,
            newCt.ctId,
            newCt.elType,
            newCt.cgName,
            newCt.name,
            oldCt.paramSpecs,
            newCt.paramSpecs
        );

        // 5. Update the rendering.
        await syncRenderCommands(reconstructResult.render, ctrl);
    }
}
