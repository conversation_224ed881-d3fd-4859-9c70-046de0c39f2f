import {
    BaseBoardViewportManager,
    FEATURE_ZOOM,
    GraphicLayerCtrl,
    Position,
    Rectangle,
    ViewportManager,
} from '@viclass/editor.core';
import {
    GeoObjCollection,
    GeoRenderElement,
    GeoStrokeStyle,
    LineSegmentGeoRenderProp,
    RenderAngle,
    RenderCircle,
    RenderCircleShape,
    RenderEllipse,
    RenderEllipseShape,
    RenderLine,
    RenderLineSegment,
    RenderPolygon,
    RenderSector,
    RenderSectorShape,
    RenderVector,
    RenderVertex,
} from '../model';
import { GeoDocCtrl } from '../objects';
import {
    buildPreviewAngleRenderProp,
    buildPreviewCircleRenderProp,
    buildPreviewCircleShapeRenderProp,
    buildPreviewEllipseRenderProp,
    buildPreviewEllipseShapeRenderProp,
    buildPreviewLineRenderProp,
    buildPreviewPolygonRenderProp,
    buildPreviewSectorRenderProp,
    buildPreviewSectorShapeRenderProp,
    buildPreviewVertexRenderProp,
    distance2Point,
    isValidIdx,
    mapGetOrSet,
} from '../tools/util.tool';
import { GeoRenderer } from './geo.renderer';
import {
    calculateAngle,
    calculateBottomLeftByAngleXYSize,
    calculateSpacing,
    drawArc,
    drawLabel,
    drawLine,
    drawParallelLines,
    getAngleWithOx,
    getLabelCenterPointsFromVertex,
    getLabelMeasure,
    getXYFromAngleAndVertex,
} from './geo2d.renderer.utils';

type PendingRenderLabel = {
    text: string;
    vertexPoint: DOMPoint;
    object: GeoRenderElement;
    highlight?: boolean;
};

type RenderedLabel = {
    bottomLeftPoint: DOMPoint;
    width: number;
    height: number;
};

enum LineWeight {
    DetailGrid = 0.5,
    Grid = 1,
    Axis = 1.5,
}

enum LineColor {
    Grid = 'lightgray',
    Axis = 'black',
}

// TODO: change to setting field
export const RENDER_VERTEX_RADIUS = 5;
export const SCALE_RESISTANCE = 0.8;

export const RENDER_VERTEX_RADIUS_POTENTIAL = 30;

// Scaling factor for resistance based on potential.
// Set to 1 to maintain the actual size of potential elements
// regardless of viewport scaling or resizing.
export const SCALE_RESISTANCE_POTENTIAL = 1;
export const RENDER_VERTEX_PADDING_SCALE = 1.8;

/**
 *
 * <AUTHOR>
 */
export class Geo2dRenderer extends GeoRenderer {
    declare context: CanvasRenderingContext2D;
    private highlightRefEls: Map<'Vertex' | 'Line' | 'Arc', Map<number, GeoRenderElement>> = new Map();
    private pendingRenderlabels: PendingRenderLabel[] = [];
    private renderedlabels: RenderedLabel[] = [];
    private checkOverlapStateList: GeoRenderElement[] = [];
    private labelPosCache: DOMPoint[] = [];
    private centerOfVertexs: DOMPoint;
    protected oldLayerSize: {
        width: number;
        height: number;
    } = {
        width: 0,
        height: 0,
    };
    protected oldViewportScale: number = 0;
    protected oldDocScale: number = 0;
    protected oldLookAt: Position = { x: 0, y: 0 };
    private requestedRenderFrame: boolean = false;

    constructor(
        override layer: GraphicLayerCtrl,
        doc: GeoDocCtrl
    ) {
        super(doc, layer);

        this.context = layer.canvas.getContext('2d');

        layer.attachRenderer((vpm: ViewportManager, lc: GraphicLayerCtrl) => this.render());
    }

    scheduleRender() {
        if (this.requestedRenderFrame) return;
        requestAnimationFrame(() => {
            this.requestedRenderFrame = false;
            this.layer.renderer(this.layer.viewport, this.layer);
        });
        this.requestedRenderFrame = true;
    }

    isContentVisible(): boolean {
        const docRenderProp = this.docCtrl.state.docRenderProp;
        return (
            docRenderProp?.border ||
            docRenderProp?.shadow ||
            docRenderProp?.grid ||
            docRenderProp?.detailGrid ||
            docRenderProp?.axis ||
            this.originObjects.size > 0
        );
    }

    private useCanvasCoord() {
        this.context.setTransform(1, 0, 0, 1, 0, 0);
    }

    override clearBoard() {
        this.context.save();

        // Use the identity matrix while clearing the canvas
        this.useCanvasCoord();
        this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // Restore the transform
        this.context.restore();
    }

    override render() {
        this.clearBoard();

        this.checkOverlapStateList = [];
        this.renderedlabels = [];
        this.pendingRenderlabels = [];

        this.renderGrid();

        // reset cache if the collection is updated, zoom, pan and layer size is changed
        if (
            this.isCollectionUpdated ||
            this.oldLayerSize.width != this.width ||
            this.oldLayerSize.height != this.height ||
            this.oldViewportScale != this.getViewportScale() ||
            this.oldDocScale != this.getDocScale() ||
            this.oldLookAt.x != this.currLookAt.x ||
            this.oldLookAt.y != this.currLookAt.y
        ) {
            this.labelPosCache = [];
            this.centerOfVertexs = null;
            this.isCollectionUpdated = false;

            // update old values
            this.oldLayerSize.width = this.width;
            this.oldLayerSize.height = this.height;
            this.oldViewportScale = this.getViewportScale();
            this.oldDocScale = this.getDocScale();
            this.oldLookAt = { x: this.currLookAt.x, y: this.currLookAt.y };
        }

        this.renderStateCollection(this.originObjects, false);
        this.renderStateCollection(this.originObjects, true);
        this.renderStateCollection(this.previewObjects, false);

        // handle render vertexs latest at all
        const vertexs = this.originObjects.vertexes(false);
        const vertexsHighlight = this.originObjects.vertexes(true);
        const vertexsPreview = this.previewObjects.vertexes(false);

        for (const obj of vertexs) this.renderObject(obj, false);
        for (const obj of vertexsHighlight) this.renderObject(obj, true);
        for (const obj of vertexsPreview) this.renderObject(obj, false);

        this.checkOverlapStateList.push(...vertexs, ...vertexsHighlight, ...vertexsPreview);

        if (this.centerOfVertexs == null) this.centerOfVertexs = this.getCenterOfVertexs();

        for (const label of this.pendingRenderlabels)
            this.renderLabel(label.text, label.vertexPoint, label.object, label.highlight);
    }

    private renderStateCollection(state: GeoObjCollection, highlight: boolean = false) {
        const shapes = state.shapes(highlight).sort((a, b) => (a.area > b.area ? -1 : 1));
        const lines = state.lines(highlight).sort((a, b) => (a.length > b.length ? -1 : 1));
        const rays = state.rays(highlight).sort((a, b) => (a.length > b.length ? -1 : 1));
        const vectors = state.vectors(highlight).sort((a, b) => (a.length > b.length ? -1 : 1));
        const lineSegments = state.lineSegments(highlight).sort((a, b) => (a.length > b.length ? -1 : 1));
        const sectors = state.sectors(highlight).sort((a, b) => (a.length > b.length ? -1 : 1));
        const ellipses = state.ellipses(highlight).sort((a, b) => (a.length > b.length ? -1 : 1));
        const circles = state.circles(highlight).sort((a, b) => (a.length > b.length ? -1 : 1));
        const angles = state.angles(highlight);
        const potentials = this.potentialSelection;

        this.checkOverlapStateList.push(
            ...shapes,
            ...lines,
            ...rays,
            ...vectors,
            ...lineSegments,
            ...sectors,
            ...ellipses,
            ...circles,
            ...angles,
            ...potentials
        );

        if (highlight) this.highlightRefEls.clear();

        for (const obj of shapes) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of angles) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of ellipses) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of circles) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of sectors) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of lines) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of rays) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of lineSegments) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }
        for (const obj of vectors) {
            this.renderObject(obj, highlight);
            if (highlight) this.highlightRefObject(obj);
        }

        for (const obj of potentials) {
            this.renderPotentialSelection(obj);
        }
    }

    private highlightRefObject(obj: GeoRenderElement) {
        obj.vertexRelIdxes
            ?.map(idx => this.originObjects.elementAt(idx))
            ?.map(o => mapGetOrSet(this.highlightRefEls, 'Vertex', new Map()).set(o.relIndex, o));
        obj.lineRelIdxes
            ?.map(idx => this.originObjects.elementAt(idx))
            ?.map(o => mapGetOrSet(this.highlightRefEls, 'Line', new Map()).set(o.relIndex, o));
        if (isValidIdx(obj.arcRelIdx)) {
            const o = this.originObjects.elementAt(obj.arcRelIdx);
            if (o) mapGetOrSet(this.highlightRefEls, 'Arc', new Map()).set(o.relIndex, o);
        }
    }

    private renderObject(rel: GeoRenderElement, highlight: boolean = false) {
        switch (rel.type) {
            case 'RenderSectorShape':
                this.renderSectorShape(rel as RenderSectorShape);
                break;
            case 'RenderAngle':
                this.renderAngle(rel as RenderAngle, highlight);
                break;
            case 'RenderCircleShape':
                this.renderCircleShape(rel as RenderCircleShape, highlight);
                break;
            case 'RenderEllipseShape':
                this.renderEllipseShape(rel as RenderEllipseShape, highlight);
                break;
            case 'RenderPolygon':
                this.renderPolygon(rel as RenderPolygon, highlight);
                break;
            case 'RenderVector':
            case 'RenderLineSegment':
            case 'RenderRay':
            case 'RenderLine':
                this.renderLine(rel as RenderLine, highlight);
                break;
            case 'RenderVertex':
                this.renderVertex(rel as RenderVertex, highlight);
                break;
            case 'RenderSector':
                this.renderSector(rel as RenderSector, highlight);
                break;
            case 'RenderEllipse':
                this.renderEllipse(rel as RenderEllipse, highlight);
                break;
            case 'RenderCircle':
                this.renderCircle(rel as RenderCircle, highlight);
                break;
            default:
                break;
        }
    }

    /**
     * Draws a grid on the canvas with labeled axes. The grid spacing is adjusted based on the zoom level.
     *
     * @private
     * @return {void}
     */
    private renderGrid(): void {
        if (!this.viewport.currentLookAt) return;

        const scale = this.getScale();

        const leftGeo = this.layerToGeoX(this.left);
        const rightGeo = this.layerToGeoX(this.right);
        const topGeo = this.layerToGeoY(this.top);
        const bottomGeo = this.layerToGeoY(this.bottom);

        const noThinerLine = 5;

        // Adjust grid spacing based on zoom level
        const spacing = calculateSpacing(this.docCtrl, this.viewport);
        const startX = leftGeo > 0 ? Math.floor(leftGeo / spacing) * spacing : 0;
        const endX = rightGeo < 0 ? Math.floor(rightGeo / spacing + 1) * spacing : 0;
        const startY = bottomGeo > 0 ? Math.floor(bottomGeo / spacing) * spacing : 0;
        const endY = topGeo < 0 ? Math.floor(topGeo / spacing + 1) * spacing : 0;

        const drp = this.docCtrl.state.docRenderProp;

        for (let x = startX; x <= rightGeo; x += spacing) {
            if (drp?.grid)
                drawLine(this.context, this.geoToLayerX(x), this.top, this.geoToLayerX(x), this.bottom, {
                    lineWeight: LineWeight.DetailGrid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis) {
                drp?.axis &&
                    drawLine(
                        this.context,
                        this.geoToLayerX(x),
                        this.geoToLayerY(0),
                        this.geoToLayerX(x),
                        this.geoToLayerY(0) - 5 * scale,
                        {
                            lineWeight: LineWeight.Axis * scale,
                            color: LineColor.Axis,
                        }
                    );
                this.drawAxisLabel(this.context, this.geoToLayerX(x), this.geoToLayerY(0), x, scale, 'x');
            }
            if (drp?.detailGrid)
                for (let i = 0; i < noThinerLine; i++)
                    drawLine(
                        this.context,
                        this.geoToLayerX(x + (spacing * i) / noThinerLine),
                        this.top,
                        this.geoToLayerX(x + (spacing * i) / noThinerLine),
                        this.bottom,
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
        }
        for (let x = endX; x >= leftGeo; x -= spacing) {
            if (drp?.grid)
                drawLine(this.context, this.geoToLayerX(x), this.top, this.geoToLayerX(x), this.bottom, {
                    lineWeight: LineWeight.Grid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis) {
                drawLine(
                    this.context,
                    this.geoToLayerX(x),
                    this.geoToLayerY(0),
                    this.geoToLayerX(x),
                    this.geoToLayerY(0) - 5 * scale,
                    {
                        lineWeight: LineWeight.Axis * scale,
                        color: LineColor.Axis,
                    }
                );
                this.drawAxisLabel(this.context, this.geoToLayerX(x), this.geoToLayerY(0), x, scale, 'x');
            }
            if (drp?.detailGrid)
                for (let i = 0; i < 10; i++)
                    drawLine(
                        this.context,
                        this.geoToLayerX(x - (spacing * i) / noThinerLine),
                        this.top,
                        this.geoToLayerX(x - (spacing * i) / noThinerLine),
                        this.bottom,
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
        }

        for (let y = startY; y <= topGeo; y += spacing) {
            if (drp?.grid)
                drawLine(this.context, this.right, this.geoToLayerY(y), this.left, this.geoToLayerY(y), {
                    lineWeight: LineWeight.Grid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis) {
                drawLine(
                    this.context,
                    this.geoToLayerX(0),
                    this.geoToLayerY(y),
                    this.geoToLayerX(0) - 5 * scale,
                    this.geoToLayerY(y),
                    {
                        lineWeight: LineWeight.Axis * scale,
                        color: LineColor.Axis,
                    }
                );
                this.drawAxisLabel(this.context, this.geoToLayerX(0), this.geoToLayerY(y), y, scale, 'y');
            }
            if (drp?.detailGrid)
                for (let i = 0; i < 10; i++) {
                    drawLine(
                        this.context,
                        this.right,
                        this.geoToLayerY(y + (spacing * i) / noThinerLine),
                        this.left,
                        this.geoToLayerY(y + (spacing * i) / noThinerLine),
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
                }
        }
        for (let y = endY; y >= bottomGeo; y -= spacing) {
            if (drp?.grid)
                drawLine(this.context, this.right, this.geoToLayerY(y), this.left, this.geoToLayerY(y), {
                    lineWeight: LineWeight.Grid * scale,
                    color: LineColor.Grid,
                });
            if (drp?.axis) {
                drawLine(
                    this.context,
                    this.geoToLayerX(0),
                    this.geoToLayerY(y),
                    this.geoToLayerX(0) - 5 * scale,
                    this.geoToLayerY(y),
                    {
                        lineWeight: LineWeight.Axis * scale,
                        color: LineColor.Axis,
                    }
                );
                this.drawAxisLabel(this.context, this.geoToLayerX(0), this.geoToLayerY(y), y, scale, 'y');
            }
            if (drp?.detailGrid)
                for (let i = 0; i < 10; i++) {
                    drawLine(
                        this.context,
                        this.right,
                        this.geoToLayerY(y - (spacing * i) / noThinerLine),
                        this.left,
                        this.geoToLayerY(y - (spacing * i) / noThinerLine),
                        {
                            lineWeight: LineWeight.DetailGrid * scale,
                            color: LineColor.Grid,
                        }
                    );
                }
        }

        if (drp?.axis) {
            if (bottomGeo < 0 && topGeo > 0)
                drawLine(this.context, this.left, this.geoToLayerY(0), this.right, this.geoToLayerY(0), {
                    lineWeight: LineWeight.Axis * scale,
                    color: LineColor.Axis,
                });
            if (leftGeo < 0 && rightGeo > 0)
                drawLine(this.context, this.geoToLayerX(0), this.bottom, this.geoToLayerX(0), this.top, {
                    lineWeight: LineWeight.Axis * scale,
                    color: LineColor.Axis,
                });
        }
    }

    private drawAxisLabel(
        ctx: CanvasRenderingContext2D,
        x: number,
        y: number,
        label: string | number,
        scale: number,
        axis: 'x' | 'y'
    ) {
        if (typeof label === 'number') label = String(Math.round(label * 100) / 100);

        const fontSize = 14 * scale;
        const measure = getLabelMeasure(ctx, label, fontSize);
        const textOptions = {
            textColor: 'black',
            fontSize: fontSize,
        };

        if (label === '0') drawLabel(ctx, label, { x: x - 20 * scale, y: y - 20 * scale }, textOptions);
        else if (axis === 'x') drawLabel(ctx, label, { x: x - measure.width / 2, y: y - 20 * scale }, textOptions);
        else if (axis === 'y')
            drawLabel(ctx, label, { x: x - measure.width - 15 * scale, y: y - fontSize / 2 }, textOptions);
    }

    private getCenterOfVertexs(): DOMPoint {
        // condition: this.pendingRenderlabels has transformed
        // so we don't need to transform it again
        const l = this.pendingRenderlabels.length;

        if (l == 0) return new DOMPoint(0, 0);

        let x = 0,
            y = 0;

        for (const obj of this.pendingRenderlabels) {
            x += obj.vertexPoint.x;
            y += obj.vertexPoint.y;
        }

        return new DOMPoint(x / l, y / l);
    }

    private renderEllipseShape(rel: RenderEllipseShape, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewEllipseShapeRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        // Get center coordinates from geometry function (handles all fallback logic internally)
        const centerCoords = rel.coord('center', this);
        if (!centerCoords || !Array.isArray(centerCoords) || centerCoords.length < 2) {
            return; // Skip rendering if center coordinates are not available
        }
        const centerPoint = this.geoToLayerCoord(centerCoords);

        this.context.save();
        this.context.beginPath();

        this.context.ellipse(
            centerPoint.x,
            centerPoint.y,
            this.geoToLayerLength(rel.a),
            this.geoToLayerLength(rel.b),
            rel.rotate,
            0,
            2 * Math.PI
        );

        this.context.setLineDash(this.lineDash(prop.strokeStyle, prop.lineWeight * this.getZoomLevel()));

        // setup background color by 'color' and 'opacity'
        const opacity = [null, undefined].includes(prop.opacity) ? 0 : Number(prop.opacity);
        this.context.fillStyle = prop.color;
        if (highlight) this.context.globalAlpha = (opacity + 5) / 100;
        else this.context.globalAlpha = opacity / 100;
        this.context.fill();

        this.context.closePath();
        this.context.restore();

        // Optional: Render mode-specific visual indicators for debugging/development
        // This can be removed in production or controlled by a debug flag
        if (rel.centerIdx !== -1 && rel.vaIdx !== -1 && rel.vbIdx !== -1) {
            // CENTER_VECTORS mode - optionally show center and vector points
            // This is mainly for visual debugging and can be customized as needed
            this.renderEllipseShapeModeIndicators(rel, centerPoint, this.getScale());
        }
        // FOCUS_POINTS mode - uses standard ellipse rendering (current behavior)
    }

    /**
     * Renders visual indicators for ellipse modes (optional, for debugging/development)
     * @param rel The ellipse render element
     * @param centerPoint The center point in layer coordinates
     * @param scale The current scale
     */
    private renderEllipseModeIndicators(rel: RenderEllipse, centerPoint: any, scale: number) {
        // This method can be used to show visual differences between modes
        // For now, it's a placeholder that can be extended as needed

        // Example: Show a small indicator for CENTER_VECTORS mode
        if (rel.centerIdx !== -1 && rel.vaIdx !== -1 && rel.vbIdx !== -1) {
            try {
                const vertexA = rel.coord('va', this);
                const vertexB = rel.coord('vb', this);

                // Draw small circles at vector endpoints if available (optional visual aid)
                this.context.save();
                this.context.fillStyle = 'rgba(255, 0, 0, 0.3)';

                if (vertexA && Array.isArray(vertexA) && vertexA.length >= 2) {
                    const va = this.geoToLayerCoord(vertexA);
                    this.context.beginPath();
                    this.context.arc(va.x, va.y, 3 * scale, 0, 2 * Math.PI);
                    this.context.fill();
                }

                if (vertexB && Array.isArray(vertexB) && vertexB.length >= 2) {
                    const vb = this.geoToLayerCoord(vertexB);
                    this.context.beginPath();
                    this.context.arc(vb.x, vb.y, 3 * scale, 0, 2 * Math.PI);
                    this.context.fill();
                }

                this.context.restore();
            } catch (e) {
                // Ignore geometry errors for visual indicators
            }
        }
    }

    /**
     * Renders visual indicators for ellipse shape modes (optional, for debugging/development)
     * @param rel The ellipse shape render element
     * @param centerPoint The center point in layer coordinates
     * @param scale The current scale
     */
    private renderEllipseShapeModeIndicators(rel: RenderEllipseShape, centerPoint: any, scale: number) {
        // This method can be used to show visual differences between modes
        // For now, it's a placeholder that can be extended as needed

        // Example: Show a small indicator for CENTER_VECTORS mode
        if (rel.centerIdx !== -1 && rel.vaIdx !== -1 && rel.vbIdx !== -1) {
            try {
                const vertexA = rel.coord('va', this);
                const vertexB = rel.coord('vb', this);

                // Draw small circles at vector endpoints if available (optional visual aid)
                this.context.save();
                this.context.fillStyle = 'rgba(0, 255, 0, 0.3)';

                if (vertexA && Array.isArray(vertexA) && vertexA.length >= 2) {
                    const va = this.geoToLayerCoord(vertexA);
                    this.context.beginPath();
                    this.context.arc(va.x, va.y, 3 * scale, 0, 2 * Math.PI);
                    this.context.fill();
                }

                if (vertexB && Array.isArray(vertexB) && vertexB.length >= 2) {
                    const vb = this.geoToLayerCoord(vertexB);
                    this.context.beginPath();
                    this.context.arc(vb.x, vb.y, 3 * scale, 0, 2 * Math.PI);
                    this.context.fill();
                }

                this.context.restore();
            } catch (e) {
                // Ignore geometry errors for visual indicators
            }
        }
    }

    private renderCircleShape(rel: RenderCircleShape, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewCircleShapeRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        const cCoord = rel.coord('center', this);
        const localPos1 = this.geoToLayerCoord(cCoord);

        this.context.save();
        this.context.beginPath();

        this.context.arc(localPos1.x, localPos1.y, this.geoToLayerLength(rel.radius), 0, 2 * Math.PI);
        this.context.setLineDash(this.lineDash(prop.strokeStyle, prop.lineWeight * this.getZoomLevel()));

        // setup background color by 'color' and 'opacity'
        const opacity = [null, undefined].includes(prop.opacity) ? 0 : Number(prop.opacity);
        this.context.fillStyle = prop.color;
        if (highlight) this.context.globalAlpha = (opacity + 5) / 100;
        else this.context.globalAlpha = opacity / 100;
        this.context.fill();

        this.context.closePath();
        this.context.restore();
    }

    /**
     * Renders a circular sector shape on the canvas.
     * @param rel The RenderSectorShape object to render.
     * @param highlight Whether to highlight the shape.
     */
    private renderSectorShape(rel: RenderSectorShape, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewSectorShapeRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        const radius = this.geoToLayerLength(rel.radius);

        const pCCoord = rel.coord('center', this);
        const pSCoord = rel.coord('start', this);
        const pECoord = rel.coord('end', this);

        const localPosS = this.geoToLayerCoord(pSCoord);
        const localPosE = this.geoToLayerCoord(pECoord);
        const localPosCenter: Position = this.geoToLayerCoord(pCCoord);

        const diffX = localPosS.x - localPosCenter.x;
        const diffY = localPosS.y - localPosCenter.y;

        const startAngle = Math.atan2(diffY, diffX);
        const endAngle = Math.atan2(localPosE.y - localPosCenter.y, localPosE.x - localPosCenter.x);

        this.context.save();
        this.context.beginPath();

        // setup background color by 'color' and 'opacity'
        const opacity = [null, undefined].includes(prop.opacity) ? 0 : Number(prop.opacity);
        this.context.fillStyle = prop.color;
        if (highlight) this.context.globalAlpha = (opacity + 5) / 100;
        else this.context.globalAlpha = opacity / 100;

        // draw
        this.context.moveTo(localPosCenter.x, localPosCenter.y);
        this.context.arc(localPosCenter.x, localPosCenter.y, radius, startAngle, endAngle, false);
        this.context.fill();

        this.context.closePath();
        this.context.restore();
    }

    private renderSector(rel: RenderSector, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewSectorRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        const scale = this.getScale();

        const pCCoord = rel.coord('center', this);
        const pSCoord = rel.coord('start', this);
        const pECoord = rel.coord('end', this);

        const localPosCenter: Position = this.geoToLayerCoord(pCCoord);
        const radius = this.geoToLayerLength(rel.radius);

        // Calculate angles from actual point positions
        const localPosS = this.geoToLayerCoord(pSCoord);
        const localPosE = this.geoToLayerCoord(pECoord);

        const startAngle = Math.atan2(localPosS.y - localPosCenter.y, localPosS.x - localPosCenter.x);
        let endAngle = Math.atan2(localPosE.y - localPosCenter.y, localPosE.x - localPosCenter.x);
        while (endAngle > startAngle) endAngle -= 2 * Math.PI;

        // Calculate angle for label
        let angle = Math.round(Math.abs(((Math.abs(startAngle - endAngle) - 2 * Math.PI) / Math.PI) * 180));
        angle = ((angle % 360) + 360) % 360;

        // Project both points to equal radius for consistent sector outline
        const projectedStartX = localPosCenter.x + radius * Math.cos(startAngle);
        const projectedStartY = localPosCenter.y + radius * Math.sin(startAngle);
        const projectedEndX = localPosCenter.x + radius * Math.cos(endAngle);
        const projectedEndY = localPosCenter.y + radius * Math.sin(endAngle);

        this.context.save();
        this.context.beginPath();

        this.context.setLineDash(this.lineDash(prop.strokeStyle, prop.lineWeight * scale));
        this.context.lineCap = 'round';
        this.context.lineJoin = 'round';
        this.context.strokeStyle = prop.lineColor;
        this.context.lineWidth = prop.lineWeight * scale;

        // Draw only the arc (no lines to center)
        this.context.arc(localPosCenter.x, localPosCenter.y, radius, startAngle, endAngle, false);

        this.context.stroke();

        if (highlight) {
            this.context.lineWidth = (prop.lineWeight + 4) * scale;
            this.context.globalAlpha = 0.25;
            this.context.stroke();
        }

        this.context.closePath();
        this.context.restore();

        // draw label
        if (prop?.showArcLabel) {
            // not circle
            let label: string;
            switch (prop?.arcLabelType) {
                case 'degree':
                    label = angle + '°';
                    break;
                case 'percent':
                    label = Math.round((angle * 100) / 360) + '%';
                    break;
                case 'free':
                    label = prop?.arcLabelContent ?? '';
                    break;
                default:
                    label = '';
                    break;
            }

            const labelHeight = 16 * scale;
            const labelMetrics = getLabelMeasure(this.context, label?.toString(), labelHeight);
            const labelWidth = labelMetrics.width;

            let labelAngle = ((startAngle - Math.abs(startAngle - endAngle) / 2) / Math.PI) * 180 - 180;
            labelAngle = ((labelAngle % 360) + 360) % 360;

            drawLabel(this.context, label?.toString(), localPosCenter, {
                fontSize: labelHeight,
                textColor: 'black',
                radAngleFromXAxis: (labelAngle * Math.PI) / 180 - Math.PI / 2,
                flipVertical: !(labelAngle > 0 && labelAngle < 180),
                flipHorizontal: !(labelAngle > 0 && labelAngle < 180),
                padding: {
                    left: -(labelWidth / 2),
                    top:
                        labelAngle > 0 && labelAngle < 180
                            ? -(radius + labelHeight / 2)
                            : radius + 10 + labelHeight / 2,
                },
            });
        }
    }

    private renderEllipse(rel: RenderEllipse, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewEllipseRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        // Get center coordinates from geometry function (handles all fallback logic internally)
        const centerCoords = rel.coord('center', this);
        if (!centerCoords || !Array.isArray(centerCoords) || centerCoords.length < 2) {
            return; // Skip rendering if center coordinates are not available
        }
        const centerPoint = this.geoToLayerCoord(centerCoords);

        const scale = this.getScale();

        this.context.save();
        this.context.beginPath();

        this.context.ellipse(
            centerPoint.x,
            centerPoint.y,
            this.geoToLayerLength(rel.a),
            this.geoToLayerLength(rel.b),
            rel.rotate,
            0,
            2 * Math.PI
        );

        this.context.setLineDash(this.lineDash(prop.strokeStyle, prop.lineWeight * scale));

        this.context.lineCap = 'round';
        this.context.lineJoin = 'round';
        this.context.strokeStyle = prop.lineColor;
        this.context.lineWidth = prop.lineWeight * scale;
        this.context.stroke();

        if (highlight) {
            this.context.lineWidth = (prop.lineWeight + 4) * scale;
            this.context.globalAlpha = 0.25;
            this.context.stroke();
        }

        this.context.closePath();
        this.context.restore();

        // Optional: Render mode-specific visual indicators for debugging/development
        // This can be removed in production or controlled by a debug flag
        if (rel.centerIdx !== -1 && rel.vaIdx !== -1 && rel.vbIdx !== -1) {
            // CENTER_VECTORS mode - optionally show center and vector points
            // This is mainly for visual debugging and can be customized as needed
            this.renderEllipseModeIndicators(rel, centerPoint, scale);
        }
        // FOCUS_POINTS mode - uses standard ellipse rendering (current behavior)
    }

    private renderPotentialSelection(rel: GeoRenderElement) {
        if (rel.type === 'RenderVertex') {
            const vertex = rel as RenderVertex;
            const prop = vertex.renderProp;

            if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;
            const localPos = this.geoToLayerCoord(vertex.coords);
            this.drawVertexPotential(localPos, RENDER_VERTEX_RADIUS_POTENTIAL);
        }
    }

    private renderCircle(rel: RenderCircle, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewCircleRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        const cCoords = rel.coord('center', this);
        const localPos1 = this.geoToLayerCoord(cCoords);
        const scale = this.getScale();
        const radius = this.geoToLayerLength(rel.radius);

        this.context.save();
        this.context.beginPath();

        this.context.arc(localPos1.x, localPos1.y, radius, 0, 2 * Math.PI);
        this.context.setLineDash(this.lineDash(prop.strokeStyle, prop.lineWeight * scale));
        this.context.lineCap = 'round';
        this.context.lineJoin = 'round';
        this.context.strokeStyle = prop.lineColor;
        this.context.lineWidth = prop.lineWeight * scale;
        this.context.stroke();

        if (highlight) {
            this.context.lineWidth = (prop.lineWeight + 4) * scale;
            this.context.globalAlpha = 0.25;
            this.context.stroke();
        }

        this.context.closePath();
        this.context.restore();
    }

    /**
     * Renders an angle object on the canvas with various visual properties like arcs, markers, and labels.
     *
     * @param {RenderAngle} rel - The angle object to render.
     * @param {boolean} highlight - Whether to highlight the angle (e.g., on selection).
     * @returns {void}
     */
    private renderAngle(rel: RenderAngle, highlight: boolean = false): void {
        if (!rel.renderProp) rel.renderProp = buildPreviewAngleRenderProp();
        const prop = rel.renderProp;

        // Skip rendering if the object is invalid or hidden
        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        // Get the angle's vertex point and convert to canvas coordinates
        const angleCoords = rel.coord('root', this);
        const center = this.geoToLayerCoord(angleCoords);
        const vS = rel.vector('start', this).map(x => x * (rel.startVDir ?? 1));
        const vE = rel.vector('end', this).map(x => x * (rel.endVDir ?? 1));
        if (!vS || !vE) return;

        // Calculate start angle if not already defined
        if (rel.startAngleInRad === undefined) {
            rel.startAngleInRad = Math.atan2(vS[1], vS[0]);
        }

        // Calculate end angle if not already defined
        // Ensures end angle is less than start angle for proper arc drawing
        if (rel.endAngleInRad === undefined) {
            let endAngleInRad = Math.atan2(vE[1], vE[0]);
            while (endAngleInRad > rel.startAngleInRad) endAngleInRad -= 2 * Math.PI;
            rel.endAngleInRad = endAngleInRad;
        }

        const angleInRad = (rel.degree / 180) * Math.PI;

        // Determine the radius of the angle arc
        // If angle sides are short, ensure arc doesn't extend beyond them
        const minSideLineSegmenGeotLength = this.getGeoMinLengthOfAngleSide(rel);
        const spaceFromArcToCorner = prop.spaceFromArcToCorner;
        const radius =
            minSideLineSegmenGeotLength !== undefined
                ? Math.min(spaceFromArcToCorner, this.geoToLayerLength(minSideLineSegmenGeotLength))
                : spaceFromArcToCorner;

        // Get styling properties
        const angleArcNumber = prop.angleArc ?? 1; // Number of concentric arcs to draw
        const scale = this.getScale(); // Get current zoom scale
        const lineWeight = 2 * scale;

        // Determine how to display the angle (right angle square vs arc)
        let showAngleTypes = prop.showAngleTypes;
        // If set to 'as1' (right angle symbol), but angle isn't ~90°, switch to arc representation
        if (showAngleTypes === 'as1') if (rel.degree > 90.5 || rel.degree < 89.5) showAngleTypes = 'as2';

        const enableEqualSegmentSign = prop.enableEqualSegmentSign;
        const equalSegmentSign = prop.equalSegmentSign;

        if (rel.centerAngle === undefined) {
            // Calculate angle at the midpoint of the arc
            rel.centerAngle =
                ((rel.startAngleInRad - Math.abs(rel.startAngleInRad - rel.endAngleInRad) / 2) / Math.PI) * 180 - 180;
            rel.centerAngle = ((rel.centerAngle % 360) + 360) % 360; // Normalize to 0-360°
        }

        // Drawing the angle arc or right angle symbol
        {
            // Helper function to draw concentric arcs
            const _drawArc = () => {
                for (let i = 0; i < angleArcNumber; i++)
                    drawArc(
                        this.context,
                        center,
                        radius - i * 5 * scale, // Each arc is slightly smaller
                        rel.startAngleInRad,
                        rel.endAngleInRad,
                        {
                            color: prop.color,
                            lineWeight: lineWeight,
                        }
                    );
            };

            // Helper function to calculate points for drawing perpendicular marks
            const getPerCoords = (len: number) => {
                // Calculate inner and outer points for the perpendicular marks
                const perOuterCoord = getXYFromAngleAndVertex(rel.centerAngle, center, radius + len / 2);
                const perInnerCoord = getXYFromAngleAndVertex(
                    rel.centerAngle,
                    center,
                    radius - 5 * (angleArcNumber - 1) - len / 2
                );

                return [perInnerCoord, perOuterCoord];
            };

            // Helper function to draw parallel lines (equal angle marks)
            const drawPer = (equalSegmentSignNumber: number) => {
                const [perInnerCoord, perOuterCoord] = getPerCoords(10);
                drawParallelLines(this.context, perInnerCoord, perOuterCoord, 6 * scale, equalSegmentSignNumber, {
                    color: prop.color,
                    lineWeight: lineWeight,
                    lineDash: this.lineDash('Solid', lineWeight),
                });
            };

            // Helper function to draw a right angle square symbol
            const drawRightAngle = () => {
                // Calculate the side length for the right angle square
                // Uses the law of sines to determine length needed
                const angleInRadians = (rel.degree / 180) * Math.PI;
                const rightSideLen =
                    (Math.sin(angleInRadians / 2) * radius) / Math.sin(Math.PI - Math.PI / 4 - angleInRadians / 2);

                this.context.save();

                // Translate and rotate to position the right angle symbol
                this.context.translate(center.x, center.y);
                this.context.rotate(((rel.centerAngle - 90) / 180) * Math.PI);

                const x1 = 0,
                    y1 = radius;

                // Draw first leg of right angle symbol
                let x2 = x1 + rightSideLen * Math.cos(Math.PI / 2 + Math.PI - Math.PI / 4),
                    y2 = y1 + rightSideLen * Math.sin(Math.PI / 2 + Math.PI - Math.PI / 4);
                drawLine(this.context, x1, y1, x2, y2, {
                    color: prop.color,
                    lineWeight: lineWeight,
                    lineDash: this.lineDash('Solid', lineWeight),
                    withoutSave: true, // Don't save/restore context inside drawLine
                });

                // Draw second leg of right angle symbol
                x2 = x1 + rightSideLen * Math.cos(Math.PI / 2 + Math.PI + Math.PI / 4);
                y2 = y1 + rightSideLen * Math.sin(Math.PI / 2 + Math.PI + Math.PI / 4);
                drawLine(this.context, x1, y1, x2, y2, {
                    color: prop.color,
                    lineWeight: lineWeight,
                    lineDash: this.lineDash('Solid', lineWeight),
                    withoutSave: true,
                });

                this.context.restore();
            };

            // Choose which representation to use based on angle type setting
            switch (showAngleTypes) {
                case 'as1': {
                    // Draw right angle symbol (square in corner)
                    drawRightAngle();
                    break;
                }
                case 'as2':
                default: {
                    // Draw standard arc representation
                    _drawArc();
                }
            }

            // Draw equal angle marks if enabled and not using right angle symbol
            if (showAngleTypes != 'as1' && enableEqualSegmentSign && equalSegmentSign?.length)
                drawPer(
                    (() => {
                        // Convert text setting to number of parallel marks
                        switch (equalSegmentSign) {
                            case 'two':
                                return 2;
                            case 'three':
                                return 3;
                            case 'one':
                            default:
                                return 1;
                        }
                    })()
                );
        }

        // Draw angle measurement label if enabled
        if (prop?.isShowAngleSize) {
            const label = Math.round(rel.degree * 100) / 100 + '°';

            // Calculate position for label at middle of arc
            if (rel.labelAngle === undefined) {
                rel.labelAngle =
                    ((rel.startAngleInRad - Math.abs(rel.startAngleInRad - rel.endAngleInRad) / 2) / Math.PI) * 180 -
                    180;
                rel.labelAngle = ((rel.labelAngle % 360) + 360) % 360;
            }

            const labelAngleInRad = (rel.labelAngle / 180) * Math.PI;

            // Draw the label with appropriate orientation based on angle
            drawLabel(this.context, label, center, {
                fontSize: 16 * scale,
                textColor: 'black',
                radAngleFromXAxis: labelAngleInRad,
                flipVertical: rel.labelAngle > 90 && rel.labelAngle < 270, // Flip text orientation for angles in 2nd & 3rd quadrants
                flipHorizontal: rel.labelAngle > 90 && rel.labelAngle < 270,
                padding: { top: 4, left: radius + 10 }, // Position label outside the arc
            });
        }

        // Draw angle fill (background)
        {
            this.context.save();
            this.context.beginPath();

            // Setup background color with appropriate opacity
            const opacity = [null, undefined].includes(prop.opacity) ? 0 : Number(prop.opacity);
            this.context.fillStyle = prop.color;
            if (highlight)
                this.context.globalAlpha = (opacity + 30) / 100; // Slightly more opaque when highlighted
            else this.context.globalAlpha = opacity / 100;

            // Choose fill path based on angle representation type
            switch (showAngleTypes) {
                case 'as1': {
                    // Translate and rotate to the right position
                    this.context.translate(center.x, center.y);
                    this.context.rotate(((rel.centerAngle - 90) / 180) * Math.PI);

                    // Calculate square side length
                    const rightSideLen =
                        (Math.sin(angleInRad / 2) * radius) / Math.sin(Math.PI - Math.PI / 4 - angleInRad / 2);

                    // Draw the right angle triangle fill path
                    this.context.moveTo(0, radius);
                    {
                        const x2 = rightSideLen * Math.cos(Math.PI / 2 + Math.PI - Math.PI / 4);
                        const y2 = radius + rightSideLen * Math.sin(Math.PI / 2 + Math.PI - Math.PI / 4);
                        this.context.lineTo(x2, y2);
                    }
                    this.context.lineTo(0, 0);
                    {
                        const x2 = rightSideLen * Math.cos(Math.PI / 2 + Math.PI + Math.PI / 4);
                        const y2 = radius + rightSideLen * Math.sin(Math.PI / 2 + Math.PI + Math.PI / 4);
                        this.context.lineTo(x2, y2);
                    }
                    this.context.moveTo(0, radius);
                    this.context.fill();
                    break;
                }
                default: {
                    // Fill for standard arc representation (sector)
                    this.context.moveTo(center.x, center.y);
                    this.context.arc(center.x, center.y, radius, rel.startAngleInRad, rel.endAngleInRad, false);
                    this.context.fill();
                    break;
                }
            }

            this.context.closePath();
            this.context.restore();
        }
    }

    private getViewportScale(): number {
        return (this.layer.viewport as BaseBoardViewportManager)?.zoomLevel ?? 1;
    }

    private getDocScale(): number {
        return 1 / (this.docCtrl.state.docRenderProp?.scale ?? 1);
    }

    private getZoomLevel(): number {
        if (this.docCtrl.editor.isSupportFeature(FEATURE_ZOOM)) return this.getDocScale();
        return this.getViewportScale();
    }

    private getScale(): number {
        // if (this.docCtrl.editor.isSupportFeature(FEATURE_ZOOM)) return 1; // Comment this line to avoid scale of lable on doc zoom and pan
        return this.getViewportScale();
    }

    private renderPolygon(rel: RenderPolygon, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewPolygonRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        const faces = [...rel.faces];

        this.context.save();
        this.context.beginPath();

        for (let i = 0; i < faces.length; i++) {
            const coord = rel.coord(i, this);

            if (!coord)
                throw new Error(
                    `Error while rendering polygon. Cannot find coordinate of coord ${i} in polygon ${rel.name}`
                );

            const localPos = this.geoToLayerCoord(coord);

            if (i === 0) this.context.moveTo(localPos.x, localPos.y);
            else this.context.lineTo(localPos.x, localPos.y);
        }

        // setup background color by 'color' and 'opacity'
        const opacity = [null, undefined].includes(prop.opacity) ? 0 : Number(prop.opacity);
        this.context.fillStyle = prop.color;

        if (highlight) this.context.globalAlpha = (opacity + 5) / 100;
        else this.context.globalAlpha = opacity / 100;
        this.context.fill();

        this.context.closePath();
        this.context.restore();
    }

    /**
     * MARK: render line
     * Renders a line object on the canvas.
     *
     * @param {RenderLine} line - The line object to render.
     * @param highlight
     */
    private renderLine(line: RenderLine, highlight: boolean = false) {
        if (!line.renderProp) line.renderProp = buildPreviewLineRenderProp();
        const prop = line.renderProp;

        if (line.deleted || !line.usable || !line.valid || prop.hidden) return;

        const pos = this.getRenderPointsFromLine(line);
        if (!pos) return;

        const { start: localPos1, end: localPos2 } = pos;
        const scale = this.getScale();

        this.context.save();
        this.context.beginPath();

        this.context.lineCap = 'round';
        this.context.lineJoin = 'miter';

        this.context.setLineDash(this.lineDash(prop.strokeStyle, prop.lineWeight * scale));
        this.context.strokeStyle = prop.lineColor;
        this.context.lineWidth = prop.lineWeight * scale;
        this.context.moveTo(localPos1.x, localPos1.y);
        this.context.lineTo(localPos2.x, localPos2.y);
        this.context.stroke();

        // draw arrow for vector
        if (line.type == 'RenderVector') {
            const x0 = localPos1.x;
            const y0 = localPos1.y;

            const x = localPos2.x;
            const y = localPos2.y;

            const head_len = 10 * scale;
            const head_angle = Math.PI / 6;
            const angle = Math.atan2(y - y0, x - x0);

            const d = distance2Point([x0, y0], [x, y]);
            const d2 = d - 8 * scale;
            const x1 = x0 - (d2 * (x0 - x)) / d;
            const y1 = y0 - (d2 * (y0 - y)) / d;

            this.context.setLineDash(this.lineDash('Solid', prop.lineWeight * scale));
            this.context.fillStyle = prop.lineColor;

            this.context.moveTo(x1, y1);
            this.context.lineTo(
                x1 - head_len * Math.cos(angle - head_angle),
                y1 - head_len * Math.sin(angle - head_angle)
            );
            this.context.lineTo(
                x1 - head_len * Math.cos(angle + head_angle),
                y1 - head_len * Math.sin(angle + head_angle)
            );
            this.context.lineTo(x1, y1);
            this.context.fill();
        }

        if (highlight) {
            this.context.lineWidth = (prop.lineWeight + 4) * scale;
            this.context.globalAlpha = 0.3;
            this.context.stroke();
        }

        this.context.closePath();
        this.context.restore();

        // draw label
        if (['RenderLineSegment', 'RenderLine', 'RenderRay', 'RenderVector'].includes(line.type)) {
            // equal mark for line segment
            if (['RenderLineSegment'].includes(line.type)) {
                const lineSegmentProps = prop as LineSegmentGeoRenderProp;
                if (lineSegmentProps?.enableEqualSegmentSign) {
                    const { start: perStart, end: perEnd } = this.getPerpendicularLineSegment(
                        localPos1,
                        localPos2,
                        prop.lineWeight * scale +
                            Math.min(Math.max(prop.lineWeight * scale * 1.2, 12 * scale), 24 * scale)
                    );
                    const equalSegmentSignMap = { one: 1, two: 2, three: 3 };

                    const lines = equalSegmentSignMap[lineSegmentProps?.equalSegmentSign];
                    if (lines)
                        drawParallelLines(
                            this.context,
                            perStart,
                            perEnd,
                            6 * scale,
                            equalSegmentSignMap[lineSegmentProps?.equalSegmentSign] ?? 1,
                            {
                                color: prop?.lineColor ?? '#000000',
                                lineWeight: 2 * scale,
                                lineDash: this.lineDash('Solid', 2 * scale),
                            }
                        );
                }
            }

            const sCoord = line.coord('start', this);
            const eCoord = line.coord('end', this);

            // Only proceed if we should show the label and there is a valid endPoint
            if (!prop?.showLabel || !sCoord || !eCoord) return;

            // Get start/end coordinates in canvas space
            const lsCoord = this.geoToLayerCoord(sCoord);
            const leCoord = this.geoToLayerCoord(eCoord);
            const lineSegmentProps = prop as LineSegmentGeoRenderProp;

            // Define some spacing/padding for the label
            const padding = (prop.lineWeight + 10) * scale;

            // Build label text based on labelType
            let label = lineSegmentProps?.label ?? '';
            if (lineSegmentProps?.labelType === 'size')
                label = Math.round(this.getGeoLineLength(line as RenderLineSegment)).toString();

            const labelMeasure = getLabelMeasure(this.context, label, 16 * scale);

            // Get a perpendicular line segment for positioning the label
            const { start: perpendicularStart, end: perpendicularEnd } = this.getPerpendicularLineSegment(
                lsCoord,
                leCoord,
                padding * 4
            );

            // Compute the midpoint between start and end for anchor
            const centerPoint = {
                x: (lsCoord.x + leCoord.x) / 2,
                y: (lsCoord.y + leCoord.y) / 2,
            };

            // Decide which point is "higher" to place the label above the line or swapped
            const higherPoint = !prop.swapLabelPosition
                ? perpendicularStart.y > perpendicularEnd.y
                    ? perpendicularStart
                    : perpendicularEnd
                : perpendicularStart.y > perpendicularEnd.y
                  ? perpendicularEnd
                  : perpendicularStart;

            // Create another perpendicular segment for aligning the label itself
            const { start: labelTopLeftCoord, end: labelTopRightCoord } = this.getPerpendicularLineSegment(
                higherPoint,
                centerPoint,
                labelMeasure.width
            );

            // Determine orientation for label rotation
            const dx = Math.abs(labelTopRightCoord.x - labelTopLeftCoord.x);
            const dy = Math.abs(labelTopRightCoord.y - 16 * scale - labelTopLeftCoord.y);
            const angleWithOx = getAngleWithOx(labelTopLeftCoord, labelTopRightCoord);
            const angleWithOxInRad = (angleWithOx / 180) * Math.PI;

            // Finally, draw the label text at the calculated position
            drawLabel(this.context, label, labelTopLeftCoord, {
                fontSize: 16 * scale,
                textColor: 'black',
                radAngleFromXAxis: angleWithOxInRad + (prop.swapLabelPosition && dx < dy ? Math.PI : 0),
                padding: {
                    left: !prop.swapLabelPosition ? 0 : dx < dy ? -labelMeasure.width : 0,
                    top: !prop.swapLabelPosition ? 0 : dx < dy ? 0 : 12 * scale,
                },
            });
        }
    }

    private getRenderPointsFromLine(line: RenderLine): Rectangle | undefined {
        if (line.deleted || !line.usable || !line.valid || line.renderProp?.hidden) return undefined;

        let point1: number[];
        let point2: number[];

        const sCoord: number[] | undefined = line.coord('start', this);
        const eCoord: number[] | undefined = line.coord('end', this);
        if (!sCoord) return undefined;

        if (line.type == 'RenderLineSegment') {
            if (!eCoord) return undefined;
            point1 = [...sCoord];
            point2 = [...eCoord];
        } else if (line.type == 'RenderLine') {
            // y = ax + b | b = y - ax
            const a = line.vector[1] / line.vector[0];
            const b = sCoord[1] - a * sCoord[0];
            const getY = x => a * x + b;

            if (a == 0) {
                point1 = [this.layerToGeoX(this.right), sCoord[1]];
                point2 = [this.layerToGeoX(this.left), sCoord[1]];
            } else if (a == Infinity || a == -Infinity || isNaN(a)) {
                point1 = [sCoord[0], this.layerToGeoY(this.top)];
                point2 = [sCoord[0], this.layerToGeoY(this.bottom)];
            } else {
                point1 = [this.layerToGeoX(this.right), getY(this.layerToGeoX(this.right))];
                point2 = [this.layerToGeoX(this.left), getY(this.layerToGeoX(this.left))];
            }
        } else if (line.type === 'RenderVector') {
            if (!eCoord) return undefined;
            const renderVector = line as RenderVector;
            if (renderVector.point1 === undefined || renderVector.point2 === undefined) {
                // compare theta of vector v and line segment s
                [renderVector.point1, renderVector.point2] = [sCoord, eCoord];
            }
            [point1, point2] = [renderVector.point1, renderVector.point2];
        } else if (line.type == 'RenderRay') {
            const endPointCoord =
                line.endPointIdx && line.endPointIdx != -1
                    ? (this.elementAt(line.endPointIdx) as RenderVertex).coords
                    : [sCoord[0] + line.vector[0], sCoord[1] + line.vector[1]];

            // y = ax + b | b = y - ax
            const a = line.vector[1] / line.vector[0];
            const b = sCoord[1] - a * sCoord[0];
            const getY = x => a * x + b;

            if (!isFinite(a)) {
                if (this.layerToGeoY(this.bottom) <= sCoord[1] && this.layerToGeoY(this.top) >= sCoord[1]) {
                    point1 = [sCoord[0], sCoord[1]];
                    if (endPointCoord[1] < sCoord[1]) {
                        point2 = [sCoord[0], this.layerToGeoY(this.bottom)];
                    } else {
                        point2 = [sCoord[0], this.layerToGeoY(this.top)];
                    }
                } else if (this.layerToGeoY(this.bottom) > sCoord[1] && endPointCoord[1] > sCoord[1]) {
                    point1 = [sCoord[0], this.layerToGeoY(this.bottom)];
                    point2 = [sCoord[0], this.layerToGeoY(this.top)];
                } else if (this.layerToGeoY(this.top) < sCoord[1] && endPointCoord[1] < sCoord[1]) {
                    point1 = [sCoord[0], this.layerToGeoY(this.top)];
                    point2 = [sCoord[0], this.layerToGeoY(this.bottom)];
                }
            } else {
                if (this.layerToGeoX(this.left) <= sCoord[0] && this.layerToGeoX(this.right) >= sCoord[0]) {
                    point1 = [sCoord[0], sCoord[1]];
                    if (endPointCoord[0] < sCoord[0]) {
                        point2 = [this.layerToGeoX(this.left), getY(this.layerToGeoX(this.left))];
                    } else {
                        point2 = [this.layerToGeoX(this.right), getY(this.layerToGeoX(this.right))];
                    }
                } else if (this.layerToGeoX(this.left) > sCoord[0] && endPointCoord[0] > sCoord[0]) {
                    point1 = [this.layerToGeoX(this.left), getY(this.layerToGeoX(this.left))];
                    point2 = [this.layerToGeoX(this.right), getY(this.layerToGeoX(this.right))];
                } else if (this.layerToGeoX(this.right) < sCoord[0] && endPointCoord[0] < sCoord[0]) {
                    point1 = [this.layerToGeoX(this.right), getY(this.layerToGeoX(this.right))];
                    point2 = [this.layerToGeoX(this.left), getY(this.layerToGeoX(this.left))];
                }
            }
        }

        if (!point1 || !point2) return undefined;

        return {
            start: this.geoToLayerCoord(point1),
            end: this.geoToLayerCoord(point2),
        };
    }

    private getPerpendicularLineSegment(start: Position, end: Position, length: number) {
        // 1. Find the center point:
        const centerX = (start.x + end.x) / 2;
        const centerY = (start.y + end.y) / 2;

        // Handle the case that the line is vertical or horizontal
        if (start.x === end.x)
            return {
                start: { x: centerX - length / 2, y: centerY },
                end: { x: centerX + length / 2, y: centerY },
            };
        else if (start.y === end.y)
            return {
                start: { x: centerX, y: centerY - length / 2 },
                end: { x: centerX, y: centerY + length / 2 },
            };

        // 2. Calculate the original line's slope:
        const slope = (end.y - start.y) / (end.x - start.x);

        // 3. Calculate the perpendicular slope (negative reciprocal):
        const perpSlope = -1 / slope;

        // 4. Calculate the offset for half the length in each direction:
        const halfLength = length / 2;
        const dx = halfLength / Math.sqrt(1 + perpSlope * perpSlope);
        const dy = perpSlope * dx;

        // 5. Determine the new endpoints:
        const newStartX = centerX - dx;
        const newStartY = centerY - dy;
        const newEndX = centerX + dx;
        const newEndY = centerY + dy;

        return {
            start: { x: newStartX, y: newStartY },
            end: { x: newEndX, y: newEndY },
        };
    }

    private renderVertex(rel: RenderVertex, highlight: boolean = false) {
        if (!rel.renderProp) rel.renderProp = buildPreviewVertexRenderProp();
        const prop = rel.renderProp;

        if (rel.deleted || !rel.usable || !rel.valid || prop.hidden) return;

        const localPos = this.geoToLayerCoord(rel.coords);

        this.drawVertex(localPos, RENDER_VERTEX_RADIUS, rel, highlight);

        // draw label
        this.pendingRenderlabels = this.pendingRenderlabels.filter(o => {
            return o.object.relIndex != rel.relIndex;
        });

        // Only add a label if showLabel is true
        if (prop.showPointLabel) {
            let text = '';
            const pointProp = prop as any; // Cast to access properties

            // Determine text based on pointLabelType
            if (pointProp.pointLabelType === 'free' && pointProp.pointLabelFreeContent?.length) {
                text = pointProp.pointLabelFreeContent;
            } else {
                // Default to name when pointLabelType is 'name' or not set
                text = rel.name;
            }

            if (text && text.length > 0) {
                this.pendingRenderlabels.push({
                    vertexPoint: new DOMPoint(localPos.x, localPos.y),
                    text: text,
                    object: rel,
                    highlight: highlight,
                });
            }
        }
    }

    private drawVertexPotential(c: Position, r: number) {
        const scale = this.getScale() / this.getDocScale();
        const strokeWidth = 1;
        const radius = r * Math.pow(scale, SCALE_RESISTANCE_POTENTIAL);

        this.context.save();

        this.context.beginPath();
        this.context.arc(c.x, c.y, radius, 0, 2 * Math.PI);
        this.context.fillStyle = 'gray';
        this.context.globalAlpha = 0.2;
        this.context.fill();

        this.context.strokeStyle = '#ffffff';
        this.context.lineWidth = strokeWidth * scale;
        this.context.stroke();

        this.context.restore();
    }

    // MARK: draw vertex
    private drawVertex(c: Position, r: number, vertex: RenderVertex, highlight: boolean = false) {
        const scale = this.getScale() / this.getDocScale();

        // draw vertex
        const strokeWidth = 1;

        this.context.save();
        this.context.beginPath();

        this.context.fillStyle = vertex.renderProp.pointColor;
        this.context.arc(c.x, c.y, r * Math.pow(scale, SCALE_RESISTANCE), 0, 2 * Math.PI);
        this.context.fill();

        if (highlight) {
            this.context.strokeStyle = vertex.renderProp.pointColor;
            this.context.lineWidth = (strokeWidth + 6) * scale;
            this.context.globalAlpha = 0.5;
            this.context.stroke();
        }

        this.context.strokeStyle = '#ffffff';
        this.context.lineWidth = strokeWidth * scale;
        this.context.globalAlpha = 1;
        this.context.stroke();

        this.context.closePath();
        this.context.restore();
    }

    private lineDash(strokeStyle: GeoStrokeStyle, lineWeight: number): number[] {
        let dash: number[];
        switch (strokeStyle) {
            case 'Solid':
                return [];
            case 'Dashed':
                dash = [5 * lineWeight, 5 * lineWeight];
                break;
            case 'Dotted':
                dash = [0, 3 * lineWeight];
                break;
            case 'DashedDotted':
                dash = [5 * lineWeight, 3 * lineWeight, 0, 3 * lineWeight];
                break;
            default:
                return [];
        }
        return dash;
    }

    /**
     * MARK: draw label
     * Draws a label on the canvas at the specified vertex point.
     *
     * @param {string} text - The text to be displayed on the label.
     * @param {DOMPoint} vertexPoint - The point where the label will be positioned.
     * @param {GeoRenderElement | GeoPreviewInfo} rel - The object associated with the label.
     */
    private renderLabel(text: string, vertexPoint: DOMPoint, rel: GeoRenderElement, highlight: boolean = false) {
        if (!text || text == '') return;

        const scale = this.getScale() / this.getDocScale();

        const fontSize = 18 * Math.pow(scale, SCALE_RESISTANCE);
        const labelMeasure = getLabelMeasure(this.context, text, fontSize);
        const labelWidth = labelMeasure.width;
        const labelHeight = labelMeasure.actualBoundingBoxAscent;
        const labelPoint = new DOMPoint(vertexPoint.x, vertexPoint.y);
        const vertexRadius = RENDER_VERTEX_RADIUS * Math.pow(scale, 0.5);

        // get optimal bottom left label point
        const optimalBottomLeftLabelPoint = (() => {
            if (this.labelPosCache[rel.relIndex]) return this.labelPosCache[rel.relIndex];
            else {
                const initialAngle = this.centerOfVertexs ? getAngleWithOx(this.centerOfVertexs, labelPoint) : 0;
                const _optimalBottomLeftLabelPoint = this.findOptimalLabelPoint(
                    labelPoint,
                    initialAngle,
                    labelWidth,
                    labelHeight,
                    rel,
                    vertexRadius,
                    scale
                );
                this.labelPosCache[rel.relIndex] = _optimalBottomLeftLabelPoint;
                return _optimalBottomLeftLabelPoint;
            }
        })();

        drawLabel(this.context, text, optimalBottomLeftLabelPoint, {
            fontSize: fontSize,
            textColor: 'black',
        });

        // add label to rendered list
        this.renderedlabels.push({
            bottomLeftPoint: labelPoint,
            width: labelWidth,
            height: labelHeight,
        });
    }

    /**
     * MARK: find optimal label point
     * Finds the optimal label point for a given vertex.
     *
     * @param {DOMPoint} vertexPoint - The point representing the vertex.
     * @param {number} initialAngle - The initial angle of the vertex (radian).
     * @param {number} width - The width of the label.
     * @param {number} height - The height of the label.
     * @param {GeoRenderElement | GeoPreviewInfo} rel - The rel associated with the label.
     * @param vertexRadius
     * @param scale
     * @return {DOMPoint} The optimal label point.
     */
    private findOptimalLabelPoint(
        vertexPoint: DOMPoint,
        initialAngle: number,
        width: number,
        height: number,
        rel: GeoRenderElement,
        vertexRadius: number,
        scale: number = 1
    ): DOMPoint {
        let firstLabelBottomLeftPoint = null;

        for (const angle of getLabelCenterPointsFromVertex(vertexPoint, vertexRadius, initialAngle)) {
            const labelBottomLeftCoord = calculateBottomLeftByAngleXYSize(
                angle.angle,
                angle,
                width,
                height,
                (1 + RENDER_VERTEX_PADDING_SCALE * Math.pow(scale, SCALE_RESISTANCE)) * vertexRadius // padding to avoid steping on its own point
            );

            // Calculate the bounding box of the new label
            const left = labelBottomLeftCoord.x;
            const right = labelBottomLeftCoord.x + width;
            const top = labelBottomLeftCoord.y + height;
            const bottom = labelBottomLeftCoord.y;
            const center = {
                x: (left + right) / 2,
                y: (top + bottom) / 2,
            };

            const labelBottomLeftPoint = new DOMPoint(labelBottomLeftCoord.x, labelBottomLeftCoord.y);
            if (!firstLabelBottomLeftPoint) firstLabelBottomLeftPoint = labelBottomLeftPoint;

            const nearVertexPoint = {
                x: vertexPoint.x + (vertexPoint.x > center.x ? -1 : 1),
                y: vertexPoint.y + (vertexPoint.y > center.y ? -1 : 1),
            };

            if (
                // does not intersect with the label
                !this.doesRectangleOrLineIntersectStates(left, top, right, bottom, 'rect', rel) &&
                // does not intersect with the line between the label and the vertex
                !this.doesRectangleOrLineIntersectStates(
                    center.x,
                    center.y,
                    nearVertexPoint.x,
                    nearVertexPoint.y,
                    'line',
                    rel
                )
            ) {
                return labelBottomLeftPoint;
            }
        }

        // Nếu không tìm thấy vị trí không overlap, trả về vị trí ban đầu
        return firstLabelBottomLeftPoint;
    }

    /**
     * MARK: get transformed point
     * Retrieves the transformed point at the specified index using the given matrix.
     *
     * @param {number} pointIndex - The index of the point to retrieve.
     * @return {DOMPoint} The transformed point.
     */
    private getPointFromElementIndex(pointIndex: number): DOMPoint {
        const point = this.elementAt(pointIndex) as RenderVertex;
        const pointPos = this.geoToLayerCoord(point.coords);
        return new DOMPoint(pointPos.x, pointPos.y);
    }

    /**
     * MARK: does rectangle intersect states
     * Checks if a rectangle defined by its bottom-left corner, width, and height overlaps with any existing geometry objects or labels.
     *
     * @param left
     * @param top
     * @param right
     * @param bottom
     * @param type
     * @param currentObject The geometry object to which the label belongs. This object is ignored during overlap checks.
     * @returns True if the rectangle overlaps with any other object, False otherwise.
     */
    private doesRectangleOrLineIntersectStates(
        left: number,
        top: number,
        right: number,
        bottom: number,
        type: 'rect' | 'line',
        currentObject: GeoRenderElement
    ): boolean {
        // The meaning of this function remains to be discussed.
        // Also because preview element and render element structure is different,
        // this method might not be applicable for both type of elements anymore.
        // TODO: re-check necessity of this method.
        return false;

        // // Iterate over each geometry object in the state list
        // for (const obj of this.checkOverlapStateList) {

        //     // Skip overlap check if the obj is:
        //     // - The same as the current object
        //     // - Not in a valid state
        //     // - Hidden
        //     if (obj === currentObject || (obj as any).deleted || !obj.usable || !obj.valid || obj.renderProp.hidden)
        //         continue;

        //     // Check for overlap based on the type of geometry object
        //     switch (obj.type) {
        //         case 'RenderEllipseShape': {
        //             const ellipse = obj as RenderEllipseShape;
        //             const f1Point = this.getPointFromElementIndex(ellipse.f1Idx);
        //             const f2Point = this.getPointFromElementIndex(ellipse.f2Idx);
        //             const centerPoint = new DOMPoint((f1Point.x + f2Point.x) / 2, (f1Point.y + f2Point.y) / 2);
        //             const F1F2 = [f2Point.x - f1Point.x, f2Point.y - f1Point.y, 0];

        //             // Calculate the rotation of the ellipse
        //             const value =
        //                 F1F2[0] * F1F2[1] > 0
        //                     ? Math.abs(F1F2[0])
        //                     : F1F2[0] * F1F2[1] < 0
        //                       ? -Math.abs(F1F2[0])
        //                       : F1F2[0];
        //             const rotation = Math.acos(value / distance2Point([f1Point.x, f1Point.y], [f2Point.x, f2Point.y]));

        //             switch (type) {
        //                 case 'rect':
        //                     if (
        //                         doesRectangleIntersectEllipseSegment(
        //                             { x: left, y: top },
        //                             { x: right, y: bottom },
        //                             centerPoint,
        //                             this.geoToLayerLength(ellipse.a),
        //                             this.geoToLayerLength(ellipse.b),
        //                             rotation,
        //                             0,
        //                             2 * Math.PI,
        //                             true
        //                         )
        //                     )
        //                         return true;
        //                     break;
        //                 case 'line':
        //                     if (
        //                         doesLineIntersectEllipseSegment(
        //                             { x: left, y: top },
        //                             { x: right, y: bottom },
        //                             centerPoint,
        //                             this.geoToLayerLength(ellipse.a),
        //                             this.geoToLayerLength(ellipse.b),
        //                             rotation,
        //                             0,
        //                             2 * Math.PI,
        //                             false
        //                         )
        //                     )
        //                         return true;
        //                     break;
        //             }
        //             break;
        //         }
        //         case 'RenderCircleShape': {
        //             const circle = obj as RenderCircleShape;
        //             const cPoint = this.getPointFromElementIndex(circle.centerPointIdx);

        //             switch (type) {
        //                 case 'rect':
        //                     if (
        //                         doesRectangleIntersectEllipseSegment(
        //                             { x: left, y: top },
        //                             { x: right, y: bottom },
        //                             cPoint,
        //                             this.geoToLayerLength(circle.radius),
        //                             this.geoToLayerLength(circle.radius),
        //                             0,
        //                             0,
        //                             2 * Math.PI,
        //                             true
        //                         )
        //                     )
        //                         return true;
        //                     break;
        //                 case 'line':
        //                     if (
        //                         doesLineIntersectEllipseSegment(
        //                             { x: left, y: top },
        //                             { x: right, y: bottom },
        //                             cPoint,
        //                             this.geoToLayerLength(circle.radius),
        //                             this.geoToLayerLength(circle.radius),
        //                             0,
        //                             0,
        //                             2 * Math.PI,
        //                             false
        //                         )
        //                     )
        //                         return true;
        //                     break;
        //             }
        //             break;
        //         }
        //         case 'RenderSectorShape': {
        //             const circularSector = obj as RenderSectorShape;
        //             const sPoint = this.getPointFromElementIndex(circularSector.startPointIdx);
        //             const ePoint = this.getPointFromElementIndex(circularSector.endPointIdx);
        //             const cPoint = this.getPointFromElementIndex(circularSector.centerPointIdx);

        //             const diffX = sPoint.x - cPoint.x;
        //             const diffY = sPoint.y - cPoint.y;
        //             const semicircleRadius = Math.abs(Math.sqrt(diffX * diffX + diffY * diffY));

        //             let startAngle = Math.atan2(diffY, diffX);
        //             let endAngle = Math.atan2(ePoint.y - cPoint.y, ePoint.x - cPoint.x);

        //             if (startAngle < 0) startAngle = 2 * Math.PI + startAngle;
        //             if (endAngle < 0) endAngle = 2 * Math.PI + endAngle;
        //             if (endAngle > startAngle) startAngle += 2 * Math.PI;

        //             switch (type) {
        //                 case 'rect': {
        //                     if (
        //                         doesLineIntersectRectangle(
        //                             sPoint,
        //                             ePoint,
        //                             { x: left, y: top },
        //                             { x: right, y: bottom }
        //                         ) ||
        //                         doesRectangleIntersectEllipseSegment(
        //                             { x: left, y: top },
        //                             { x: right, y: bottom },
        //                             cPoint,
        //                             semicircleRadius,
        //                             semicircleRadius,
        //                             0,
        //                             startAngle,
        //                             endAngle,
        //                             false
        //                         )
        //                     )
        //                         return true;
        //                     break;
        //                 }
        //                 case 'line': {
        //                     if (
        //                         doesLineIntersectLine({ x: left, y: top }, { x: right, y: bottom }, sPoint, ePoint) ||
        //                         doesLineIntersectEllipseSegment(
        //                             { x: left, y: top },
        //                             { x: right, y: bottom },
        //                             cPoint,
        //                             semicircleRadius,
        //                             semicircleRadius,
        //                             0,
        //                             startAngle,
        //                             endAngle,
        //                             false
        //                         )
        //                     )
        //                         return true;
        //                     break;
        //                 }
        //             }
        //             break;
        //         }
        //         case 'RenderPolygon': {
        //             const polygon = obj as RenderPolygon;
        //             for (let i = 0; i < polygon.faces.length - 1; i++) {
        //                 const domPoint1 = this.getPointFromElementIndex(polygon.faces[i]);
        //                 const domPoint2 = this.getPointFromElementIndex(polygon.faces[i + 1]);
        //                 switch (type) {
        //                     case 'rect': {
        //                         if (
        //                             doesLineIntersectRectangle(
        //                                 domPoint1,
        //                                 domPoint2,
        //                                 { x: left, y: top },
        //                                 { x: right, y: bottom }
        //                             )
        //                         )
        //                             return true;
        //                         break;
        //                     }
        //                     case 'line': {
        //                         if (
        //                             doesLineIntersectLine(
        //                                 { x: left, y: top },
        //                                 { x: right, y: bottom },
        //                                 domPoint1,
        //                                 domPoint2
        //                             )
        //                         )
        //                             return true;
        //                     }
        //                 }
        //             }
        //             break;
        //         }
        //         case 'RenderAngle':
        //             // don't need to implement
        //             break;
        //         case 'RenderVector':
        //         case 'RenderRay':
        //         case 'RenderLineSegment':
        //         case 'RenderLine': {
        //             const scale = this.getScale();
        //             const pos = this.getRenderPointsFromLine(obj as RenderLine);
        //             if (!pos) return false;
        //             const { start, end } = pos;
        //             if (!start || !end) return false;

        //             // Vertex size calculation based on zoom level
        //             let vertexSize = 5;
        //             vertexSize = scale <= 1 ? vertexSize * scale : vertexSize * Math.pow(scale, 0.6);

        //             switch (type) {
        //                 case 'rect': {
        //                     if (doesLineIntersectRectangle(start, end, { x: left, y: top }, { x: right, y: bottom }))
        //                         return true;
        //                     break;
        //                 }
        //                 case 'line': {
        //                     if (doesLineIntersectLine(start, end, { x: left, y: top }, { x: right, y: bottom }))
        //                         return true;
        //                 }
        //             }
        //             break;
        //         }
        //         case 'RenderVertex': {
        //             const localPos = this.geoToLayerCoord((obj as RenderVertex).coords);
        //             const p = new DOMPoint(localPos.x, localPos.y);

        //             // Vertex size calculation based on zoom level
        //             let vertexSize = 5;
        //             const scale = this.getScale();
        //             vertexSize = scale <= 1 ? vertexSize * scale : vertexSize * Math.pow(scale, 0.6);

        //             // Calculate bounding box of the vertex
        //             const vertexLeft = p.x - vertexSize;
        //             const vertexRight = p.x + vertexSize;
        //             const vertexTop = p.y - vertexSize;
        //             const vertexBottom = p.y + vertexSize;

        //             switch (type) {
        //                 case 'rect': {
        //                     if (!(right < vertexLeft || left > vertexRight || bottom < vertexTop || top > vertexBottom))
        //                         return true;
        //                     break;
        //                 }
        //                 case 'line': {
        //                     if (
        //                         doesLineIntersectRectangle(
        //                             { x: left, y: top },
        //                             { x: right, y: bottom },
        //                             { x: vertexLeft, y: vertexTop },
        //                             { x: vertexRight, y: vertexBottom }
        //                         )
        //                     )
        //                         return true;
        //                     break;
        //                 }
        //             }
        //             break;
        //         }
        //     }
        // }

        // // Check for overlap with existing labels
        // for (const label of this.renderedlabels) {
        //     const labelLeft = label.bottomLeftPoint.x;
        //     const labelRight = label.bottomLeftPoint.x + label.width;
        //     const labelTop = label.bottomLeftPoint.y - label.height;
        //     const labelBottom = label.bottomLeftPoint.y;
        //     if (!(right < labelLeft || left > labelRight || bottom < labelTop || top > labelBottom)) return true;
        // }

        // return false; // No overlap detected
    }
}
