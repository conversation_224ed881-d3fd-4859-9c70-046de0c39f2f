import { Point, point, Vector, vector } from '@flatten-js/core';

/**
 * Phân chia đường thẳng thành hai phía, lấy một điểm trên đường thẳng làm gốc quay (role).
 * Nếu góc của vector (vec) của đường thẳng và vector tạo bởi gốc và điểm mới (p) < 180 tức là điểm mới đang nằm trên nữa
 * đầu tiên của mặt phân chia, nếu >= 180 thì điểm mới nằm ở nửa thứ 2
 * @param vec
 * @param role
 * @param p
 */
export function nthDirectionByLine(vec: number[], role: number[], p: number[]): number {
    const _v = vector(vec[0], vec[1]);
    const _role = point(role[0], role[1]);
    const _p = point(p[0], p[1]);
    const vector_role_p = vector(_role, _p);
    if (_v.length === 0 || vector_role_p.length === 0) return 1;

    const angle = _v.angleTo(vector_role_p);
    return angle < Math.PI ? 1 : 2;
}

/**
 * When calculating nth direction the rotating vector must be from point with lower relindex to ensure consistent
 * direction no matter where the points move or names are changed.
 *
 * This method takes in two points (p1, p2) and their relIdxes and calculate the nth values for a point list
 *
 * @param ps the list of Flatten-js points
 * @param p1Idx index of the first point of the vector
 * @param p2Idx index of the second point of the vector
 * @param pLIdx list of points we will check for nth value
 * @param p1RelIdx relIdx of the first point in the renderer
 * @param p2RelIdx relIdx of the second point in the renderer
 */
export function nthDriectionRotationWithRelIdx(
    ps: Point[],
    p1Idx: number,
    p2Idx: number,
    pLIdx: number[],
    p1RelIdx: number,
    p2RelIdx: number
): number[] {
    // determine which point is the first point
    // if both point has rel idx > 0 (real element) the smaller one will be used as the first point
    // if one of the point is real and the other one is preview element, the real one is used as first point
    // if both are preview, the order is not changed.
    if ((p1RelIdx >= 0 && p2RelIdx >= 0 && p1RelIdx > p2RelIdx) || (p1RelIdx < 0 && p2RelIdx >= 0)) {
        const temp = p1Idx;
        p1Idx = p2Idx;
        p2Idx = temp;
    }

    let idx = 0;
    // calculate the rotation angles of each ps points whose index is in pLIdx
    const angleValues = pLIdx
        .map(i => {
            return [idx++, vector(ps[p1Idx], ps[p2Idx]).angleTo(vector(ps[p1Idx], ps[i]))];
        })
        // sort them by the angle value, in ascending order
        .sort((a, b) => a[1] - b[1]);

    // record the sorted index in the result
    const result = new Array(pLIdx.length).fill(0);
    for (let i = 0; i < angleValues.length; i++) {
        result[angleValues[i][0]] = i + 1;
    }

    return result;
}

/**
 * Phân chia đường thẳng thành hai phía, lấy một điểm trên đường thẳng làm gốc quay (role).
 * Nếu góc của vector (vec) của đường thẳng và vector tạo bởi gốc và điểm mới (p) < 180 tức là điểm mới đang nằm trên nữa
 * đầu tiên của mặt phân chia, nếu >= 180 thì điểm mới nằm ở nửa thứ 2.
 *
 * Hàm này có tính đến thứ tự của 2 điểm vector. Nếu điểm nào có relIdx nhỏ hơn sẽ làm gốc quay.
 *
 * @param ps the list of Flatten-js points
 * @param p1Idx index of the first point of the vector
 * @param p2Idx index of the second point of the vector
 * @param p3Idx index of the point we will check for nth value
 * @param p1RelIdx relIdx of the first point in the renderer
 * @param p2RelIdx relIdx of the second point in the renderer
 */
export function nthSideOfVector(
    ps: Point[],
    p1Idx: number,
    p2Idx: number,
    p3Idx: number,
    p1RelIdx: number,
    p2RelIdx: number
): number {
    // determine which point is the first point
    // if both point has rel idx > 0 (real element) the smaller one will be used as the first point
    // if one of the point is real and the other one is preview element, the real one is used as first point
    // if both are preview, the order is not changed.
    if ((p1RelIdx >= 0 && p2RelIdx >= 0 && p1RelIdx > p2RelIdx) || (p1RelIdx < 0 && p2RelIdx >= 0)) {
        const temp = p1Idx;
        p1Idx = p2Idx;
        p2Idx = temp;
    }

    return nthDirectionByLineV2(vector(ps[p1Idx], ps[p2Idx]), ps[p1Idx], ps[p3Idx]);
}

/**
 * Phân chia đường thẳng thành hai phía, lấy một điểm trên đường thẳng làm gốc quay (role).
 * Nếu góc của vector (vec) của đường thẳng và vector tạo bởi gốc và điểm mới (p) < 180 tức là điểm mới đang nằm trên nữa
 * đầu tiên của mặt phân chia, nếu >= 180 thì điểm mới nằm ở nửa thứ 2
 * @param vec
 * @param role
 * @param p
 */
export function nthDirectionByLineV2(vec: Vector, role: Point, p: Point): number {
    const vector_role_p = vector(role, p);
    if (vector_role_p.length === 0) return 1;

    const angle = vec.angleTo(vector_role_p);
    return angle < Math.PI ? 1 : 2;
}

/**
 * Từ một đường thẳng cho trước đi qua tâm quay (role), tìm vị trí thứ n của (p0) khi quay các điểm (points) quanh tâm
 * @param vec
 * @param role
 * @param p0
 * @param points
 */
export function nthDirectionRotation(vec: number[], role: number[], p0: number[], points: number[][]): number {
    const _v = vector(vec[0], vec[1]);
    const _p = new Set<Point>();
    const _p0 = point(p0[0], p0[1]);
    const _role = point(role[0], role[1]);

    _p.add(_p0);
    points.forEach(i => {
        if (i !== p0) _p.add(point(i[0], i[1]));
    });

    const _s = [..._p].sort((p1, p2) => {
        const angle1 = p1.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p1));
        const angle2 = p2.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p2));
        if (angle1 < angle2) return -1;
        if (angle1 > angle2) return 1;
        else return 0;
    });
    return _s.indexOf(_p0) + 1;
}

/**
 * Từ một đường thẳng cho trước đi qua tâm quay (role), tìm vị trí thứ n của (p0) khi quay các điểm (points) quanh tâm
 * @param vec
 * @param role
 * @param p0
 * @param points
 */
export function nthDirectionRotationV2(vec: Vector, role: Point, p0: Point, points: Point[]): number {
    const allPoints = new Set<Point>();

    allPoints.add(p0);
    points.forEach(i => {
        if (i !== p0) allPoints.add(point(i[0], i[1]));
    });

    const _s = [...allPoints].sort((p1, p2) => {
        const angle1 = p1.equalTo(role) ? 0 : vec.angleTo(vector(role, p1));
        const angle2 = p2.equalTo(role) ? 0 : vec.angleTo(vector(role, p2));
        if (angle1 < angle2) return -1;
        if (angle1 > angle2) return 1;
        else return 0;
    });
    return _s.indexOf(p0) + 1;
}

/**
 * Sắp xếp các điểm (points) theo góc quay điểm quanh gốc (role)
 * @param vec
 * @param role
 * @param points
 */
export function sortByRotation(vec: number[], role: number[], points: number[][]): number[][] {
    const _v = vector(vec[0], vec[1]);
    const _p = new Set<Point>();
    const _role = point(role[0], role[1]);

    points.forEach(i => {
        _p.add(point(i[0], i[1]));
    });

    const _s = [..._p].sort((p1, p2) => {
        const angle1 = p1.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p1));
        const angle2 = p2.equalTo(_role) ? 0 : _v.angleTo(vector(_role, p2));
        if (angle1 < angle2) return -1;
        if (angle1 > angle2) return 1;
        else return 0;
    });

    return _s.map(s => [s.x, s.y]);
}

/**
 * Sắp xếp các điểm (points) theo góc quay điểm quanh gốc (role)
 * @param vec
 * @param role
 * @param points
 */
export function sortByRotationV2(vec: Vector, role: Point, points: Point[]): Point[] {
    return [...points].sort((p1, p2) => {
        const angle1 = p1.equalTo(role) ? 0 : vec.angleTo(vector(role, p1));
        const angle2 = p2.equalTo(role) ? 0 : vec.angleTo(vector(role, p2));
        if (angle1 < angle2) return -1;
        if (angle1 > angle2) return 1;
        else return 0;
    });
}

/**
 * Sắp xếp các điểm dựa trên góc của chúng so với một vector tham chiếu và một vector vai trò.
 * @param vec Vector tham chiếu
 * @param role Vector vai trò
 * @param points Mảng các điểm để sắp xếp
 * @returns Mảng các điểm được sắp xếp theo góc của chúng
 */
export function pointsByRotation(vec: Point, role: Point, points: Point[]): Point[] {
    const refVector = vector(vec.x, vec.y); // Chuyển đổi điểm thành vector từ gốc tọa độ
    return points.sort((a, b) => {
        const vecToA = vector(a.x - role.x, a.y - role.y);
        const vecToB = vector(b.x - role.x, b.y - role.y);
        // Sử dụng angleTo của flatten-js thay vì hàm tùy chỉnh
        const angleA = refVector.angleTo(vecToA);
        const angleB = refVector.angleTo(vecToB);
        return angleA - angleB;
    });
}

/**
 * Danh sách các điểm (points) và điểm muốn tìm thứ tự (p0) nằm trên một đường thẳng có vector (vec),
 * Theo chiều vector (vec), trả về số thứ tự của (p0) trên đường thẳng
 * @param vec
 * @param p0
 * @param points
 */
export function nthDirectionOnLine(vec: number[], p0: number[], points: number[][]): number {
    if (!points.includes(p0)) throw 'p list is not included p0';

    const _vec = vector(vec[0], vec[1]);
    const vecUnit = _vec.normalize();
    const vec0 = vector(p0[0], p0[1]);
    const allVec = new Set<Vector>();
    allVec.add(vec0);

    points.forEach(i => {
        if (i !== p0) allVec.add(vector(i[0], i[1]));
    });

    const _s = [...allVec].sort((vec1, vec2) => {
        const k1 = vec1.dot(vecUnit);
        const k2 = vec2.dot(vecUnit);
        return k1 < k2 ? -1 : k1 > k2 ? 1 : 0;
    });

    const idx = _s.indexOf(vec0);

    return idx + 1;
}

/**
 * Danh sách các điểm (points) và điểm muốn tìm thứ tự (p0) nằm trên một đường thẳng có vector (vec),
 * Theo chiều vector (vec), trả về số thứ tự của (p0) trên đường thẳng
 * @param vec
 * @param p0
 * @param points
 */
export function nthDirectionOnLineV2(vec: Vector, p0: Point, points: Point[]): number {
    if (!points.includes(p0)) throw 'p list is not included p0';

    const vecUnit = vec.normalize();
    const vec0 = vector(p0[0], p0[1]);
    const allVec = new Set<Vector>();
    allVec.add(vec0);

    points.forEach(i => {
        if (i !== p0) allVec.add(vector(i[0], i[1]));
    });

    const _s = [...allVec].sort((vec1, vec2) => {
        const k1 = vec1.dot(vecUnit);
        const k2 = vec2.dot(vecUnit);
        return k1 < k2 ? -1 : k1 > k2 ? 1 : 0;
    });

    const idx = _s.indexOf(vec0);

    return idx + 1;
}

/**
 * Sắp xếp các điểm (points) theo chiều vector
 * @param vec
 * @param points
 */
export function sortOnLineV2(vec: Vector, points: Point[]): Point[] {
    const allVec = new Set<Vector>();
    const vecUnit = vec.normalize();
    points.forEach(i => {
        allVec.add(vector(i.x, i.y));
    });
    const _s = [...allVec].sort((vec1, vec2) => {
        const k1 = vec1.dot(vecUnit);
        const k2 = vec2.dot(vecUnit);
        return k1 < k2 ? -1 : k1 > k2 ? 1 : 0;
    });
    return _s.map(s => point(s.x, s.y));
}

/**
 * Sắp xếp các điểm (points) theo chiều vector
 * @param vec
 * @param points
 */
export function sortOnLine(vec: number[], points: number[][]): number[][] {
    const _vec = vector(vec[0], vec[1]);
    const vecUnit = _vec.normalize();
    const allVec = new Set<Vector>();
    points.forEach(i => {
        allVec.add(vector(i[0], i[1]));
    });
    const _s = [...allVec].sort((vec1, vec2) => {
        const k1 = vec1.dot(vecUnit);
        const k2 = vec2.dot(vecUnit);
        return k1 < k2 ? -1 : k1 > k2 ? 1 : 0;
    });
    return _s.map(s => [s.x, s.y]);
}

/**
 * Chia đường thẳng thành hai phía âm/dương tại một điểm (root), trả về -1 hoặc 1 nếu điểm p nằm trên nửa tương ứng.
 * @param vec
 * @param root
 * @param p
 */
export function directionOfPointOnParallelVector(vec: number[], root: number[], p: number[]): 1 | -1 {
    const _vec = vector(vec[0], vec[1]);
    const vecUnit = _vec.normalize();
    const vecRoot = vector(root[0], root[1]);
    const vecP = vector(p[0], p[1]);

    const k1 = vecRoot.dot(vecUnit);
    const k2 = vecP.dot(vecUnit);

    return k1 < k2 ? -1 : 1;
}

/**
 * Chia đường thẳng thành hai phía âm/dương tại một điểm (root), trả về -1 hoặc 1 nếu điểm p nằm trên nửa tương ứng.
 * @param vec
 * @param root
 * @param p
 */
export function directionOfPointOnParallelVectorV2(vec: Vector, root: Point, p: Point): 1 | -1 {
    const vecUnit = vec.normalize();
    const vecRoot = vector(root.x, root.y);
    const vecP = vector(p.x, p.y);

    const k1 = vecRoot.dot(vecUnit);
    const k2 = vecP.dot(vecUnit);

    return k1 < k2 ? -1 : 1;
}

export function isLargerThan(sourceRelIdx: number, targetRelIdx: number): boolean {
    return (
        (sourceRelIdx < 0 && targetRelIdx < 0 && sourceRelIdx < targetRelIdx) ||
        (sourceRelIdx >= 0 && targetRelIdx >= 0 && sourceRelIdx > targetRelIdx) ||
        (sourceRelIdx < 0 && targetRelIdx >= 0)
    );
}

export function getLargerIdx(sourceRelIdx: number, targetRelIdx: number): number {
    return isLargerThan(sourceRelIdx, targetRelIdx) ? sourceRelIdx : targetRelIdx;
}

export function getSmallerIdx(sourceRelIdx: number, targetRelIdx: number): number {
    return isLargerThan(sourceRelIdx, targetRelIdx) ? targetRelIdx : sourceRelIdx;
}
