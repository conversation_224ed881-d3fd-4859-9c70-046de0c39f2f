# Transform Functions và Check Functions trong Selector

## Tổng quan

Transform functions và check functions là các hàm quan trọng trong Selector System, cho phép:

- **Transform functions (`tfunc`)**: Biến đổi vị trí của preview elements theo ràng buộc hình học
- **Check functions (`cfunc`)**: <PERSON><PERSON><PERSON> tra tính hợp lệ của selection theo điều kiện cụ thể

## Transform Functions

### perpLinesTransform - Ràng buộc góc vuông

Chiếu điểm lên đường thẳng đi qua các điểm đã chọn và vuông góc với đoạn thẳng nối chúng.

```typescript
import { perpLinesTransform } from '../selectors';

const rightAngleSelector = vertex({
    tfunc: (previewEl, doc) => {
        const selectedPoints = getSelectedPoints(); // [point1, point2]
        return perpLinesTransform(selectedPoints, previewEl);
    }
});
```

**Sử dụng trong**: T<PERSON><PERSON> tam gi<PERSON><PERSON> vuông, hình chữ nhật

### halfCircleTransform - Ràng buộc trên nửa đường tròn

Chiếu điểm lên nửa đường tròn có đường kính là đoạn thẳng nối 2 điểm đã chọn.

```typescript
import { halfCircleTransform } from '../selectors';

const rightTriangleSelector = vertex({
    tfunc: (previewEl, doc) => {
        const [point1, point2] = getSelectedPoints();
        return halfCircleTransform([point1, point2], previewEl);
    }
});
```

**Sử dụng trong**: Tạo tam giác vuông (góc nội tiếp nửa đường tròn)

### equilateralTransform - Ràng buộc tam giác đều

Tính toán vị trí điểm thứ 3 để tạo tam giác đều.

```typescript
import { equilateralTransform } from '../selectors';

const equilateralTriangleSelector = vertex({
    tfunc: (previewEl, doc) => {
        const [point1, point2] = getSelectedPoints();
        return equilateralTransform([point1, point2], previewEl);
    }
});
```

**Sử dụng trong**: Tạo tam giác đều

### perpBisectorTransform - Ràng buộc trên đường trung trực

Chiếu điểm lên đường trung trực của đoạn thẳng nối 2 điểm đã chọn.

```typescript
import { perpBisectorTransform } from '../selectors';

const isoscelesSelector = vertex({
    tfunc: (previewEl, doc) => {
        const [point1, point2] = getSelectedPoints();
        return perpBisectorTransform([point1, point2], previewEl);
    }
});
```

**Sử dụng trong**: Tạo tam giác cân

### circleTransform - Ràng buộc trên đường tròn

Chiếu điểm lên đường tròn có tâm là một trong 2 điểm đã chọn và bán kính bằng khoảng cách giữa chúng.

```typescript
import { circleTransform } from '../selectors';

const circleConstrainedSelector = vertex({
    tfunc: (previewEl, doc) => {
        const [center, radiusPoint] = getSelectedPoints();
        return circleTransform([center, radiusPoint], previewEl, 0); // 0: tâm là điểm đầu
    }
});
```

**Tham số thứ 4**: 
- `0`: Tâm là điểm đầu tiên
- `1`: Tâm là điểm thứ hai
- `undefined`: Chọn tâm gần nhất

## Check Functions

### perpLinesCheck - Kiểm tra góc vuông

Kiểm tra xem điểm có tạo góc vuông với 2 điểm đã chọn không.

```typescript
import { perpLinesCheck } from '../selectors';

const rightAngleSelector = vertex({
    cfunc: (el, doc) => {
        const selectedPoints = getSelectedPoints();
        return perpLinesCheck(selectedPoints, el);
    }
});
```

### halfCircleCheck - Kiểm tra trên nửa đường tròn

Kiểm tra xem điểm có nằm trên nửa đường tròn không.

```typescript
import { halfCircleCheck } from '../selectors';

const rightTriangleSelector = vertex({
    cfunc: (el, doc) => {
        const [point1, point2] = getSelectedPoints();
        return halfCircleCheck([point1, point2], el);
    }
});
```

### equilateralCheck - Kiểm tra tam giác đều

Kiểm tra xem 3 điểm có tạo tam giác đều không.

```typescript
import { equilateralCheck } from '../selectors';

const equilateralSelector = vertex({
    cfunc: (el, doc) => {
        const [point1, point2] = getSelectedPoints();
        return equilateralCheck([point1, point2], el);
    }
});
```

### perpBisectorCheck - Kiểm tra trên đường trung trực

Kiểm tra xem điểm có cách đều 2 điểm đã chọn không.

```typescript
import { perpBisectorCheck } from '../selectors';

const isoscelesSelector = vertex({
    cfunc: (el, doc) => {
        const [point1, point2] = getSelectedPoints();
        return perpBisectorCheck([point1, point2], el);
    }
});
```

### circleCheck - Kiểm tra trên đường tròn

Kiểm tra xem điểm có nằm trên đường tròn không.

```typescript
import { circleCheck } from '../selectors';

const circleSelector = vertex({
    cfunc: (el, doc) => {
        const [center, radiusPoint] = getSelectedPoints();
        return circleCheck([center, radiusPoint], el);
    }
});
```

## Utility Functions

### vert() - Trích xuất RenderVertex

Trích xuất RenderVertex từ SelectedVertex (có thể là array).

```typescript
import { vert } from '../selectors';

const vertex1 = vert(selectedVertex); // RenderVertex
const vertex2 = vert([stroke, vertex]); // RenderVertex (lấy vertex)
```

### strk() - Trích xuất StrokeType

Trích xuất StrokeType từ SelectedStroke.

```typescript
import { strk } from '../selectors';

const stroke1 = strk(selectedStroke); // StrokeType
const stroke2 = strk([stroke]); // StrokeType
```

### pointsFromSelected() - Chuyển đổi sang Point[]

Chuyển đổi array SelectedVertex thành array Point của flatten-js.

```typescript
import { pointsFromSelected } from '../selectors';

const points = pointsFromSelected([vertex1, vertex2, vertex3]);
// Trả về Point[] để sử dụng với flatten-js
```

### closerPoint() - Tìm điểm gần nhất

Xác định điểm nào trong 2 điểm đã chọn gần với điểm hiện tại nhất.

```typescript
import { closerPoint } from '../selectors';

const closerIndex = closerPoint([point1, point2], currentPoint);
// Trả về 0 hoặc 1
```

## Ví dụ kết hợp Transform và Check

### Tạo tam giác vuông với 2 chế độ

```typescript
export class CreateRightTriangleTool extends GeometryTool {
    drawMode: number = 0; // 0: perpLines, 1: halfCircle
    
    createSelLogic() {
        const selector = triangleWithProj(
            this.pQ,
            this.cursor,
            // Transform function dựa trên chế độ
            (f2p, el) => {
                return this.drawMode === 0 
                    ? perpLinesTransform(f2p, el)
                    : halfCircleTransform(f2p, el);
            },
            // Check function tương ứng
            (f2p, el) => {
                return this.drawMode === 0
                    ? perpLinesCheck(f2p, el)
                    : halfCircleCheck(f2p, el);
            }
        );
    }
}
```

### Selector với nhiều ràng buộc

```typescript
const constrainedSelector = vertex({
    tfunc: (previewEl, doc) => {
        const selectedPoints = getSelectedPoints();
        
        // Áp dụng transform theo điều kiện
        if (this.constraintType === 'perpendicular') {
            return perpLinesTransform(selectedPoints, previewEl);
        } else if (this.constraintType === 'equilateral') {
            return equilateralTransform(selectedPoints, previewEl);
        }
        
        return previewEl; // Không transform
    },
    
    cfunc: (el, doc) => {
        const selectedPoints = getSelectedPoints();
        
        // Kiểm tra tương ứng
        if (this.constraintType === 'perpendicular') {
            return perpLinesCheck(selectedPoints, el);
        } else if (this.constraintType === 'equilateral') {
            return equilateralCheck(selectedPoints, el);
        }
        
        return true; // Luôn hợp lệ
    }
});
```

## Best Practices

### 1. Luôn modify in-place trong Transform Functions

```typescript
// ✅ Đúng
const tfunc = (previewEl, doc) => {
    previewEl.coords[0] = newX;
    previewEl.coords[1] = newY;
    return previewEl;
};

// ❌ Sai - tạo object mới
const tfunc = (previewEl, doc) => {
    return { ...previewEl, coords: [newX, newY] };
};
```

### 2. Sử dụng GeoEpsilon cho so sánh số thực

```typescript
import { GeoEpsilon } from '../model';

const cfunc = (el, doc) => {
    const distance = calculateDistance(el, target);
    return Math.abs(distance) < GeoEpsilon; // Thay vì === 0
};
```

### 3. Kết hợp Transform và Check logic

```typescript
// Transform và check nên có logic tương ứng
const selector = vertex({
    tfunc: perpLinesTransform,
    cfunc: perpLinesCheck  // Cùng loại ràng buộc
});
```

### 4. Xử lý edge cases

```typescript
const tfunc = (previewEl, doc) => {
    const selectedPoints = getSelectedPoints();
    
    // Kiểm tra đủ điểm đã chọn
    if (selectedPoints.length < 2) {
        return previewEl; // Không transform
    }
    
    // Kiểm tra điểm không trùng nhau
    if (pointsAreEqual(selectedPoints[0], selectedPoints[1])) {
        return previewEl;
    }
    
    return applyTransform(selectedPoints, previewEl);
};
```

### 5. Performance optimization

```typescript
// Cache tính toán phức tạp
let cachedTransform: any = null;

const tfunc = (previewEl, doc) => {
    if (!cachedTransform) {
        cachedTransform = calculateComplexTransform(selectedPoints);
    }
    
    return applyTransform(cachedTransform, previewEl);
};
```

Transform functions và check functions là công cụ mạnh mẽ để tạo ra các ràng buộc hình học phức tạp trong quá trình selection, giúp người dùng tạo ra các hình học chính xác và có ý nghĩa.
