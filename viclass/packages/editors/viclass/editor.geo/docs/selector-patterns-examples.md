# Selector Patterns và Use Cases

## Tổng quan

Document này mô tả các patterns phổ biến và use cases thực tế khi sử dụng Selector APIs trong VIClass Geometry Editor.

## Patterns cơ bản

### Pattern 1: Simple Point Selection

**Use case**: Tạo điểm đơn giản, chọn điểm có sẵn hoặc tạo mới.

```typescript
export class CreatePointTool extends GeometryTool {
    selLogic: VertexSelector;

    constructor() {
        this.selLogic = vertex({
            preview: true, // Cho phép tạo điểm mới
            renderEl: true, // Cho phép chọn điểm có sẵn
            onComplete: this.createPoint.bind(this),
        });
    }
}
```

### Pattern 2: Two Points Line Creation

**Use case**: Tạo đường thẳng từ 2 điểm.

```typescript
export class CreateLineTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;

    constructor() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.createLine.bind(this),
        });
    }

    handlePointerEvent(event: GeoPointerEvent) {
        const selected = this.selLogic.trySelect(event, docCtrl);

        // Hiển thị preview line khi có 2 điểm
        if (selected && selected.length === 2) {
            this.showPreviewLine(vert(selected[0]), vert(selected[1]));
        }
    }
}
```

### Pattern 3: Point on Object

**Use case**: Tạo điểm trên đối tượng khác (đường, đường tròn).

```typescript
export class PointOnObjectTool extends GeometryTool {
    selLogic: OrSelector<SelectedVertex>;

    constructor() {
        this.selLogic = or(
            [
                vertex({ preview: true }), // Điểm tự do
                vertexOnStroke({
                    // Điểm trên stroke
                    selectableStrokeTypes: ['RenderLine', 'RenderCircle'],
                    syncPreview: true,
                }),
            ],
            {
                flatten: true,
                onComplete: this.createPoint.bind(this),
            }
        );
    }
}
```

## Patterns phức tạp

### Pattern 4: Sequential Selection

**Use case**: Chọn các phần tử theo thứ tự cụ thể.

```typescript
export class PerpendicularLineTool extends GeometryTool {
    selLogic: ThenSelector;

    createSelLogic() {
        const lineSelector = stroke({
            selectableStrokeTypes: ['RenderLine'],
            name: 'baseLine',
        });

        const pointSelector = vertexS(this.pQ, this.cursor);
        pointSelector.get('vertex').setOption('name', 'throughPoint');

        this.selLogic = then([lineSelector, pointSelector], {
            onComplete: this.createPerpendicularLine.bind(this),
        });
    }

    createPerpendicularLine(selector: ThenSelector, doc: GeoDocCtrl) {
        const [baseLine, throughPoint] = selector.selected;
        // Tạo đường vuông góc
        this.constructPerpendicularLine(strk(baseLine), vert(throughPoint));
    }
}
```

### Pattern 5: Constrained Selection

**Use case**: Chọn với ràng buộc hình học.

```typescript
export class RightTriangleTool extends GeometryTool {
    selLogic: ThenSelector;

    createSelLogic() {
        // Chọn 2 điểm đầu tự do
        const first2Points = nPoints(this.pQ, this.cursor, { count: 2 });

        // Điểm thứ 3 bị ràng buộc tạo góc vuông
        const thirdPoint = vertex({
            preview: true,
            tfunc: (previewEl, doc) => {
                const selectedPoints = this.selLogic.selected[0] as SelectedVertex[];
                return perpLinesTransform(selectedPoints, previewEl);
            },
            cfunc: (el, doc) => {
                const selectedPoints = this.selLogic.selected[0] as SelectedVertex[];
                return perpLinesCheck(selectedPoints, el);
            },
        });

        this.selLogic = then([first2Points, thirdPoint], {
            onComplete: this.createRightTriangle.bind(this),
        });
    }
}
```

### Pattern 6: Multi-mode Selection

**Use case**: Tool có nhiều chế độ selection khác nhau.

```typescript
export class AngleTool extends GeometryTool {
    selLogic: OrSelector;

    createSelLogic() {
        // Chế độ 1: Chọn góc có sẵn
        const existingAngleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            name: 'existingAngle',
        });

        // Chế độ 2: Chọn 2 đường thẳng + điểm chỉ hướng
        const lineBasedSelector = then([
            repeat(
                stroke({
                    selectableStrokeTypes: ['RenderLine'],
                    name: 'line',
                }),
                { count: 2 }
            ),
            vertex({ name: 'directionPoint' }),
        ]);

        // Chế độ 3: Chọn 3 điểm
        const pointBasedSelector = nPoints(this.pQ, this.cursor, {
            count: 3,
            name: 'threePoints',
        });

        this.selLogic = or([existingAngleSelector, lineBasedSelector, pointBasedSelector], {
            onComplete: this.createAngle.bind(this),
        });
    }

    createAngle(selector: OrSelector, doc: GeoDocCtrl) {
        const selected = selector.selected;

        // Xử lý theo loại selection
        if (this.isExistingAngle(selected)) {
            this.processExistingAngle(selected);
        } else if (this.isLineBased(selected)) {
            this.processLineBased(selected);
        } else {
            this.processPointBased(selected);
        }
    }
}
```

## Use Cases thực tế

### Use Case 1: Polygon Tool

**Mô tả**: Tạo đa giác với số điểm không giới hạn, kết thúc bằng double-click hoặc click vào điểm đầu.

```typescript
export class PolygonTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    selectedPoints: RenderVertex[] = [];

    constructor() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            onPartialSelection: this.onPointSelected.bind(this),
            // Không set count để cho phép chọn không giới hạn
        });
    }

    onPointSelected(
        newSel: SelectedVertex,
        curSel: SelectedVertex[],
        selector: RepeatSelector<SelectedVertex>,
        doc: GeoDocCtrl
    ): boolean {
        const newPoint = vert(newSel);
        this.selectedPoints.push(newPoint);

        // Kiểm tra kết thúc polygon
        if (this.selectedPoints.length >= 3) {
            // Nếu click vào điểm đầu tiên -> kết thúc
            if (this.isClickOnFirstPoint(newPoint)) {
                this.completePolygon();
                return false; // Dừng repeat
            }
        }

        // Hiển thị preview edges
        this.updatePreviewPolygon();
        return true; // Tiếp tục chọn
    }

    handleDoubleClick(event: GeoPointerEvent) {
        if (this.selectedPoints.length >= 3) {
            this.completePolygon();
        }
    }
}
```

### Use Case 2: Circle Tool với nhiều chế độ

**Mô tả**: Tạo đường tròn bằng center-radius, 2 điểm, hoặc 3 điểm.

```typescript
export class CircleTool extends GeometryTool {
    mode: 'center-radius' | 'two-points' | 'three-points' = 'center-radius';
    selLogic: OrSelector;

    createSelLogic() {
        // Chế độ center-radius: chọn tâm rồi chọn điểm trên đường tròn
        const centerRadiusSelector = then([
            vertexS(this.pQ, this.cursor), // Tâm
            vertexS(this.pQ, this.cursor), // Điểm trên đường tròn
        ]);

        // Chế độ 2 điểm: đường tròn có đường kính là đoạn thẳng nối 2 điểm
        const twoPointsSelector = nPoints(this.pQ, this.cursor, { count: 2 });

        // Chế độ 3 điểm: đường tròn đi qua 3 điểm
        const threePointsSelector = nPoints(this.pQ, this.cursor, { count: 3 });

        this.selLogic = or([centerRadiusSelector, twoPointsSelector, threePointsSelector], {
            onComplete: this.createCircle.bind(this),
        });
    }

    createCircle(selector: OrSelector, doc: GeoDocCtrl) {
        const selected = selector.selected;

        if (selected.length === 2 && this.mode === 'center-radius') {
            this.createCenterRadiusCircle(selected);
        } else if (selected.length === 2 && this.mode === 'two-points') {
            this.createDiameterCircle(selected);
        } else if (selected.length === 3) {
            this.createThreePointCircle(selected);
        }
    }
}
```

### Use Case 3: Intersection Tool

**Mô tả**: Tìm giao điểm của 2 đối tượng.

```typescript
export class IntersectionTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedStroke>;

    constructor() {
        this.selLogic = repeat(strokeS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.findIntersections.bind(this),
        });
    }

    findIntersections(selector: RepeatSelector<SelectedStroke>, doc: GeoDocCtrl) {
        const [stroke1, stroke2] = selector.selected.map(s => strk(s));

        // Tính toán giao điểm
        const intersections = this.calculateIntersections(stroke1, stroke2);

        if (intersections.length === 0) {
            this.showMessage('Không có giao điểm');
            return;
        }

        // Tạo điểm giao
        intersections.forEach(point => {
            this.createIntersectionPoint(point, stroke1, stroke2);
        });
    }
}
```

### Use Case 4: Symmetry Tool

**Mô tả**: Tạo đối xứng qua điểm hoặc đường thẳng.

```typescript
export class SymmetryTool extends GeometryTool {
    mode: 'point' | 'line' = 'point';
    selLogic: ThenSelector;

    createSelLogic() {
        // Chọn đối tượng cần đối xứng
        const objectSelector = or([vertexS(this.pQ, this.cursor), strokeS(this.pQ, this.cursor)], { flatten: true });

        // Chọn trục đối xứng
        const axisSelector =
            this.mode === 'point'
                ? vertexS(this.pQ, this.cursor) // Điểm đối xứng
                : strokeS(this.pQ, this.cursor); // Đường đối xứng

        this.selLogic = then([objectSelector, axisSelector], {
            onComplete: this.createSymmetry.bind(this),
        });
    }

    createSymmetry(selector: ThenSelector, doc: GeoDocCtrl) {
        const [object, axis] = selector.selected;

        if (this.mode === 'point') {
            this.createPointSymmetry(object, vert(axis));
        } else {
            this.createLineSymmetry(object, strk(axis));
        }
    }
}
```

## Advanced Patterns

### Pattern 7: Ellipse Tool với 2 chế độ

**Use case**: Tool tạo ellipse với 2 chế độ khác nhau - focus points và center vectors.

```typescript
export class CreateEllipseTool extends GeometryTool {
    mode: number = 0; // 0: focus points, 1: center vectors
    selLogic: ThenSelector;

    createSelLogic() {
        // Cả 2 chế độ đều chọn 2 điểm đầu + 1 điểm cuối
        const first2Points = nPoints(this.pQ, this.cursor, { count: 2 });

        const lastVertex = vertex({
            name: this.mode === 0 ? 'pointOnEllipse' : 'vectorBPoint',
            previewQueue: this.pQ,
            cursor: this.cursor,
        });

        this.selLogic = then([first2Points, lastVertex], {
            onComplete: this.performConstruction.bind(this),
        });
    }

    handlePointerEvent(event: GeoPointerEvent) {
        const selected = this.selLogic.trySelect(event, docCtrl);

        // Hiển thị preview khác nhau theo chế độ
        if (this.mode === 0) {
            this.handleFocusPointsPreview(selected, docCtrl);
        } else {
            this.handleCenterVectorsPreview(selected, docCtrl);
        }
    }
}
```

### Pattern 8: Regular Polygon với Mouse Wheel

**Use case**: Tool tạo đa giác đều với khả năng thay đổi số cạnh bằng mouse wheel.

```typescript
export class CreateRegularPolygonTool extends GeometryTool {
    selLogic: ThenSelector;

    constructor() {
        super();
        // Đăng ký mouse wheel event
        this.registerMouseHandling({
            event: 'mousewheel',
            keys: ['ctrl'],
        });
        this.createSelLogic();
    }

    createSelLogic() {
        const first2Points = nPoints(this.pQ, this.cursor, {
            count: 2,
            onComplete: this.first2Points.bind(this),
        });

        // Điểm thứ 3 để xác định hướng và kích thước
        const thirdPoint = vertex({
            previewQueue: this.pQ,
            cursor: this.cursor,
            tfunc: this.transformThirdPoint.bind(this),
        });

        this.selLogic = then([first2Points, thirdPoint], {
            onComplete: this.performConstruction.bind(this),
        });
    }

    handleMouseWheel(event: WheelEvent) {
        if (event.ctrlKey) {
            // Thay đổi số cạnh đa giác
            this.toolState.edgeCount += event.deltaY > 0 ? 1 : -1;
            this.toolState.edgeCount = Math.max(3, Math.min(10, this.toolState.edgeCount));
            this.updatePreview();
        }
    }
}
```

### Pattern 10: Conditional Selection Logic

**Use case**: Logic selection thay đổi dựa trên context.

```typescript
export class SmartLineTool extends GeometryTool {
    createSelLogic() {
        this.selLogic = or(
            [
                // Nếu chọn 2 điểm -> tạo đường thẳng
                repeat(vertexS(this.pQ, this.cursor), {
                    count: 2,
                    name: 'twoPoints',
                }),

                // Nếu chọn 1 điểm + 1 đường -> tạo đường song song/vuông góc
                then([vertexS(this.pQ, this.cursor), strokeS(this.pQ, this.cursor)], { name: 'pointAndLine' }),

                // Nếu chọn 2 đường -> tạo đường qua giao điểm
                repeat(strokeS(this.pQ, this.cursor), {
                    count: 2,
                    name: 'twoLines',
                }),
            ],
            {
                onComplete: this.createSmartLine.bind(this),
            }
        );
    }

    createSmartLine(selector: OrSelector, doc: GeoDocCtrl) {
        const selectorName = this.getActiveSelectorName(selector);

        switch (selectorName) {
            case 'twoPoints':
                this.createLineFromPoints(selector.selected);
                break;
            case 'pointAndLine':
                this.createParallelOrPerpendicular(selector.selected);
                break;
            case 'twoLines':
                this.createLineThroughIntersection(selector.selected);
                break;
        }
    }
}
```

### Pattern 8: Dynamic Exclusion

**Use case**: Loại trừ elements dựa trên selection hiện tại.

```typescript
export class TriangleTool extends GeometryTool {
    selectedVertices: RenderVertex[] = [];

    createSelLogic() {
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 3,
            onPartialSelection: this.onVertexSelected.bind(this),
        });

        // Cập nhật exclusion filter
        this.updateExclusionFilter();
    }

    onVertexSelected(
        newSel: SelectedVertex,
        curSel: SelectedVertex[],
        selector: RepeatSelector<SelectedVertex>,
        doc: GeoDocCtrl
    ): boolean {
        this.selectedVertices.push(vert(newSel));
        this.updateExclusionFilter();
        return true;
    }

    updateExclusionFilter() {
        const vertexSelector = this.selLogic.get('vertex') as VertexSelector;
        vertexSelector.setOption('refinedFilter', (el: RenderVertex) => {
            // Loại trừ các điểm đã chọn
            return !this.selectedVertices.some(v => v.relIndex === el.relIndex);
        });
    }
}
```

### Pattern 11: Semicircle Tool với Center Point

**Use case**: Tool tạo bán nguyệt với tự động tính center point.

```typescript
export class CreateSemicircleTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    previewSector: RenderSector;

    constructor() {
        super();
        this.selLogic = nPoints(this.pQ, this.cursor, {
            count: 2,
            onComplete: this.performConstruction.bind(this),
        });
    }

    doTrySelection(event: UIPointerEventData, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (selected && selected.length >= 2) {
            const v1 = vert(selected[0]);
            const v2 = vert(selected[1]);

            // Tính center point tự động
            const centerCoords = [(v1.coords[0] + v2.coords[0]) / 2, (v1.coords[1] + v2.coords[1]) / 2];
            const centerPoint = pVertex(-22, centerCoords);

            // Tạo preview elements
            this.pQ.add(pLine(ctrl, -24, RenderLineSegment, v1, centerPoint));
            this.pQ.add(pLine(ctrl, -23, RenderLineSegment, centerPoint, v2));

            // Lưu sector để dùng trong construction
            this.previewSector = pSector(ctrl, -20, v1, centerPoint, v2);
            this.pQ.add(pSectorShape(ctrl, -21, v1, centerPoint, v2, this.previewSector));
        }

        this.pQ.flush(ctrl);
    }
}
```

### Pattern 13: Point on Object Tool

**Use case**: Tool tạo điểm trên object với VertexOnStrokeSelector đơn giản.

```typescript
export class PointOnObjectTool extends GeometryTool {
    selLogic: VertexOnStrokeSelector;

    constructor() {
        super();
        this.selLogic = vertexOnStroke({
            previewQueue: this.pQ,
            syncPreview: true,
            cursor: this.cursor,
            onComplete: this.performConstruction.bind(this),
        });
    }

    // Tool này chỉ cần một selector đơn giản
    // Không cần logic phức tạp vì VertexOnStrokeSelector đã xử lý tất cả
    handlePointerEvent(event: GeoPointerEvent) {
        const ctrl = getFocusDocCtrl(this.editor, event.viewport.id);
        this.selLogic.trySelect(event, ctrl);
        this.pQ.flush(ctrl);
    }

    async performConstruction(selector: VertexOnStrokeSelector, ctrl: GeoDocCtrl) {
        // selector.selected có dạng [StrokeType, RenderVertex]
        const [stroke, pointOnStroke] = selector.selected;

        // Tạo construction request cho point on object
        const construction = this.buildPointOnElConstruction(pointOnStroke, stroke);
        await this.executeConstruction(construction, ctrl);
    }
}
```

### Pattern 17: Angle Tool với Refined Filter

**Use case**: Tool tạo góc từ 2 đường thẳng + điểm chỉ hướng, với filter loại trừ endpoints của đường đã chọn.

```typescript
export class CreateAngleTool extends GeometryTool {
    selLogic: ThenSelector;

    createSelectionLogic() {
        // Stage 1: Chọn 2 đường thẳng
        const lineSelector = nLines(this.pQ, this.cursor, {
            count: 2,
            name: 'lineSelector',
        });

        // Stage 2: Chọn điểm chỉ hướng với refined filter
        const directionPointSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.cursor,
            refinedFilter: (vertex: RenderVertex) => {
                // Loại trừ các endpoints của đường đã chọn
                const selectedLines = this.selLogic.selected[0] as RenderLine[];
                return !selectedLines.some((selectedStroke: any) => {
                    const line = Array.isArray(selectedStroke) ? selectedStroke[0] : selectedStroke;
                    return line.startPointIdx === vertex.relIndex || line.endPointIdx === vertex.relIndex;
                });
            },
        });

        this.selLogic = then([lineSelector, directionPointSelector], {
            onComplete: this.performConstructionFromSelection.bind(this),
        });
    }
}
```

### Pattern 18: Bisector Tool với Transform Function

**Use case**: Tool tạo đường phân giác với transform function chiếu điểm lên tia phân giác.

```typescript
export class CreateBisectorLineTool extends GeometryTool {
    selLogic: ThenSelector;
    selectedAngle: RenderAngle | undefined;
    bisectorVector: number[] | undefined;
    rayPreview: RenderRay | undefined;

    createSelLogic() {
        // Chọn góc trước
        const angleSelector = stroke({
            selectableStrokeTypes: ['RenderAngle'],
            previewQueue: this.pQ,
            cursor: this.cursor,
        });

        // Chọn điểm cuối với transform function
        const vertexSelector = vertex({
            previewQueue: this.pQ,
            cursor: this.cursor,
            tfunc: (previewEl: RenderVertex, doc: GeoDocCtrl) => this.projectOnBisectorRay(previewEl, doc),
        });

        this.selLogic = then([angleSelector, vertexSelector], {
            onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => {
                const [angle, endSelection] = selector.selected;
                this.performConstruction(doc, angle as RenderAngle, endSelection);
            },
        });
    }

    // Transform function phức tạp
    projectOnBisectorRay(previewEl: RenderVertex, doc: GeoDocCtrl): RenderVertex {
        if (!this.selectedAngle || !this.bisectorVector || !this.rayPreview) {
            return previewEl;
        }

        try {
            const angleCoords = this.selectedAngle.coord('root', doc.rendererCtrl);
            const projectedCoords = projectPointOntoLine(previewEl.coords, angleCoords, this.bisectorVector);

            if (projectedCoords) {
                // Kiểm tra hướng của tia
                const pointVector = [projectedCoords[0] - angleCoords[0], projectedCoords[1] - angleCoords[1]];
                const dotProduct = pointVector[0] * this.bisectorVector[0] + pointVector[1] * this.bisectorVector[1];

                if (dotProduct >= 0) {
                    // Projection hợp lệ trên tia
                    previewEl.coords[0] = projectedCoords[0];
                    previewEl.coords[1] = projectedCoords[1];
                } else {
                    // Projection phía sau, dùng điểm bắt đầu tia
                    previewEl.coords[0] = angleCoords[0];
                    previewEl.coords[1] = angleCoords[1];
                }
            }
        } catch (error) {
            console.warn('Projection failed:', error);
        }

        return previewEl;
    }
}
```

### Pattern 19: Circular Sector với Circle Transform

**Use case**: Tool tạo cung tròn với điểm cuối bị ràng buộc trên đường tròn.

```typescript
export class CreateSectorTool extends GeometryTool {
    selLogic: ThenSelector;
    previewSector: any = null;

    createSelLogic() {
        // Chọn 2 điểm đầu (tâm và điểm xác định bán kính)
        const first2Points = nPoints(this.pQ, this.cursor, {
            count: 2,
            onPartialSelection: (newSel: SelectedVertex, curSel, selector, doc) => {
                return true;
            },
        });

        // Điểm cuối với circle transform hoặc vertex on stroke
        const endPointSelector = or(
            [
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.cursor,
                    // Transform để ràng buộc trên đường tròn
                    tfunc: el =>
                        circleTransform(
                            this.selLogic.selected[0] as SelectedVertex[],
                            el,
                            0 // Tâm là điểm đầu tiên
                        ),
                }),
                vertexOnStroke({
                    selectableStrokeTypes: [
                        'RenderCircle',
                        'RenderLine',
                        'RenderLineSegment',
                        'RenderVector',
                        'RenderRay',
                        'RenderSector',
                    ],
                }),
            ],
            {
                flatten: true,
            }
        );

        this.selLogic = then([first2Points, endPointSelector], {
            onComplete: (selector: ThenSelector, doc) => this.performConstruction(selector, doc),
        });
    }

    // Keyboard event handling cho clockwise/counterclockwise
    handleKeyboardEvent(event: GeoKeyboardEvent): GeoKeyboardEvent {
        if (event.eventType == 'keyup' && event.getKeys.includes('shift')) {
            this.toolState.clockwise = !this.toolState.clockwise;
            this.toolbar.update(this.toolType, this.toolState);
        }
        return event;
    }
}
```

### Pattern 20: Middle Point Tool với Multiple Input Types

**Use case**: Tool tạo điểm giữa với 2 input types khác nhau - 2 điểm hoặc 1 line segment.

```typescript
export class MiddlePointTool extends GeometryTool {
    selLogic: OrSelector<SelectedInput>;
    points: RenderVertex[] = [];

    createSelLogic() {
        this.selLogic = or<[RenderVertex, RenderVertex] | [RenderLineSegment]>(
            [
                // Option 1: Chọn 2 điểm
                repeat<RenderVertex>(
                    vertex({
                        genPreview: false,
                        previewQueue: this.pQ,
                        cursor: this.cursor,
                        refinedFilter: this.excludeSelection.bind(this),
                        onComplete: selector => this.points.push(selector.selected),
                    }),
                    { count: 2 }
                ),

                // Option 2: Chọn line segment với transform
                vertexOnStroke({
                    selectableStrokeTypes: ['RenderLineSegment', 'RenderVector', 'RenderSector'],
                    cursor: this.cursor,
                    tfunc: this.pointOnObjectTransform.bind(this),
                    syncPreview: true,
                    genPreview: true,
                }),
            ],
            {
                flatten: true,
                onComplete: this.performConstruction.bind(this),
            }
        );
    }

    excludeSelection(el: RenderVertex) {
        // Loại trừ điểm đã chọn
        return !this.points.includes(el);
    }

    pointOnObjectTransform(stroke: any, vertex: RenderVertex, doc: GeoDocCtrl): RenderVertex {
        // Transform để tạo middle point trên object
        return this.calculateMiddlePointOnStroke(stroke, vertex);
    }
}
```

### Pattern 22: Angle by Three Points với Complex Preview

**Use case**: Tool tạo góc từ 3 điểm với complex preview calculation và direction handling.

```typescript
export class CreateAngleByThreePointsTool extends GeometryTool {
    selLogic: ThenSelector;
    previewAngle?: RenderAngle;
    previewLine1?: RenderLine;
    previewLine2?: RenderLine;

    createSelLogic() {
        this.selLogic = then(
            [
                // Chọn 3 điểm đầu
                nPoints(this.pQ, this.cursor, { count: 3 }),

                // Điểm thứ 4 để xác định hướng góc
                vertex({
                    previewQueue: this.pQ,
                    cursor: this.cursor,
                    syncPreview: false,
                }),
            ],
            {
                onComplete: (selector: ThenSelector, doc: GeoDocCtrl) =>
                    this.performConstructionFromPreview(selector.selected, doc),
            }
        );
    }

    // Complex preview calculation với vector mathematics
    calculatePreviewAngle(
        p1: RenderVertex,
        root: RenderVertex,
        p2: RenderVertex,
        dir: RenderVertex
    ): {
        startLine: RenderLine;
        endLine: RenderLine;
        startLineVDir: 1 | -1;
        endLineVDir: 1 | -1;
        isSwaped: boolean;
    } {
        const vecRootP1 = vector(point(root.coords[0], root.coords[1]), point(p1.coords[0], p1.coords[1]));
        const vecRootP2 = vector(point(root.coords[0], root.coords[1]), point(p2.coords[0], p2.coords[1]));
        const vecRootDir = vector(point(root.coords[0], root.coords[1]), point(dir.coords[0], dir.coords[1]));

        const radP1P2 = angleCCW(vecRootP1, vecRootP2);
        const radP1Dir = angleCCW(vecRootP1, vecRootDir);

        const needSwap = radP1Dir > radP1P2;

        return {
            startLine: needSwap ? this.previewLine2 : this.previewLine1,
            endLine: needSwap ? this.previewLine1 : this.previewLine2,
            startLineVDir: needSwap ? -1 : 1,
            endLineVDir: needSwap ? 1 : -1,
            isSwaped: needSwap,
        };
    }
}
```

### Pattern 23: Simple Circle Tool

**Use case**: Tool tạo đường tròn đơn giản với 2 điểm (tâm + điểm trên đường tròn).

```typescript
export class CreateCircleTool extends GeometryTool {
    selLogic: RepeatSelector<SelectableType>;
    renderCircle: RenderCircle;

    constructor() {
        super();
        // Selector đơn giản: chọn 2 điểm
        this.selLogic = repeat(vertexS(this.pQ, this.cursor), {
            count: 2,
            onComplete: this.performConstruction.bind(this, 'Đường tròn'),
        });
    }

    doTrySelection(event: UIPointerEventData, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        // Preview circle khi có đủ 2 điểm
        if (selected && selected.length == 2) {
            // Extract vertices từ SelectedVertex arrays
            const v1 = ((selected[0] as Array<any>).length == 2 ? selected[0][1] : selected[0][0]) as RenderVertex;
            const v2 = ((selected[1] as Array<any>).length == 2 ? selected[1][1] : selected[1][0]) as RenderVertex;

            // Tạo preview circle và shape
            this.renderCircle = pCircle(ctrl, -20, v1, v2);
            const renderCircleShape = pCircleShape(ctrl, -21, v1, this.renderCircle, v2);
            this.pQ.add(renderCircleShape);
        }

        this.pQ.flush(ctrl);
    }
}
```

### Pattern 27: Polygon Tool với Dynamic Completion

**Use case**: Tool tạo đa giác với số điểm không giới hạn, kết thúc khi click vào điểm đầu tiên.

```typescript
export class CreatePolygonTool extends GeometryTool {
    selLogic: RepeatSelector<SelectedVertex>;
    firstPoint?: RenderVertex;

    createSelLogic() {
        this.selLogic = nPoints(this.pQ, this.cursor, {
            onPartialSelection: this.newPointSelected.bind(this),
        });
        this.selLogic.setOption('onComplete', this.performConstruction.bind(this));
    }

    // Logic xử lý mỗi điểm được chọn
    newPointSelected(newSel: SelectedVertex, curSel: SelectedVertex[], selector: any, doc: GeoDocCtrl): boolean {
        // Enable preview selection sau khi có >= 3 điểm
        if (curSel.length >= 3) {
            this.selLogic.get('vertex').setOption('preview', true);
        }

        let complete = false;
        if (curSel.length > 2 && vert(newSel).relIndex == this.firstPoint?.relIndex) {
            // Nếu click vào điểm đầu tiên -> hoàn thành polygon
            complete = true;
        }
        return !complete; // Return false để dừng selection
    }

    doTrySelection(event: UIPointerEventData, ctrl: GeoDocCtrl) {
        const selected = this.selLogic.trySelect(event, ctrl);

        if (selected && selected.length > 1) {
            // Tạo preview point màu đỏ cho điểm đầu tiên
            const v = vert(selected[0]);
            this.firstPoint = pVertex(-21, v.coords);
            this.firstPoint.renderProp = buildPreviewVertexRenderProp();
            this.firstPoint.renderProp.pointColor = '#ff0000'; // Màu đỏ để highlight
            this.pQ.add(this.firstPoint);

            // Tạo preview polygon
            const p = pPolygon(
                ctrl,
                -20,
                selected.map(s => vert(s).coords),
                true,
                RenderVector
            );
            this.pQ.add(p);
        }

        this.pQ.flush(ctrl);
    }
}
```

### Pattern 30: Utility Functions cho Code Reuse

**Use case**: Shared utility functions để tái sử dụng logic giữa các tools.

```typescript
// parallel_perpendicular.line.tool.utils.ts

/**
 * Tạo selection logic chung cho parallel/perpendicular line tools
 */
export function createLineToolSelLogic(
    pQ: PreviewQueue,
    pointerHandler: { cursor: any },
    onComplete: (selector: ThenSelector, doc: GeoDocCtrl) => Promise<void>
): ThenSelector {
    // Stage 1: Chọn line
    const lineSelector = stroke({
        selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
        previewQueue: pQ,
        cursor: pointerHandler.cursor,
    });

    // Stage 2: Chọn point
    const pointSelector = vertex({
        previewQueue: pQ,
        cursor: pointerHandler.cursor,
    });

    // Stage 3: Chọn final vertex (có thể trên stroke)
    const finalSelector = or(
        [
            vertex({
                previewQueue: pQ,
                cursor: pointerHandler.cursor,
            }),
            vertexOnStroke({
                selectableStrokeTypes: ['RenderLine', 'RenderLineSegment', 'RenderRay', 'RenderVector'],
                previewQueue: pQ,
                cursor: pointerHandler.cursor,
                preview: true,
                syncPreview: true,
            }),
        ],
        { flatten: true }
    );

    return then([lineSelector, pointSelector, finalSelector], {
        onComplete: onComplete,
    });
}

/**
 * Hiển thị preview line sau khi chọn line
 */
export function showPreviewLineAfterLineSelection(
    selectedLine: RenderLine,
    ctrl: GeoDocCtrl,
    pQ: PreviewQueue,
    previewId: number,
    isPerpendicularVector: boolean = false
): RenderLine | undefined {
    if (!selectedLine) return undefined;

    // Tính toán vector (parallel hoặc perpendicular)
    const lineVector = selectedLine.vector;
    if (!lineVector || lineVector.length < 2) return undefined;

    const calculatedVector = isPerpendicularVector
        ? [-lineVector[1], lineVector[0], lineVector[2] || 0] // Perpendicular
        : lineVector; // Parallel

    // Tạo preview line
    const startCoords = selectedLine.coord('start', ctrl.rendererCtrl);
    const endCoords = selectedLine.coord('end', ctrl.rendererCtrl);

    if (startCoords && endCoords) {
        const previewLine = pLine(ctrl, previewId, RenderLine, pVertex(-1, startCoords), pVertex(-2, endCoords));
        previewLine.vector = calculatedVector;

        pQ.add(previewLine);
        return previewLine;
    }

    return undefined;
}

/**
 * Tính toán scaling factor cho line construction
 */
export function calculateLineScalingFactor(
    selectedLine: RenderLine,
    selectedPoint: RenderVertex,
    finalVertex: RenderVertex
): number {
    const lineVector = selectedLine.vector;
    if (!lineVector) return 1;

    const pointToFinal = [
        finalVertex.coords[0] - selectedPoint.coords[0],
        finalVertex.coords[1] - selectedPoint.coords[1],
    ];

    const dotProduct = pointToFinal[0] * lineVector[0] + pointToFinal[1] * lineVector[1];
    const lineLength = Math.sqrt(lineVector[0] ** 2 + lineVector[1] ** 2);

    return lineLength > 0 ? dotProduct / lineLength ** 2 : 1;
}
```

## Best Practices cho Patterns

### 1. Tách biệt Logic và UI

```typescript
// ✅ Tốt - Logic selection tách biệt
export class GeometryTool {
    protected createSelLogic() {
        // Chỉ định nghĩa logic selection
    }

    protected handleSelection(selected: any) {
        // Xử lý kết quả selection
    }

    protected updatePreview(selected: any) {
        // Cập nhật preview UI
    }
}
```

### 2. Sử dụng Named Selectors

```typescript
// ✅ Tốt - Đặt tên cho selectors
const pointSelector = vertex({ name: 'centerPoint' });
const lineSelector = stroke({ name: 'baseLine' });

// Dễ dàng truy cập sau này
const centerPoint = this.selLogic.get('centerPoint');
```

### 3. Quản lý State đúng cách

```typescript
// ✅ Tốt - Reset state khi cần
override resetState() {
    this.selectedVertices = [];
    this.previewElements = [];
    this.selLogic.reset();
    super.resetState();
}
```

### 4. Error Handling

```typescript
// ✅ Tốt - Xử lý lỗi gracefully
handleSelection(selector: any, doc: GeoDocCtrl) {
    try {
        if (!selector.selected || selector.selected.length === 0) {
            this.showMessage('Không có phần tử nào được chọn');
            return;
        }

        this.processSelection(selector.selected);
    } catch (error) {
        this.showError('Lỗi xử lý selection: ' + error.message);
        this.resetState();
    }
}
```

Các patterns này cung cấp foundation vững chắc để xây dựng các geometry tools phức tạp với logic selection linh hoạt và dễ bảo trì.
