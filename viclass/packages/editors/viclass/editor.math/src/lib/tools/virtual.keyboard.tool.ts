import { DocumentId, ToolState } from '@viclass/editor.core';
import { Subscription } from 'rxjs';
import { MathDocCtrl } from '../docs/math.doc.ctrl';
import { MathEditor } from '../math.editor';
import { MathToolDocListener } from '../math.models';
import { MathTool } from './math.tool';
import { MathToolType } from './models';

export interface VirtualKeyboardState extends ToolState {
    isShowVirtualKeyboard: boolean;
    focusedDocId: DocumentId | null;
}

const DEFAULT_TOOLSTATE: VirtualKeyboardState = {
    isShowVirtualKeyboard: false,
    focusedDocId: null,
};

export class VirtualKeyboardTool extends MathTool<VirtualKeyboardState> implements MathToolDocListener {
    readonly toolType: MathToolType = 'VirtualKeyboardTool';
    override toolState: VirtualKeyboardState;
    private subscription?: Subscription;

    constructor(editor: MathEditor) {
        super(editor);
    }

    override onEnable(): void {
        this.onDocAttached();
    }

    override onDisable(): void {
        this.onDocDetached();
    }

    setShowVirtualKeyboard(show: boolean): void {
        this.executeInFocusedDocCtrl((docCtrl: MathDocCtrl) =>
            docCtrl.mathLib.virtualKeyboardManager.setShowVirtualKeyboard(show)
        );
    }

    onDocAttached(docCtrl?: MathDocCtrl): void {
        this.subscription?.unsubscribe();

        docCtrl = docCtrl ? docCtrl : this.getFocusedMathDocCtrls()?.[0];
        const kbManager = docCtrl?.mathLib?.virtualKeyboardManager;
        if (!kbManager) return;

        this.subscription = kbManager.isShowing$.subscribe(isShowing =>
            this.changeToolState({
                isShowVirtualKeyboard: isShowing,
                focusedDocId: docCtrl?.state?.globalId || null,
            })
        );
    }

    onDocDetached(docCtrl?: MathDocCtrl): void {
        if (!docCtrl || docCtrl.state.globalId === this.toolState.focusedDocId) {
            if (this.subscription) {
                this.subscription.unsubscribe();
                this.subscription = undefined;
            }

            if (this.toolState?.isShowVirtualKeyboard) {
                if (docCtrl) {
                    docCtrl.mathLib.virtualKeyboardManager.setShowVirtualKeyboard(false);
                } else {
                    window.mathVirtualKeyboard.hide();
                }
            }

            this.changeToolState(DEFAULT_TOOLSTATE);
        }
    }

    private changeToolState(newState: VirtualKeyboardState) {
        this.toolState = { ...newState };
        this.toolbar.update('VirtualKeyboardTool', this.toolState);
    }
}
