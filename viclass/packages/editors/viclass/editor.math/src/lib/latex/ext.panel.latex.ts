import { LatexGroup } from './latex.model';

export const EXT_PANEL_GROUP: LatexGroup = {
    type: 'group',
    label: 'Dấu nhấn đặc biệt',
    icon: 'group_accents-ext-panel',
    children: [
        {
            type: 'sub-group',
            children: [
                {
                    type: 'item',
                    latex: '\\widetilde{#@}',
                    icon: 'wide-tilde',
                    label: {
                        en: 'Wide tilde',
                    },
                },
                {
                    type: 'item',
                    latex: '\\widehat{#@}',
                    icon: 'wide-hat',
                    label: {
                        en: 'Wide hat',
                    },
                },
                {
                    type: 'item',
                    latex: '\\overleftarrow{#@}',
                    icon: 'over-left-arrow',
                    label: {
                        en: 'over-left-arrow',
                    },
                },
                {
                    type: 'item',
                    latex: '\\overrightarrow{#@}',
                    icon: 'over-right-arrow',
                    label: {
                        en: 'over-right-arrow',
                    },
                },
                {
                    type: 'item',
                    latex: '\\underleftarrow{#@}',
                    icon: 'under-left-arrow',
                    label: {
                        en: 'Under left arrow',
                    },
                },
                {
                    type: 'item',
                    latex: '\\underrightarrow{#@}',
                    icon: 'under-right-arrow',
                    label: {
                        en: 'Under right arrow',
                    },
                },
                {
                    type: 'item',
                    latex: '\\overleftrightarrow{#@}',
                    icon: 'over-left-right-arrow',
                    label: {
                        en: 'Over left right arrow',
                    },
                },
                {
                    type: 'item',
                    latex: '\\underleftrightarrow{#@}',
                    icon: 'under-left-right-arrow',
                    label: {
                        en: 'Under left right arrow',
                    },
                },
                {
                    type: 'item',
                    latex: '\\overline{#@}',
                    icon: 'over-line',
                    label: {
                        en: 'Over line',
                    },
                },
                {
                    type: 'item',
                    latex: '\\underline{#@}',
                    icon: 'under-line',
                    label: {
                        en: 'Under line',
                    },
                },
                {
                    type: 'item',
                    latex: '\\overbrace{#@}',
                    icon: 'over-brace',
                    label: {
                        en: 'Over brace',
                    },
                },
                {
                    type: 'item',
                    latex: '\\underbrace{#@}',
                    icon: 'under-brace',
                    label: {
                        en: 'Under brace',
                    },
                },
                {
                    type: 'item',
                    latex: '\\overset{a}{#@}',
                    icon: 'over-set',
                    label: {
                        en: 'Over set',
                    },
                },
                {
                    type: 'item',
                    latex: '\\underset{b}{#@}',
                    icon: 'under-set',
                    label: {
                        en: 'Under set',
                    },
                },
                {
                    type: 'item',
                    latex: '\\sqrt{#@}',
                    icon: 'square-root',
                    label: {
                        en: 'square root',
                    },
                },
            ],
        },
    ],
};
