import { ToolState } from '@viclass/editor.core';
import { Subscription } from 'rxjs';
import { WordTool, WordToolType } from '.';
import { DEFAULT_CONTEXT, WordContext } from '../doceditor/managers';
import { WordToolDocListener } from '../word.models';
import { WordDocCtrl } from '../docs/word.doc.ctrl';

export interface WordContextState extends ToolState, WordContext {
    focusedDocId: string;
}

const DEFAULT_TOOLSTATE: WordContextState = {
    ...DEFAULT_CONTEXT,
    focusedDocId: '',
};

/**
 *  Tool to retrieve the current selection context of word editor
 */
export class WordContextTool extends WordTool<WordContextState, any> implements WordToolDocListener {
    readonly toolType: WordToolType = 'WordContextTool';
    override toolState: WordContextState = { ...DEFAULT_TOOLSTATE };
    private subscription?: Subscription;

    override onEnable(): void {
        this.onDocAttached();
    }

    override onDisable(): void {
        this.onDocDetached();
    }

    onDocAttached(docCtrl?: WordDocCtrl): void {
        // remove any old subscription
        this.subscription?.unsubscribe();

        docCtrl = docCtrl ? docCtrl : this.getFocusedWordDocCtrls()?.[0];
        const contextManager = docCtrl?.wordLib?.contextManager;
        if (!contextManager) return;

        this.subscription = contextManager.selectionContext$.subscribe(ctx =>
            this.changeToolState({
                ...ctx,
                focusedDocId: docCtrl?.state?.globalId || '',
            })
        );
    }

    onDocDetached(docCtrl?: WordDocCtrl): void {
        if (!docCtrl || docCtrl.state.globalId === this.toolState.focusedDocId) {
            if (this.subscription) {
                this.subscription.unsubscribe();
                this.subscription = undefined;
            }

            this.changeToolState(DEFAULT_TOOLSTATE);
        }
    }

    private changeToolState(newState: WordContextState) {
        this.toolState = { ...newState };
        this.toolbar.update('WordContextTool', this.toolState);
    }
}
