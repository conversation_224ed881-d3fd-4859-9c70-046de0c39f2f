import { ToolState } from '@viclass/editor.core';
import { Subscription } from 'rxjs';
import { WordTool, WordToolType } from '.';
import {
    CellVerticalAlign,
    DEFAULT_TABLE_CONTEXT,
    TableContext,
    TableManager,
} from '../doceditor/managers/table-manager/table.manager';
import { WordDocCtrl } from '../docs/word.doc.ctrl';
import { WordToolDocListener } from '../word.models';

/**
 * Border application modes for table selection
 */
export enum BorderStyleApplyMode {
    ALL = 'all', // Apply to all borders of selected cells
    TOP = 'top', // Apply to top borders only
    BOTTOM = 'bottom', // Apply to bottom borders only
    LEFT = 'left', // Apply to left borders only
    RIGHT = 'right', // Apply to right borders only
    INNER = 'inner', // Apply to inner borders only (between cells)
    OUTER = 'outer', // Apply to outer borders only (perimeter)
}

/**
 * Border style data. Used in table border dialog
 */
export type BorderStyleData = {
    width: number;
    type: string;
    color: string;
};

/**
 * Default border style when no border style is provided
 */
export const DEFAULT_BORDER_STYLE = {
    width: 1,
    type: 'solid',
    color: '#000000',
} as const;

export interface TableContextState extends ToolState, TableContext {
    isTableEditing: boolean;
    currentCellBgColor: string;
    currentCellVerticalAlign?: CellVerticalAlign;
    currentCellBorderStyle?: BorderStyleData;
}

const DEFAULT_CONTEXT_STATE = {
    ...DEFAULT_TABLE_CONTEXT,
    isTableEditing: false,
    currentCellBgColor: 'transparent',
    currentCellVerticalAlign: undefined,
    currentCellBorderStyle: undefined,
};

export class WordTableTool extends WordTool<TableContextState, any> implements WordToolDocListener {
    override toolType: WordToolType = 'WordTableTool';
    override toolState: TableContextState = { ...DEFAULT_CONTEXT_STATE };

    private subscriptions: Subscription[] = [];
    private focusedTableManager?: TableManager;

    get tableManager(): TableManager | null {
        const docCtrl = this.getFocusedWordDocCtrls()?.[0];
        return docCtrl?.wordLib?.tableManager || null;
    }

    override onEnable(): void {
        this.onDocAttached();
    }

    override onDisable(): void {
        this.onDocDetached();
    }

    /**
     * Initialize the tool when the document is attached. Include subscribe to the changes in the table context
     */
    onDocAttached(docCtrl?: WordDocCtrl): void {
        const manager = docCtrl?.wordLib?.tableManager || this.tableManager;
        if (!manager) return;

        this.cleanSubscriptions();

        this.subscriptions.push(
            manager.tableContext$.subscribe(ctx =>
                this.changeToolState({
                    ...this.toolState,
                    ...ctx,
                })
            )
        );

        this.subscriptions.push(
            manager.tableCellContext$.subscribe(cellNodeCtx =>
                this.changeToolState({
                    ...this.toolState,
                    isTableEditing: !!cellNodeCtx.cell,
                    currentCellBgColor: cellNodeCtx.cellBgColor,
                    currentCellVerticalAlign: cellNodeCtx.verticalAlign,
                    currentCellBorderStyle: parseBorderStyle(cellNodeCtx.borderStyle),
                })
            )
        );

        this.focusedTableManager = manager;
    }

    /**
     * Clean up the subscriptions when the document is detached
     */
    onDocDetached(docCtrl?: WordDocCtrl): void {
        if (!docCtrl || docCtrl.wordLib.tableManager === this.focusedTableManager) {
            this.cleanSubscriptions();
            this.changeToolState({ ...DEFAULT_CONTEXT_STATE });
        }
    }

    /**
     * Insert new table into the document.
     */
    insertTable(columns: number, row: number, includeHeader = false) {
        const clampRow = Math.max(1, row);
        const clampColumns = Math.min(100, Math.max(1, columns));

        this.tableManager?.insertTable(clampColumns, clampRow, includeHeader);
    }

    /**
     * Merge selected cells
     */
    mergeCells() {
        this.tableManager?.mergeTableCellsAtSelection();
    }

    /**
     * Unmerge selected cells
     */
    unmergeCells() {
        this.tableManager?.unmergeTableCellsAtSelection();
    }

    /**
     * Insert a new row before or after the current selection
     */
    insertRow(insertAfter: boolean) {
        this.tableManager?.insertTableRowAtSelection(insertAfter);
    }

    /**
     * Insert a new column before or after the current selection
     */
    insertColumn(insertAfter: boolean) {
        this.tableManager?.insertTableColumnAtSelection(insertAfter);
    }

    /**
     * Delete selected rows
     */
    deleteRow() {
        this.tableManager?.deleteTableRowAtSelection();
    }

    /**
     * Delete selected columns
     */
    deleteColumn() {
        this.tableManager?.deleteTableColumnAtSelection();
    }

    /**
     * Delete the selected table
     */
    deleteTable() {
        this.tableManager?.deleteTableAtSelection();
    }

    /**
     * Change the background color of the selected cell
     */
    changeCellBgColor(color: string) {
        this.tableManager?.handleCellBackgroundColor(color);
    }

    /**
     * Change the vertical alignment of the selected cell
     */
    changeCellVerticalAlign(align: string) {
        this.tableManager?.formatVerticalAlign(align);
    }

    /**
     * Change border style with advanced per-side control and application modes
     * @param borderData - The border style data to apply
     * @param mode - How to apply the border (all, top, bottom, left, right, inner, outer)
     */
    changeCellBorderStyle(borderData: BorderStyleData, mode: BorderStyleApplyMode) {
        this.tableManager?.handleCellBorderStyleAdvanced(borderData, mode);
    }
    /**
     * Distributes the height of selected rows evenly
     */
    distributeRowsEvenly() {
        this.tableManager?.distributeRowsEvenly();
    }

    /**
     * Distributes the width of selected columns evenly
     */
    distributeColumnsEvenly() {
        this.tableManager?.distributeColumnsEvenly();
    }

    /**
     * utility function to update the tool state
     */
    private changeToolState(newState: TableContextState) {
        this.toolState = { ...newState };
        this.toolbar.update('WordTableTool', this.toolState);
    }

    /**
     * utility function to clean up the subscriptions
     */
    private cleanSubscriptions() {
        this.subscriptions.forEach(s => s.unsubscribe());
        this.subscriptions = [];
        this.focusedTableManager = undefined;
    }
}

/**
 * Parses a border style string and extracts the width, type, and color.
 * If the input is invalid or missing, it returns a default border style.
 *
 * To be used to parse the border style of a table cell
 *
 * @param border - The border style string in the format "<width>px <type> <color>".
 * @returns An object containing width (number), type (string), and color (string).
 */
export function parseBorderStyle(border: string | undefined): BorderStyleData {
    if (!border) return DEFAULT_BORDER_STYLE;

    const parts = border.trim().split(/\s+/);
    if (parts.length < 3) return DEFAULT_BORDER_STYLE;

    const [widthStr, type, ...colorParts] = parts;
    const color = colorParts.join(' '); // color can contains spaces e.g. "rgb(0, 255, 0)"
    const widthMatch = widthStr.match(/(\d+(\.\d+)?)px/);

    if (!widthMatch) return DEFAULT_BORDER_STYLE;

    const width = parseFloat(widthMatch[1]);

    return { width, type, color };
}
