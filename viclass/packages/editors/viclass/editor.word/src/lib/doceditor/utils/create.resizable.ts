import { calculateZoomLevel } from '@lexical/utils';
import { BaseBoardViewportManager, VDocCtrl } from '@viclass/editor.core';
import { CLICK_COMMAND, COMMAND_PRIORITY_HIGH, LexicalEditor, NodeKey } from 'lexical';
import { NodeSelectionUtils } from './node.selection.utils';

type PositionData = {
    direction: number;
    startX: number;
    startY: number;
    startWidth: number;
    startHeight: number;
    currentWidth: number;
    currentHeight: number;
    ratio: number;
    alignCenter: boolean;
};

function clamp(value: number, min: number, max: number) {
    return Math.min(Math.max(value, min), max);
}

const Direction = {
    east: 1 << 0,
    north: 1 << 3,
    south: 1 << 1,
    west: 1 << 2,
};

export function createResizableNode(
    resizableNode: HTMLElement,
    lexical: LexicalEditor,
    nodeKey: NodeKey,
    selectionUtils: NodeSelectionUtils,
    docCtrl: VDocCtrl,
    options?: {
        maxWidth?: number;
        maxHeight?: number;
        checkResizable?: () => boolean;
        onResizing?: (width: number, height: number) => void;
        onResizeFinished?: (width: number, height: number) => void;
        selectOverlay?: boolean;
    }
): () => void {
    const teardownFns: (() => void)[] = [];

    const editorRootElement = lexical.getRootElement();
    const rootDocument = editorRootElement.ownerDocument;
    // Find max width, accounting for editor padding.
    let maxWidthContainer: number;
    let maxHeightContainer: number;

    const minWidth = 20;
    const minHeight = 20;

    resizableNode.classList.add('resizable');
    const selectSubsc = selectionUtils.isSelected$.subscribe(isSelected => {
        if (isSelected) {
            resizableNode.classList.add('resizable-selected');
        } else {
            resizableNode.classList.remove('resizable-selected');
        }
    });
    teardownFns.push(() => selectSubsc.unsubscribe());

    let posData: PositionData | null = null;
    let requestedFrame = false;

    // Intercep click command to prioritize the resizing
    teardownFns.push(
        lexical.registerCommand(
            CLICK_COMMAND,
            (_event: MouseEvent) => {
                return posData != null;
            },
            COMMAND_PRIORITY_HIGH
        )
    );

    const setStartCursor = (direction: number) => {
        const ew = direction === Direction.east || direction === Direction.west;
        const ns = direction === Direction.north || direction === Direction.south;
        const nwse =
            (direction & Direction.north && direction & Direction.west) ||
            (direction & Direction.south && direction & Direction.east);

        const cursorDir = ew ? 'ew' : ns ? 'ns' : nwse ? 'nwse' : 'nesw';

        if (editorRootElement !== null) {
            editorRootElement.style.setProperty('cursor', `${cursorDir}-resize`, 'important');
        }
        if (document.body !== null) {
            document.body.style.setProperty('cursor', `${cursorDir}-resize`, 'important');
            document.body.style.setProperty('-webkit-user-select', `none`, 'important');
        }
    };

    const getViewportZoomFactor = () => {
        return docCtrl.viewport instanceof BaseBoardViewportManager ? docCtrl.viewport.zoomLevel : 1;
    };

    const setEndCursor = () => {
        if (editorRootElement !== null) {
            editorRootElement.style.setProperty('cursor', 'text');
        }
        if (document.body !== null) {
            document.body.style.removeProperty('cursor');
            document.body.style.removeProperty('-webkit-user-select');
        }
    };

    // other handlers (like the table selection of `applyTableHandler()`) are using `mousemove` event -> can mixed up btw selection and resize -> block
    const blockMouseEventOnProcessing = (event: MouseEvent) => {
        if (posData != null) event.stopPropagation();
    };

    const handlePointerMove = (event: PointerEvent) => {
        if (requestedFrame) return;

        requestAnimationFrame(() => {
            requestedFrame = false;
            if (!posData) return;

            const isHorizontal = posData.direction & (Direction.east | Direction.west);
            const isVertical = posData.direction & (Direction.south | Direction.north);

            const zoom = calculateZoomLevel(resizableNode);
            const vpZoom = getViewportZoomFactor();
            if (isHorizontal && isVertical) {
                let diff = Math.floor(posData.startX - event.clientX / zoom);
                diff = posData.direction & Direction.east ? -diff : diff;

                const unclamped = (posData.startWidth + diff) * vpZoom;
                const width = clamp(unclamped, minWidth, maxWidthContainer);

                const height = width / posData.ratio;
                resizableNode.style.width = `${width}px`;
                resizableNode.style.height = `${height}px`;
                posData.currentHeight = height;
                posData.currentWidth = width;
            } else {
                if (isVertical) {
                    let diff = Math.floor(posData.startY - event.clientY / zoom);
                    diff = posData.direction & Direction.south ? -diff : diff;

                    const unclamped = (posData.startHeight + diff) * vpZoom;
                    const height = clamp(unclamped, minHeight, maxHeightContainer);

                    resizableNode.style.height = `${height}px`;
                    posData.currentHeight = height;
                }

                if (isHorizontal) {
                    let diff = Math.floor(posData.startX - event.clientX / zoom);
                    diff = posData.direction & Direction.east ? -diff : diff;

                    const scale = posData.alignCenter ? 2 : 1;
                    const unclamped = (posData.startWidth + diff * scale) * vpZoom;
                    const width = clamp(unclamped, minWidth, maxWidthContainer);

                    resizableNode.style.width = `${width}px`;
                    posData.currentWidth = width;
                }

                posData.ratio = posData.currentWidth / posData.currentHeight;
            }

            if (options.onResizing) {
                options.onResizing(posData.currentWidth, posData.currentHeight);
            }
        });

        requestedFrame = true;
    };

    const handlePointerUp = () => {
        if (posData && options.onResizeFinished) {
            options.onResizeFinished(posData.currentWidth, posData.currentHeight);
        }
        posData = null;
        setEndCursor();
        resizableNode.classList.remove('resizing');

        rootDocument.removeEventListener('pointerup', handlePointerUp);
        rootDocument.removeEventListener('pointermove', handlePointerMove);
        rootDocument.removeEventListener('mousemove', blockMouseEventOnProcessing);

        selectionUtils.setSelected(true);
    };

    const handlePointerDown = (event: PointerEvent) => {
        if (!!options.checkResizable && !options.checkResizable()) return;
        event.stopPropagation();

        const direction = Number((event.target as HTMLDivElement).dataset['direction']);
        const { width, height } = resizableNode.getBoundingClientRect();
        const zoom = calculateZoomLevel(resizableNode);
        const vpZoom = getViewportZoomFactor();

        const blockTypeAncestor = findClosestBlockAncestor(resizableNode);
        const alignCenter = isAlignCentered(blockTypeAncestor);

        posData = {
            direction,
            startX: event.clientX / zoom,
            startY: event.clientY / zoom,
            // real size on the screen
            startWidth: width,
            startHeight: height,
            // size will be saved to the node
            currentWidth: width * vpZoom,
            currentHeight: height * vpZoom,
            ratio: width / height,
            alignCenter: alignCenter,
        };

        maxWidthContainer = options.maxWidth
            ? options.maxWidth
            : editorRootElement !== null
              ? editorRootElement.getBoundingClientRect().width * vpZoom - 20
              : 100;
        maxHeightContainer = options.maxHeight
            ? options.maxHeight
            : editorRootElement !== null
              ? editorRootElement.getBoundingClientRect().height * vpZoom - 20
              : 100;

        setStartCursor(direction);
        resizableNode.classList.add('resizing');

        rootDocument.addEventListener('pointerup', handlePointerUp);
        rootDocument.addEventListener('pointermove', handlePointerMove);
        rootDocument.addEventListener('mousemove', blockMouseEventOnProcessing);
    };

    // prettier-ignore
    const { resizerN, resizerE, resizerS, resizerW, resizerNE, resizerSE, resizerSW, resizerNW } = createResizer(resizableNode);
    const resizers = [resizerN, resizerE, resizerS, resizerW, resizerNE, resizerSE, resizerSW, resizerNW];

    if (options.selectOverlay) {
        const selectOverlay = rootDocument.createElement('div');
        selectOverlay.className = 'resizable-select-overlay';
        resizableNode.appendChild(selectOverlay);
    }

    resizers.forEach(resizer => {
        resizer.addEventListener('pointerdown', handlePointerDown);
    });

    return () => {
        resizers.forEach(resizer => {
            resizer.removeEventListener('pointerdown', handlePointerDown);
            resizer.remove();
        });
        resizers.length = 0;
        teardownFns.forEach(fn => fn());
    };
}

function createResizer(resizableNode: HTMLElement) {
    const resizerN = document.createElement('div');
    resizerN.className = 'node-resizer node-resizer-n';
    resizerN.dataset['direction'] = String(Direction.north);

    const resizerE = document.createElement('div');
    resizerE.className = 'node-resizer node-resizer-e';
    resizerE.dataset['direction'] = String(Direction.east);

    const resizerS = document.createElement('div');
    resizerS.className = 'node-resizer node-resizer-s';
    resizerS.dataset['direction'] = String(Direction.south);

    const resizerW = document.createElement('div');
    resizerW.className = 'node-resizer node-resizer-w';
    resizerW.dataset['direction'] = String(Direction.west);

    const resizerNE = document.createElement('div');
    resizerNE.className = 'node-resizer node-resizer-ne';
    resizerNE.dataset['direction'] = String(Direction.north | Direction.east);

    const resizerSE = document.createElement('div');
    resizerSE.className = 'node-resizer node-resizer-se';
    resizerSE.dataset['direction'] = String(Direction.south | Direction.east);

    const resizerSW = document.createElement('div');
    resizerSW.className = 'node-resizer node-resizer-sw';
    resizerSW.dataset['direction'] = String(Direction.south | Direction.west);

    const resizerNW = document.createElement('div');
    resizerNW.className = 'node-resizer node-resizer-nw';
    resizerNW.dataset['direction'] = String(Direction.north | Direction.west);

    resizableNode.appendChild(resizerN);
    resizableNode.appendChild(resizerE);
    resizableNode.appendChild(resizerS);
    resizableNode.appendChild(resizerW);
    resizableNode.appendChild(resizerNE);
    resizableNode.appendChild(resizerSE);
    resizableNode.appendChild(resizerSW);
    resizableNode.appendChild(resizerNW);

    // prettier-ignore
    return { resizerN, resizerE, resizerS, resizerW, resizerNE, resizerSE, resizerSW, resizerNW };
}

function findClosestBlockAncestor(element: HTMLElement): HTMLElement | null {
    let ancestor = element.parentElement;
    while (ancestor && ancestor !== document.body) {
        // Stop at body to avoid infinite loops
        const style = window.getComputedStyle(ancestor);
        if (style.display === 'block') {
            return ancestor; // Found a block display ancestor
        }
        ancestor = ancestor.parentElement; // Move up to the next ancestor
    }
    return null; // No block display ancestor found
}

function isAlignCentered(element: HTMLElement | null): boolean {
    if (!element) return false;
    const style = window.getComputedStyle(element);
    return style.textAlign === 'center';
}
