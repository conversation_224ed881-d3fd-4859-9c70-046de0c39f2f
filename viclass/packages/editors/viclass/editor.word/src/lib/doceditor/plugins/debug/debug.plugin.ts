import {
    $getSelection,
    COMMAND_PRIORITY_CRITICAL,
    COMMAND_PRIORITY_NORMAL,
    LexicalCommand,
    LexicalEditor,
    PASTE_COMMAND,
    SELECTION_CHANGE_COMMAND,
} from 'lexical';
import { Subject, tap, throttleTime } from 'rxjs';
import { WordPlugin } from '../word.plugin';
import { generateContent } from './generate.content';
import { Binding } from '@lexical/yjs';

export type LexicalCommandLog = LexicalCommand<unknown> & { payload: unknown };

export function registerLexicalCommandLogger(
    editor: LexicalEditor,
    cmdLogger: (oldValue: LexicalCommandLog) => void
): () => void {
    const unregisterCommandListeners = new Set<() => void>();

    for (const [command] of editor._commands) {
        unregisterCommandListeners.add(
            editor.registerCommand(
                command,
                payload => {
                    cmdLogger({
                        payload,
                        type: command.type ? command.type : 'UNKNOWN',
                    });

                    return false;
                },
                COMMAND_PRIORITY_CRITICAL
            )
        );
    }

    return () => unregisterCommandListeners.forEach(unregister => unregister());
}

export class DebugPlugin extends WordPlugin {
    commandSizeLimit = 10;

    exportDOM = false;

    private _commands: Array<LexicalCommand<unknown> & { payload: unknown }> = [];

    private _isWatching = false;
    private _watcherTeardowns: Array<() => void> = [];

    printCmd = false;

    get yBinding(): Binding {
        return this.wordLib.binding;
    }

    private readonly watchUpdates$ = new Subject<void>();
    private watchUpdateSubsc = this.watchUpdates$
        .pipe(
            throttleTime(100),
            tap(() => {
                console.clear();
                this.logStructure();
            })
        )
        .subscribe();

    private commandLogger = (oldValue: LexicalCommandLog): void => {
        if (this.printCmd) {
            console.log('old value', oldValue);
        }

        this._commands.push(oldValue);
        if (this._commands.length > this.commandSizeLimit) {
            this._commands.shift();
        }
    };

    override init() {
        console.log('init debug plugin', this);
        this.addUnsubscribe(registerLexicalCommandLogger(this.lexical, this.commandLogger));

        this.addUnsubscribe(
            this.lexical.registerCommand(
                PASTE_COMMAND,
                (e: ClipboardEvent) => {
                    const se = $getSelection();
                    const { clipboardData } = e;
                    const allData: string[] = [];

                    if (clipboardData && clipboardData.types) {
                        clipboardData.types.forEach(type => {
                            allData.push(type, clipboardData.getData(type), '\n');
                        });
                    }

                    console.log(allData.join('\n'));
                    return false;
                },
                COMMAND_PRIORITY_NORMAL
            )
        );

        this.addUnsubscribe(() => {
            this.watchUpdateSubsc.unsubscribe();
            this.unwatchStructure();
        });
    }

    watchStructure() {
        if (this._isWatching) return;

        this._isWatching = true;

        this.addWatcher(
            this.lexical.registerUpdateListener(() => {
                this.watchUpdates$.next();
            })
        );
        this.addWatcher(
            this.lexical.registerCommand(
                SELECTION_CHANGE_COMMAND,
                () => {
                    this.watchUpdates$.next();
                    return false;
                },
                COMMAND_PRIORITY_CRITICAL
            )
        );
    }

    unwatchStructure() {
        this._isWatching = false;

        this._watcherTeardowns.forEach(teardown => teardown());
        this._watcherTeardowns.length = 0;
    }

    logStructure() {
        console.log(generateContent(this.lexical, this._commands, this.exportDOM));
    }

    private addWatcher(teardown: () => void) {
        this._watcherTeardowns.push(teardown);
    }
}
