export class OverflowManager {
    private readonly overflowObserver!: IntersectionObserver;
    // track per‑element state & last toggle time
    private readonly state = new Map<
        HTMLElement,
        {
            isOverflow: boolean;
            lastToggle: number;
        }
    >();
    private DEBOUNCE_MS = 100;

    constructor(root: HTMLElement) {
        this.overflowObserver = new IntersectionObserver(
            entries => {
                entries.forEach(entry => {
                    const el = entry.target as HTMLElement;
                    const now = entry.time;
                    const wantOverflow = entry.intersectionRatio === 0;

                    const prev = this.state.get(el) ?? { lastToggle: now, isOverflow: !wantOverflow };
                    // no state change?
                    if (prev.isOverflow === wantOverflow) return;
                    // still within debounce window?
                    if (now - prev.lastToggle < this.DEBOUNCE_MS) return;

                    // commit
                    this.state.set(el, { lastToggle: now, isOverflow: wantOverflow });

                    if (wantOverflow) {
                        const brc = entry.boundingClientRect;
                        Object.assign(el.style, {
                            contentVisibility: 'hidden',
                            containIntrinsicSize: `${brc.width}px ${brc.height}px`,
                        });
                    } else {
                        Object.assign(el.style, {
                            contentVisibility: '',
                        });
                    }
                });
            },
            {
                root: root,
                rootMargin: '30px',
                threshold: [0, 0.5],
            }
        );
    }

    /** Start observing a new element */
    observe(el: HTMLElement) {
        this.overflowObserver.observe(el);
    }

    /** Stop observing and clear any stored state on that element */
    unobserve(el: HTMLElement) {
        this.overflowObserver.unobserve(el);
        this.state.delete(el);
    }

    destroy() {
        this.overflowObserver.disconnect();
        this.state.clear();
    }
}
