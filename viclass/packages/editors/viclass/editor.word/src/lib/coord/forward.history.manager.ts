import { HistoryI<PERSON>, HistoryManager, SupportFeatureHistory } from '@viclass/editor.core';
import { WordSubViewportHistoryItem } from '../history';

/**
 * A history manager that manager the historys item for viewport inside the word editor
 */
export class WordForwardHistoryManager extends HistoryManager {
    constructor(
        private targetHistoryManager: HistoryManager,
        private supporter: SupportFeatureHistory
    ) {
        super();
    }

    override push(item: HistoryItem) {
        const wrappedItem = new WordSubViewportHistoryItem(this.supporter, item);
        this.targetHistoryManager.push(wrappedItem);
    }

    override undo() {
        return this.targetHistoryManager.undo();
    }

    override redo() {
        return this.targetHistoryManager.redo();
    }

    override get undoAble(): boolean {
        return this.targetHistoryManager.undoAble;
    }

    override get redoAble(): boolean {
        return this.targetHistoryManager.redoAble;
    }

    override getAllItem(editor?: SupportFeatureHistory): HistoryItem[] {
        return this.targetHistoryManager.getAllItem(editor);
    }

    override clear(items: HistoryItem[]): void {
        this.targetHistoryManager.clear(items);
    }

    override clearWithCondition(condition: (item: HistoryItem, fromStack: 'undo' | 'redo') => boolean): void {
        this.targetHistoryManager.clearWithCondition(condition);
    }
}
