import { BoundaryDelegator } from '@viclass/editor.coordinator/classroom';
import {
    AttachmentFeature,
    AwarenessFeature,
    AwarenessId,
    Cmd,
    CmdChannel,
    ContentVisibilityCheckFeature,
    CopyPasteDTO,
    CRUDChangeResult,
    CRUDDelegator,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocumentEditor,
    DocumentId,
    DOMElementLayerCtrl,
    EditorBase,
    EditorConfig,
    EditorCoordinator,
    EditorId,
    EditorType,
    ExportTarget,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FEATURE_ATTACHMENT,
    FEATURE_AWARENESS,
    FEATURE_CONTENT_VISIBILITY,
    FEATURE_COPYPASTE,
    FEATURE_CRD_DOC,
    FEATURE_CRUD,
    FEATURE_HISTORY,
    FEATURE_HTML_EXPORT,
    FEATURE_REMOVE,
    FEATURE_ROB,
    FEATURE_SELECTION,
    HasSelectionFeature,
    HistoryFeature,
    InsertDocCtrlDelegator,
    LoadingContext,
    OperationMode,
    ROBFeature,
    SelectContext,
    SelectDelegator,
    SelectionFeature,
    SupportContentVisibilityCheckFeature,
    SupportCopyPasteFeature,
    SupportFeatureHistory,
    SupportHtmlExportFeature,
    SupportRemoveFeature,
    SupportSelectFeature,
    ThrottleCombinator,
    ToolBar,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { deserializer } from './cmds/composer.cmd';
import { ComposerCmdProcessor } from './cmds/composer.cmd.processor';
import { ComposerGateway } from './composer.gateway';
import { ComposerDocInitData } from './composer.models';
import { ComposerEditorConfig, ComposerEditorCoordinatorConfig } from './config';
import { ComposerEditorCoordinator } from './coord/composer.coordinator';
import { ComposerDocCtrl } from './docs/composer.doc.ctrl';
import { ComposerHistoryItem, ComposerSubViewportHistoryItem } from './history';
import { ComposerDoc, ComposerLayer, FetchDocResponse } from './model';
import {
    ComposerCreateDocumentTool,
    ComposerSettingsTool,
    ComposerToolBar,
    ComposerToolType,
    ContentTool,
    DocInsertTool,
    SubEditorManagerTool,
} from './tools';

/**
 * Implementation of the viclass document editor (wysiwyg)
 */
export class ComposerEditor
    extends EditorBase<ComposerDocCtrl>
    implements
        DocumentEditor,
        SupportContentVisibilityCheckFeature,
        SupportCopyPasteFeature,
        SupportRemoveFeature,
        SupportFeatureHistory,
        HasSelectionFeature,
        SupportHtmlExportFeature
{
    static readonly awarenessPastingId: AwarenessId = 'awareness-composer-pasting';
    static readonly awarenessCopyingId: AwarenessId = 'awareness-composer-copying';

    readonly id: EditorId;
    readonly editorType: EditorType;

    private readonly _cmdChannel: CmdChannel;
    private readonly _cmdProcessor: ComposerCmdProcessor;
    private readonly _composerGateway: ComposerGateway;

    toolbars: Map<ViewportId, ComposerToolBar> = new Map();

    /**
     * Coordinator related stuff
     * The composer editor itself contains an internal coordinator which it uses to manage the editors
     * it needs to create the inserted documents of other editors
     **/
    composerCoord: ComposerEditorCoordinator;
    selectionFeature: SelectionFeature;
    historyFeature: HistoryFeature;
    robFeature: ROBFeature;
    crdFeature: DocCRDFeature;
    awarenessFeature: AwarenessFeature;
    crudDelegator: CRUDDelegator;
    contentVisibilityFeature: ContentVisibilityCheckFeature;
    attachmentFeature: AttachmentFeature;

    readonly selectDelegator = new SelectDelegator<ComposerDocCtrl>(this, {
        onSelect: this.onSelectDocCtrl.bind(this),
    });

    readonly insertDocDelegator = new InsertDocCtrlDelegator<ComposerDocCtrl, ComposerDoc>(
        this,
        (vp, state) => new ComposerDocCtrl(this, state, vp)
    );

    // TODO: here
    private readonly _operationMode: OperationMode;

    get operationMode(): OperationMode {
        return this._operationMode;
    }

    static readonly cmdChannelThrottle = 300;

    readonly boundaryDelegator = new BoundaryDelegator(this);

    constructor(
        public conf: EditorConfig,
        private _coordinator: EditorCoordinator
    ) {
        super(conf);
        this.id = conf.id;
        this.editorType = conf.editorType;
        this._operationMode = conf.operationMode || OperationMode.CLOUD;

        this._composerGateway = new ComposerGateway(conf.apiUri);

        this._cmdProcessor = new ComposerCmdProcessor(this);
        this._cmdChannel = _coordinator.registerCmdChannel(this.id);
        this._cmdChannel.registerCmdListener(this._cmdProcessor);
        this._cmdChannel.registerCombinator(
            FCCmdTypeProto.PREVIEW_BOUNDARY,
            new ThrottleCombinator(ComposerEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(
            FCCmdTypeProto.UPDATE_BOUNDARY,
            new ThrottleCombinator(ComposerEditor.cmdChannelThrottle)
        );

        this._cmdChannel.registerDeserializer(deserializer);

        const coordConf: ComposerEditorCoordinatorConfig = (conf as ComposerEditorConfig).composerCoordConf;

        this.composerCoord = new ComposerEditorCoordinator(coordConf, this, _coordinator);

        this.crudDelegator = new CRUDDelegator(
            this,
            this.cmdChannel,
            this.regMan,
            { doc: this.docReg, layer: this.layerReg },
            this.generateInitDocData.bind(this)
        );
    }

    // Composer doc is not support local content yet
    getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined {
        return undefined;
    }

    /**
     * Called when a document is selected to change the editable state
     */
    onSelectDocCtrl(docCtrl: ComposerDocCtrl): void {
        const viewPortId = docCtrl.viewport.id;

        const contentTool = this.toolbars.get(viewPortId).getTool('ContentEditorTool') as ContentTool;
        contentTool.checkFocusedDocEditable(viewPortId, docCtrl, this.selectionFeature);

        (docCtrl as ComposerDocCtrl).select();
    }

    /**
     * Supporter of the ContentVisibilityCheckFeature so we can show a border when the document look empty
     */
    isContentVisible(docCtrl: ComposerDocCtrl): boolean {
        // return !docCtrl.composerLib.isContentEmpty();
        throw new Error('isContentVisible not implemented.');
    }

    /**
     * @inheritdoc
     */
    async initialize(): Promise<void> {
        await this.composerCoord.initialize();
    }

    /**
     * @inheritdoc
     */
    async duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>> {
        return await this.composerGateway.duplicateDoc(docGlobalIds);
    }

    /**
     * Supporter of the HistoryFeature to perform undo action
     */
    async undo(item: ComposerHistoryItem) {
        switch (item.type) {
            case 'composer-editing': {
                const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
                const docCtrl = docRegistry.getEntity(item.docId);
                // docCtrl.composerLib.undoManager.undo();
                throw new Error('redo composer-editing not implemented.');
                break;
            }
            case 'subviewport-editing': {
                const subVpHistoryItem = (item as ComposerSubViewportHistoryItem).originalItem;
                await subVpHistoryItem.supporter.undo(subVpHistoryItem);
                break;
            }
            default:
                break;
        }
    }

    /**
     * Supporter of the HistoryFeature to perform redo action
     */
    async redo(item: ComposerHistoryItem) {
        switch (item.type) {
            case 'composer-editing': {
                const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
                const docCtrl = docRegistry.getEntity(item.docId);
                // docCtrl.composerLib.undoManager.redo();
                throw new Error('redo composer-editing not implemented.');
                break;
            }
            case 'subviewport-editing': {
                const subVpHistoryItem = (item as ComposerSubViewportHistoryItem).originalItem;
                await subVpHistoryItem.supporter.redo(subVpHistoryItem);
                break;
            }
            default:
                break;
        }
    }

    async clearHistory(viewportId: ViewportId): Promise<void> {
        this.historyFeature?.clear(this, viewportId);
    }

    /**
     * Supporter for the custom HTML export feature.
     * @returns The selectors for elements to perform custom conversion before export to HTML.
     */
    getCustomHtmlExportSelectors(target: ExportTarget): string[] {
        const selectors = new Set<string>();
        this.composerCoord.editors.forEach(editor => {
            if (editor.isSupportFeature(FEATURE_HTML_EXPORT)) {
                const htmlExportFeature = editor.featureSupporter<SupportHtmlExportFeature>(FEATURE_HTML_EXPORT);
                if (htmlExportFeature) {
                    htmlExportFeature.getCustomHtmlExportSelectors(target).forEach(selector => selectors.add(selector));
                }
            }
        });
        return [...selectors];
    }

    /**
     * Supporter for the custom HTML export feature. Composer itself don't need any custom conversion,
     * but it need to gather the custom conversion functions from child editors in composer coordinator.
     */
    async customHtmlExportConverter(node: HTMLElement, target: ExportTarget): Promise<HTMLElement | null> {
        const convertPromises: Promise<HTMLElement | null>[] = [];
        this.composerCoord.editors.forEach(editor => {
            if (editor.isSupportFeature(FEATURE_HTML_EXPORT)) {
                const htmlExportFeature = editor.featureSupporter<SupportHtmlExportFeature>(FEATURE_HTML_EXPORT);
                if (
                    htmlExportFeature &&
                    typeof htmlExportFeature.customHtmlExportConverter === 'function' &&
                    htmlExportFeature.getCustomHtmlExportSelectors(target).some(selector => node.matches(selector))
                )
                    convertPromises.push(htmlExportFeature.customHtmlExportConverter(node, target));
            }
        });

        return (await Promise.all(convertPromises)).find(Boolean) ?? null;
    }

    /**
     * @inheritdoc
     *
     * Supporter of the RemoveFeature to allow custom removal on the composer document content
     */
    async remove(vpId: ViewportId, isCutting?: boolean): Promise<boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(vpId);
        if (currDocs.length !== 1) return false;

        //         const docCtr = currDocs[0];
        //         docCtr.composerLib.lexical.focus();
        //
        //         return docCtr.composerLib.lexical.getEditorState().read(() => {
        //             const selectionCtx = docCtr.composerLib.contextManager.selectionContext$.value;
        //
        //             // if not selected element, then return false (meaning remove tool take care for document)
        //             if (!selectionCtx) return false;
        //
        //             const nodeKeys = selectionCtx.elementKeys;
        //
        //             // if not selected element inside lexical then return true (meaning lexical take care itself)
        //             if (!nodeKeys || nodeKeys.length !== 1) return false;
        //
        //             const node = $getNodeByKey(nodeKeys[0]);
        //             if ($isSubViewportNode(node)) {
        //                 const nodeEditor = this.composerCoord.editorByType(node.editorType);
        //                 const nodeVmId = node.viewportId;
        //
        //                 let removePromise: Promise<boolean> | undefined = undefined;
        //                 if (nodeEditor.isSupportFeature(FEATURE_REMOVE)) {
        //                     const removeFeature = nodeEditor.featureSupporter<SupportRemoveFeature>(FEATURE_REMOVE);
        //                     if (removeFeature) {
        //                         removePromise = removeFeature.remove(nodeVmId, isCutting);
        //                     }
        //                 }
        //
        //                 (removePromise ?? Promise.resolve(false)).then(handled => {
        //                     if (!handled) docCtr.composerLib.dispatchCommand(DELETE_SUBVIEWPORTS_COMMAND, [nodeVmId]);
        //                 });
        //             } else {
        //                 const selection = $getSelection();
        //                 if ($isRangeSelection(selection) && selection.isCollapsed()) return false;
        //
        //                 docCtr.composerLib.dispatchCommand(DELETE_CHARACTER_COMMAND, true);
        //             }
        //
        //             return true;
        //         });
        return false;
    }

    /**
     * @inheritdoc
     *
     * Supporter of the CopyFeature to allow custom copy on the composer document content
     */
    async copy(sourceVpId: ViewportId): Promise<CopyPasteDTO | boolean> {
        const currDocs = this.selectDelegator.getFocusedDocs(sourceVpId);
        if (currDocs.length !== 1) return undefined;

        //         const docCtr = currDocs[0];
        //
        //         docCtr.composerLib.lexical.focus();
        //         return docCtr.composerLib.lexical.getEditorState().read(() => {
        //             const selection = $getSelection();
        //
        //             // let copy-paste tool handle when selection is empty range
        //             if ($isRangeSelection(selection) && selection.isCollapsed()) return false;
        //
        //             docCtr.composerLib.dispatchCommand(COPY_COMMAND, null);
        //
        //             return true; // `true` to indicate that lexical will handle itself
        //         });

        return false;
    }

    /**
     * @inheritdoc
     *
     * Supporter of the PasteFeature to allow custom paste by API on the composer document content
     */
    async paste(targetVpId: ViewportId, copiedDTO?: CopyPasteDTO): Promise<CopyPasteDTO | boolean> {
        //         const currDocs = this.selectDelegator.getFocusedDocs(targetVpId);
        //         if (currDocs.length !== 1) return false;
        //         const docCtr = currDocs[0];
        //         const selection = docCtr.composerLib.contextManager.selectionContext$.value;
        //
        //         // if not selected element, then return false (meaning copy paste tool take care for document)
        //         if (!selection) return false;
        //
        //         // if focus on doc. let lexical handle itself
        //         const lexicalInst = docCtr.composerLib.lexical;
        //         if (!!copiedDTO && isSubViewportCopyPasteDTO(copiedDTO)) {
        //             lexicalInst.dispatchCommand(PASTE_SUBVIEWPORT_COMMAND, copiedDTO as SubViewportCopyPasteDTO);
        //         } else {
        //             // no DTO -> transfer the items of Clipboard Async API into a fake ClipboardEvent
        //             const items = await navigator.clipboard.read();
        //             if (!items?.length || !items[0].types.length) return false;
        //
        //             const data = new DataTransfer();
        //             const item = items[0];
        //             for (const type of item.types) {
        //                 // ignore any left-over DTO mime as it should be invalid (ex: DTO of Composer doc which can not be handled here)
        //                 if (type === VICLASS_GENERAL_DTO_MIME) continue;
        //                 const dataString = await (await item.getType(type)).text(); // fast + small size -> no need Promise.all
        //                 data.setData(type, dataString);
        //             }
        //
        //             if (data.types.length === 0) return false;
        //
        //             const prevSelection = lexicalInst.read(() => $getSelection());
        //             lexicalInst.focus(() => {
        //                 lexicalInst.update(() => {
        //                     // try restore prev selection as it will jump to the end of doc after focus
        //                     if (prevSelection) $setSelection(prevSelection.clone());
        //
        //                     lexicalInst.dispatchCommand(PASTE_COMMAND, new ClipboardEvent('paste', { clipboardData: data }));
        //                 });
        //             });
        //         }
        //
        //         return true;
        return false;
    }

    /**
     * remove document internally, meaning just remove doc without sync cmd
     *
     * @param vpId
     * @param docId
     */
    async internalRemoveDoc(vpId: ViewportId, docId: DocLocalId) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vpId);
        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vpId, docId);
        const docCtrl = docRegistry.getEntity(docId);
        if (docCtrl) {
            // because the deselect method doesn't wait for blur, cannot use it directly
            await this.selectDelegator.doBlurDocCtrl(docCtrl);
            const context: SelectContext = {
                doc: docCtrl,
                supporter: this.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION),
                selectDetails: {},
            };

            this.selectionFeature.onDeselect(context);
            docRegistry.removeEntity(docId);
            layerRegistry.removeEntity(docCtrl.layer.state.id);
            docCtrl.onRemove();
        }
    }

    get coordinator(): EditorCoordinator {
        return this._coordinator;
    }

    get cmdChannel(): CmdChannel {
        return this._cmdChannel;
    }

    get composerGateway(): ComposerGateway {
        return this._composerGateway;
    }

    async sendCommand(cmd: Cmd<any>): Promise<void> {
        await this._cmdChannel.receive(cmd);
    }

    /**
     * @inheritdoc
     */
    async loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByGlobalId(
            this._cmdChannel.channelCode,
            globalId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext);
    }

    /**
     * @inheritdoc
     */
    async loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByLocalId(
            this._cmdChannel.channelCode,
            localId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext, localId);
    }

    /**
     * @inheritdoc
     */
    async reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        await this.loadDocumentByLocalId(localId, loadingContext);
    }

    /**
     * @inheritdoc
     */
    getDocumentContentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext): Promise<FetchDocResponse> {
        return loadingContext.connector.loadDocumentByGlobalId(this._cmdChannel.channelCode, globalId, 'json');
    }

    /**
     * Create a new document controller from the load doc response data
     */
    private async createDocumentCtrlFromResponseData(
        response: FetchDocResponse,
        loadingContext: LoadingContext,
        localId?: DocLocalId
    ): Promise<ComposerDocCtrl> {
        const docRegistry = this.regDelegator.getOrCreateDocReg(loadingContext.vm.id);

        // in scenarios where documents are not loaded by localId but only
        // global id is provided, we generate a local id for usage; it doesn't matter anyway
        if (!localId) localId = docRegistry.getAndIncrementId();

        const doc = new ComposerDoc(localId, response.id, response.viewportElClass, response.content, response.version);

        const layerId = 1; // only one layer per document, so layer id is always 1

        const state = new ComposerLayer(layerId, localId); // initially, the layer state doesn't have a boundary

        /**
         * Request the layer used by the document controller from the viewport
         * Provide what we know about the layer. The actual positioning of the layer
         * on the board is determined by the viewport.
         *
         * In the case of the classroom, the viewport manager will get the position from the
         * doc mapping, in another case, the layer will be created in a default position with
         * the provided width / height
         */
        state.zindex = loadingContext.zIndexes[0];
        const layerCtrl = loadingContext.vm.requestLayer(DOMElementLayerCtrl, true, {
            docLocalId: localId,
            docGlobalId: response.id,
            viewport: loadingContext.vm,
            editor: this,
            state: state,
            domElType: 'div',
        }) as DOMElementLayerCtrl;

        state.boundary = layerCtrl.boundary;

        // let's start creating the doc controller
        const docCtrl = new ComposerDocCtrl(this, doc, loadingContext.vm, loadingContext);

        docRegistry.addEntity(doc.id, docCtrl);

        // link the layer with the doc controller
        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);

        docCtrl.setContent(response.content, response.settingJSON);

        const layerRegistry = this.regDelegator.getOrCreateLayerReg(loadingContext.vm.id, localId);
        layerRegistry.addEntity(1, layerCtrl);
        loadingContext.vm.addLayer(layerCtrl);

        return docCtrl;
    }

    /**
     * @inheritdoc
     */
    async start() {
        await this._cmdProcessor.start();
        await this._cmdChannel.start();
        await this.composerCoord.start();
    }

    /**
     * @inheritdoc
     */
    featureSupporter<T>(featureKey: string): T {
        if (
            [
                FEATURE_HISTORY,
                FEATURE_COPYPASTE,
                FEATURE_REMOVE,
                FEATURE_CONTENT_VISIBILITY,
                FEATURE_AWARENESS,
                FEATURE_ATTACHMENT, // no need to actually implement supporter on this yet
                FEATURE_HTML_EXPORT,
            ].includes(featureKey)
        )
            return this as unknown as T;

        if (featureKey == FEATURE_ROB) return this.robFeature as T;
        if (featureKey == FEATURE_CRUD) return this.crudDelegator as T;
        if (featureKey == FEATURE_SELECTION) return this.selectDelegator as T;

        throw new Error(`Composer Editor doesn't support feature ${featureKey}`);
    }

    /**
     * @inheritdoc
     */
    isSupportFeature(featureKey: string): boolean {
        return [
            FEATURE_SELECTION,
            FEATURE_ROB,
            FEATURE_HISTORY,
            FEATURE_CRD_DOC,
            FEATURE_CRUD,
            FEATURE_COPYPASTE,
            FEATURE_REMOVE,
            FEATURE_CONTENT_VISIBILITY,
            FEATURE_AWARENESS,
            FEATURE_ATTACHMENT,
            FEATURE_HTML_EXPORT,
        ].includes(featureKey);
    }

    /**
     * @inheritdoc
     */
    onFeatureInitialization(featureKey: string, feature: any): Promise<void> {
        switch (featureKey) {
            case FEATURE_SELECTION: {
                this.selectionFeature = feature as SelectionFeature;
                this.boundaryDelegator.setSelectionFeature(this.selectionFeature);
                break;
            }
            case FEATURE_HISTORY: {
                this.historyFeature = feature as HistoryFeature;
                break;
            }
            case FEATURE_ROB: {
                this.robFeature = feature as ROBFeature;
                break;
            }
            case FEATURE_CRD_DOC: {
                this.crdFeature = feature as DocCRDFeature;
                this.crdFeature.registerEditor(this, this.cmdChannel);
                break;
            }
            case FEATURE_CONTENT_VISIBILITY: {
                this.contentVisibilityFeature = feature as ContentVisibilityCheckFeature;
                break;
            }
            case FEATURE_AWARENESS: {
                this.awarenessFeature = feature as AwarenessFeature;
                break;
            }
            case FEATURE_ATTACHMENT: {
                this.attachmentFeature = feature as AttachmentFeature;
                break;
            }
            default:
                break;
        }
        return Promise.resolve();
    }

    /**
     * Create a delegator to be used in the create doc tool on attach viewport
     */
    initCreateDocTool(viewport: ViewportManager) {
        const createToolType: ComposerToolType = 'CreateComposerDocumentTool';
        return this.crdFeature.generateCRDToolForViewport(
            viewport.id,
            createToolType,
            this,
            this.cmdChannel,
            this.robFeature,
            this.selectionFeature
        );
    }

    /**
     * @inheritdoc
     */
    createToolbar(): ToolBar<any, any> {
        const tb = new ComposerToolBar(this);

        // floating ui is only available for a bounded document
        if (this.conf.docViewMode == 'bounded')
            tb.addTool('CreateComposerDocumentTool', new ComposerCreateDocumentTool(this));
        tb.addTool('InsertionEditorTool', new DocInsertTool(this));
        tb.addTool('SubEditorManagerTool', new SubEditorManagerTool(this, this.composerCoord));
        tb.addTool('ComposerSettingsTool', new ComposerSettingsTool(this));

        return tb;
    }

    /**
     * To be called from the CRD delegator to init the document
     */
    async generateInitDocData(curChanges: CRUDChangeResult, insertDoc: FCInsertDocCmd, insertLayer: FCInsertLayerCmd) {
        const response = await this.composerGateway.createDoc();
        const initData: ComposerDocInitData = {
            viewportElClass: response.viewportElClass,
            content: response.content,
        };

        const encoder = new TextEncoder();
        const bytes = encoder.encode(JSON.stringify(initData));
        insertDoc.state.setInitdata(bytes);
        insertDoc.state.setGlobalId(response.id);
    }

    /**
     * Util function to add history item to the history manager
     */
    addHistoryItem(item: ComposerHistoryItem) {
        if (!this.historyFeature) return; // if the history feature is not initialized, we simply ignore
        const manager = this.historyFeature.getHistoryManager(item.viewportId);

        manager.push(item);
    }

    /**
     * Notify the content visibility feature about a content visibility possible change
     */
    notifyContentVisibilityChange(docCtrl: ComposerDocCtrl) {
        this.contentVisibilityFeature?.onContentVisibilityChange(docCtrl);
    }
}
