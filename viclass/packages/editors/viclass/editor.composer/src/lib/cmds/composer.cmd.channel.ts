import {
    Cmd,
    CmdChannel,
    CmdCombinator,
    CmdGateway,
    CmdMeta,
    CmdOriginType,
    CmdProcessor,
    CompletableData,
    DefaultEventEmitter,
    VEventListener,
} from '@viclass/editor.core';
import { Subject } from 'rxjs';

// a command channel used by the editors within the composer editor
// currently copy from default cmd channel
export class ComposerCmdChannel implements CmdChannel {
    private _cmdSrc: DefaultEventEmitter<Cmd<any>> = new DefaultEventEmitter();
    private allowEmission: boolean = false;

    private cmdQueue: CompletableData<Cmd<any>>[] = [];
    private sendingExternal: boolean;

    private combinators: { [key: number]: CmdCombinator } = {};
    private curCombinator: Map<number, CmdCombinator> = new Map<number, CmdCombinator>();

    // store cmd in sequence by viewport
    private vpCmdQueue: Map<string, Cmd<any>[]> = new Map<string, Cmd<any>[]>();

    // store viewports that need to combine first of all
    private priorityVPs: { id: string; job: Subject<void>; complete() }[] = [];

    // store current combine viewport
    private curCombineVP?: string = undefined;

    // Lưu cmd mà thực sự sẽ được gửi đi sau khi đã được combined và process trong hook
    private cmdActuallySendingQueue: Cmd<any>[] = [];

    deserializer: (meta: CmdMeta, stateData: Uint8Array) => Cmd<any>;

    constructor(
        private _channelCode: number,
        private gateway: CmdGateway
    ) {
        this.cmdSource.onBeforeListenerAdded = listener => {
            const cmdProcessor = listener as CmdProcessor;
            cmdProcessor.onAttached(this);
        };
    }

    async start() {
        this.allowEmission = true;

        await this.sendBeforeStartBuffer();

        await this.actuallySend();
    }

    private async sendBeforeStartBuffer() {
        if (this.cmdQueue.length > 0) {
            const cmd = this.cmdQueue.shift();
            await this.emitCmd(cmd);
            await this.sendBeforeStartBuffer();
        }
    }

    async pause() {
        this.allowEmission = false;
    }

    deserialize(meta: CmdMeta, stateData: Uint8Array): Cmd<any> {
        return this.deserializer.call(null, meta, stateData);
    }

    registerDeserializer(deserializer: (meta: CmdMeta, stateData: Uint8Array) => Cmd<any>) {
        this.deserializer = deserializer;
    }

    registerCombinator<CmdType>(cmdType: CmdType, combinator: CmdCombinator) {
        if (typeof cmdType !== 'number') throw new Error('Cmd Type must be a number');

        this.combinators[cmdType] = combinator;
    }

    get channelCode(): number {
        return this._channelCode;
    }

    registerCmdListener(listener: VEventListener<Cmd<any>>) {
        this.cmdSource.registerListener(listener);
    }

    get cmdSource(): DefaultEventEmitter<Cmd<any>> {
        return this._cmdSrc;
    }

    receive(cmd: CompletableData<Cmd<any>> | Cmd<any>): Promise<any> {
        if (this.allowEmission && this.cmdQueue.length == 0) {
            return this.emitCmd(cmd);
        } else {
            if (cmd instanceof CompletableData) {
                this.cmdQueue.push(cmd);
                return cmd.promise;
            } else {
                const c = new CompletableData(cmd);
                this.cmdQueue.push(c);
                return c.promise;
            }
        }
    }

    emitCmd(cmdOrCompletableData: Cmd<any> | CompletableData<Cmd<any>>): Promise<any> {
        const promise =
            cmdOrCompletableData instanceof CompletableData
                ? this.cmdSource.emitCompletable(cmdOrCompletableData)
                : this.cmdSource.emit(cmdOrCompletableData);
        const cmd = cmdOrCompletableData instanceof CompletableData ? cmdOrCompletableData.data : cmdOrCompletableData;

        if (cmd.meta.origin == CmdOriginType.local) {
            const microTaskSendExternalF = () => queueMicrotask(() => this.sendExternal(cmd));
            if (cmd.meta.waitLocalProcessingBeforeSync) promise.then(microTaskSendExternalF);
            else microTaskSendExternalF();
        }

        return promise;
    }

    sendExternal(cmd: Cmd<any>) {
        const cmdVp = cmd.meta.viewport;
        const cmdSequence = this.vpCmdQueue.get(cmdVp.id);
        if (cmdSequence) cmdSequence.push(cmd);
        else this.vpCmdQueue.set(cmdVp.id, [cmd]);

        if (!this.sendingExternal) {
            this.sendingExternal = true;
            setTimeout(() => this.executeSend());
        }
    }

    /**
     * Poll next cmd that need to combine.
     * prioritize cmd of the viewport that in the priorityVPs first
     */
    pollCmd(): Cmd<any> | undefined {
        // try to poll cmd that in the priority viewport first
        if (this.priorityVPs.length > 0 && this.curCombineVP != this.priorityVPs[0].id) {
            this.curCombineVP = this.priorityVPs[0].id;
            return this.pollCmd();
        }

        // find next viewport that need to combine, just get the next viewport in the map
        if (!this.curCombineVP) {
            if (this.vpCmdQueue.size > 0) {
                this.curCombineVP = this.vpCmdQueue.keys().next().value;
                return this.pollCmd();
            }
            return undefined;
        }

        // polling cmd in the combining viewport
        const vpCmd = this.vpCmdQueue.get(this.curCombineVP);
        if (vpCmd?.length > 0) {
            return vpCmd.shift();
        }

        //==== BELOW FOR HANDLING WHEN THERE IS NO CMD IN THE COMBINING VIEWPORT ANYMORE ===

        // if there is priority viewport, and it is the current combining, remove it from priority viewport list
        if (this.priorityVPs.length > 0 && this.priorityVPs[0].id == this.curCombineVP) {
            this.priorityVPs.shift().complete();
        }

        // remove current combining viewport, then reset state, so we can begin to handle combine the next viewport
        this.vpCmdQueue.delete(this.curCombineVP);
        this.curCombineVP = undefined;

        return this.pollCmd();
    }

    private combineBeforeSend(cmd: Cmd<any>) {
        const preVp = this.curCombineVP;
        const c = this.combinators[cmd.meta.cmdType];
        const curCombinatorKey = cmd.meta.isolateCombinator ? cmd.meta.cmdType : 0;

        let curCombinator = this.curCombinator.get(curCombinatorKey);
        const setCurCombinator = (combinator: CmdCombinator) => {
            this.curCombinator.set(curCombinatorKey, combinator);
            curCombinator = combinator;
        };

        cmd.meta.channelCode = this._channelCode;

        if (c) {
            // if this type of command needs a combinator
            if (curCombinator) {
                // if already using a combinator
                if (curCombinator == c && (!preVp || cmd.meta.viewport.id == preVp)) {
                    // if the currently used combinator and the viewport are the same as the one needs to be used, then continue using
                    curCombinator.combine(cmd);
                    setTimeout(() => this.executeSend());
                } else {
                    // if not the same as the one to be used, stop the current one first, flush all the commands to be combined,
                    // then when that complete, use the new one to combine
                    // OR WHEN VIEWPORT IS SWITCHED, need to end previous sequence
                    curCombinator.endSequenceAndFlush().finally(() => {
                        setCurCombinator(c);
                        // start sequence when switch viewport
                        curCombinator.startSequence(combined => {
                            if (!combined.meta.notSync) this.addCmdActuallySend(combined);
                        });
                        curCombinator.combine(cmd);
                        setTimeout(() => this.executeSend());
                    });
                }
            } else {
                // if not, simply use the one needs to be used
                setCurCombinator(c);
                curCombinator.startSequence(combined => {
                    if (!combined.meta.notSync) this.addCmdActuallySend(combined);
                });
                curCombinator.combine(cmd);
                setTimeout(() => this.executeSend());
            }
        } else {
            if (curCombinator) {
                curCombinator.endSequenceAndFlush().finally(() => {
                    setCurCombinator(null);
                    if (!cmd.meta.notSync) this.addCmdActuallySend(cmd);
                    setTimeout(() => this.executeSend());
                });
            } else {
                if (!cmd.meta.notSync) this.addCmdActuallySend(cmd);
                setTimeout(() => this.executeSend());
            }
        }
    }

    private addCmdActuallySend(cmd: Cmd<any>) {
        this.cmdActuallySendingQueue.push(cmd);
    }

    private async actuallySend() {
        if (this.cmdActuallySendingQueue.length == 0) {
            setTimeout(() => this.actuallySend(), 100);
            return;
        }
        const cmd = this.cmdActuallySendingQueue.shift();
        await this.processSyncHook(cmd);
        this.gateway.sync(cmd);
        await this.actuallySend();
    }

    private async processSyncHook(cmd: Cmd<any>) {
        const hookResult = this.processPreSyncHook(cmd);
        if (hookResult && hookResult instanceof Promise) {
            return await hookResult;
        }
    }

    private processPreSyncHook(cmd: Cmd<any>): void | Promise<any> {
        if (cmd.meta.preSyncHooks) {
            const promises: Promise<any>[] = cmd.meta.preSyncHooks.map(h => h(cmd)).filter(v => v instanceof Promise);
            if (promises.length > 0) return Promise.all(promises);
        }
    }

    executeSend(): void {
        const cmd = this.pollCmd();

        if (!cmd) {
            // stop execute sending cmd when there is no viewport need to execute cmd
            this.sendingExternal = false;
            return;
        }

        this.combineBeforeSend(cmd);
    }

    async flush(vpId: string): Promise<void> {
        if (!this.vpCmdQueue.has(vpId)) {
            return Promise.resolve();
        }

        const priorityVP = {
            id: vpId,
            job: new Subject<void>(),
            complete() {
                this.job.complete();
            },
        };
        this.priorityVPs.push(priorityVP);
        await priorityVP.job.toPromise();
    }
}
