import { DocumentId } from '@viclass/editor.core';
import axios from 'axios';
import { FetchDocResponse } from './model';

export class ComposerGateway {
    constructor(private backendUri: string) {}

    async createDoc(): Promise<FetchDocResponse> {
        const resp = await axios.post<FetchDocResponse>(
            this.url('/document/create'),
            {},
            {
                withCredentials: true,
                headers: {
                    'Content-Type': 'application/json',
                },
            }
        );

        return resp.data;
    }

    private url(path: string): string {
        return this.backendUri + path;
    }

    async duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<string, string>> {
        const resp = await axios.post(this.url(`/documents/duplicate`), docGlobalIds, {
            withCredentials: true,
            headers: {
                'Content-Type': 'application/json',
            },
            responseType: 'json',
        });

        return new Map(Object.entries(resp.data.mapping));
    }
}
