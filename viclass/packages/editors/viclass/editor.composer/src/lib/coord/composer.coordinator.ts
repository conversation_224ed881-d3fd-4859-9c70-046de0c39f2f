import {
    BaseCoordinator,
    DirectBackendDataSaver,
    WrappingBoardViewportManager,
    WrappingInlineViewportManager,
} from '@viclass/editor.coordinator/common';
import {
    AwarenessFeature,
    AwarenessTool,
    BaseBoardViewportManager,
    BaseViewportManager,
    CmdGateway,
    CommonToolbar,
    CRUDChangeResult,
    CRUDLocalContentChangeListener,
    CRUDRemoveDocChangeListener,
    DefaultToolBar,
    DefaultViewportEventManager,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocPrepInfo,
    DocumentEditor,
    DocumentId,
    EditorBackendConnector,
    EditorBlurCES,
    EditorCoordinator,
    EditorEventManager,
    EditorFocusCES,
    EditorLookup,
    EditorType,
    HistoryFeature,
    LayerPrepInfo,
    LocalContentUpdateResult,
    NewDocChangeResult,
    PanEventData,
    PanTool,
    RemoveTool,
    ROBFeature,
    SelectionFeature,
    SelectTool,
    ToolBar,
    VEventListener,
    ViewportFocusInCES,
    ViewportFocusOutCES,
    ViewportId,
    ViewportManager,
    ViewportMode,
    ZoomEventData,
    ZoomTool,
} from '@viclass/editor.core';
import { ComposerWrappingCmdGateway } from '../cmds/composer.wrapping.cmd.gateway';
import { ComposerEditor } from '../composer.editor';
import { ComposerEditorCoordinatorConfig } from '../config';
import { ComposerDocCtrl } from '../docs/composer.doc.ctrl';
import { ComposerCoordBackendConnector } from './coord.backend.connector';
import { ComposerForwardHistoryFeature } from './forward.history.feature';

/**
 * The composer editor coordinator is responsible for creating and managing internal editor
 */
export class ComposerEditorCoordinator extends BaseCoordinator {
    readonly vms: Map<Element, ViewportManager> = new Map();
    readonly vmsById: Map<ViewportId, ViewportManager> = new Map();

    readonly curFocusVmByDoc: Map<DocumentId, ViewportManager> = new Map();
    /**
     * A Map of each ViewportId with its corresponding event listener for handling zoom and pan events.
     */
    readonly curZoomPanListenerBySubVmId: Map<ViewportId, VEventListener<ZoomEventData | PanEventData>> = new Map();

    private _commonToolbar: CommonToolbar;

    /**
     * Each viewport created by this coordinator must be long to a document
     * We store reference to the document so that when a command from a sub editor
     * is sent, we know which doc it comes from. Using the globalId we can do routing
     * on the receiving side
     */
    readonly containingDoc: Map<ViewportId, ComposerDocCtrl> = new Map();

    readonly robFeature: ROBFeature;
    readonly selectionFeature: SelectionFeature;
    readonly historyFeature: HistoryFeature;
    readonly crdFeature: DocCRDFeature;
    readonly awarenessFeature: AwarenessFeature;
    readonly em: EditorEventManager;

    declare _cmdGateway: CmdGateway;
    private _dataSaver: DirectBackendDataSaver;

    private onLocalContentChange: CRUDLocalContentChangeListener;
    private onDocRemoved: CRUDRemoveDocChangeListener;

    constructor(
        public override readonly conf: ComposerEditorCoordinatorConfig,
        private readonly weditor: ComposerEditor,
        public readonly parent: EditorCoordinator
    ) {
        super(conf);

        this.selectionFeature = new SelectionFeature(this);
        this.robFeature = new ROBFeature(this);
        this.historyFeature = new ComposerForwardHistoryFeature(this, this.weditor);
        this.crdFeature = new DocCRDFeature(this, {
            historyFeature: this.historyFeature,
            loadingCtxGenerator: this.loadingCtxGenerator,
            localContentGetter: this.localContentGetter,
        });
        this.awarenessFeature = new AwarenessFeature(this);

        this.em = new DefaultViewportEventManager(this);
        this._dataSaver = new DirectBackendDataSaver(
            new Map(this.conf.edLookups.map(v => [v.editorType, v.settings['apiUri']])),
            this.conf.editorTypeMapping
        );
        this._cmdGateway = new ComposerWrappingCmdGateway(this.weditor.cmdChannel, this, this._dataSaver);

        this.onLocalContentChange = this._onLocalContentChanges.bind(this);
        this.onDocRemoved = this._onDocRemoved.bind(this);
    }

    loadingCtxGenerator = (editor: DocumentEditor, vmId: ViewportId): EditorBackendConnector => {
        const vm = this.getViewportManager(vmId);
        return new ComposerCoordBackendConnector(
            this.conf.edLookups.find(v => v.editorType == editor.editorType).settings['apiUri'],
            this.getGlobalId(vm),
            1
        );
    };

    localContentGetter = (editor: DocumentEditor, vmId: ViewportId, localId: DocLocalId) => {
        const vm = this.getViewportManager(vmId);
        const docCtrl = this.getContainingDocFromChildViewport(vm);
        // return docCtrl?.composerLib?.getSubViewportLocalContent(vmId);
        throw 'Not implemented get local content';
    };

    createToolbar(): ToolBar<any, any> {
        const tb = new CommonToolbar(this);
        const selectTool = new SelectTool(this, this.selectionFeature, this.robFeature, this.crdFeature, false, true);
        tb.addTool('zoom', new ZoomTool(true, false));
        tb.addTool('pan', new PanTool(true, false));
        tb.addTool(
            'remove',
            new RemoveTool(this, this.crdFeature, this.selectionFeature, undefined, this.awarenessFeature)
                .allowDelHotkey(true)
                .enableDelDoc(true)
        );
        tb.addTool('select', selectTool);
        tb.addTool('awareness', new AwarenessTool(this.awarenessFeature));

        this._commonToolbar = tb;

        return tb;
    }

    override getCommonToolbar(viewportId: ViewportId): CommonToolbar {
        return this.commonToolbar;
    }

    override getEditorToolbar(viewportId: ViewportId, editorType: EditorType): DefaultToolBar<any, any> {
        return undefined;
    }

    get commonToolbar(): CommonToolbar {
        return this._commonToolbar;
    }

    getViewportManager(viewportId: ViewportId): ViewportManager {
        return this.vmsById.get(viewportId);
    }

    async initialize(): Promise<void> {
        const promises: Promise<any>[] = [];

        for (const edType of Object.keys(this.conf.editorTypeMapping)) {
            const edLookup: EditorLookup = this.conf.edLookups.find(e => e.editorType == edType) as EditorLookup;
            if (!edLookup) return Promise.reject(Error(`Not found editor lookup for ${edType}`));

            const promise = this.addEditor(edLookup);
            promises.push(promise);
        }

        await this._cmdGateway.initialize();

        await Promise.all(promises);
    }

    override async addEditor(lookup: EditorLookup) {
        const result = await super.addEditor(lookup);

        this._dataSaver.addBkEnd(lookup.editorType, lookup.settings['apiUri']);

        return result;
    }

    // create a viewport and attach an event manager
    createViewport(docCtrl: ComposerDocCtrl, container: HTMLElement, edType: EditorType): ViewportManager {
        const vpConfForEd = this.conf.viewport[edType];

        if (vpConfForEd == null) throw new Error(`No viewport configuration found for editor type ${edType}`);

        let vm: ViewportManager;

        switch (vpConfForEd.vpType) {
            case 'board': {
                vm = new WrappingBoardViewportManager(container);
                vm.parentViewport = docCtrl.viewport;

                setTimeout(() => {
                    // initialize viewport view state
                    const lookAtX = parseFloat(container.dataset['lookAtX']);
                    const lookAtY = parseFloat(container.dataset['lookAtY']);
                    const zoom = parseFloat(container.dataset['zoom']);

                    if (lookAtX && lookAtY)
                        (vm as BaseBoardViewportManager).centerAt({
                            x: lookAtX,
                            y: lookAtY,
                        });
                    if (zoom) (vm as BaseBoardViewportManager).zoom(zoom);
                });
                // toolbar is created when the toolbar of the editor is created
                break;
            }
            case 'inline': {
                vm = new WrappingInlineViewportManager(container);
                vm.parentViewport = docCtrl.viewport;
                break;
            }
            default:
                break;
        }

        this.vms.set(container, vm);
        this.vmsById.set(vm.id, vm);
        this.containingDoc.set(vm.id, docCtrl);

        this.em.attachViewport(vm);
        this._cmdGateway.registerViewport(vm.id);
        this.crdFeature.registerCRDListener(vm.id, this.onDocRemoved, 'remove-doc');
        this.crdFeature.registerCRDListener(vm.id, this.onLocalContentChange, 'local-content');

        (vm as BaseViewportManager).eventManager = this.em;

        return vm;
    }

    async removeViewport(vpId: ViewportId): Promise<void> {
        const vm = this.getViewportManager(vpId);
        if (!vm) return;

        // clean up events
        this.em.detachViewport(vm);
        this._cmdGateway.unregisterViewport(vm.id);
        this.crdFeature.unregisterCRDListener(vm.id, this.onDocRemoved, 'remove-doc');
        this.crdFeature.unregisterCRDListener(vm.id, this.onLocalContentChange, 'local-content');

        const docCtrl = this.containingDoc.get(vm.id);
        if (docCtrl) {
            const removeTool = this.commonToolbar.getTool('remove') as RemoveTool;
            const edType = this.getEditorTypeFromSubVm(vm);
            const editor = this.editorByType(edType);
            const doc = editor.findDocumentByLocalId(vm.id, this.getLocalId(vm));
            // no history item for remove child viewport, SubViewportPlugin will handle it
            await removeTool.removeDoc(vm.id, editor, doc.state, false);
        }

        // unfocus the viewport and clean focus state
        let focusedDocId: DocumentId | undefined = undefined;
        for (const [docId, docVm] of this.curFocusVmByDoc.entries()) {
            if (docVm.id === vpId) {
                focusedDocId = docId;
                break;
            }
        }

        if (focusedDocId) {
            await this.unfocusViewport(vm);
            this.curFocusVmByDoc.delete(focusedDocId);
        }

        // Clean local state.
        this.vmsById.delete(vpId);
        this.vms.delete(vm.rootEl as HTMLElement);
        this.containingDoc.delete(vpId);
        await vm.destroy();

        // emit event for the editors to handle
        await this.coordEventEmitter.emit({
            eventType: 'viewport-removed',
            source: this,
            state: { vmId: vpId },
        });
    }

    getContainingDocFromChildViewport(childVM: ViewportManager) {
        return this.containingDoc.get(childVM.id);
    }

    /**
     * Get global id of the document inside an internal viewport
     * @param vm
     */
    getGlobalId(vm: ViewportManager) {
        return vm.rootEl.dataset['globalId'];
    }

    /**
     * Get local id of the document inside an internal viewport
     * @param vm
     */
    getLocalId(vm: ViewportManager) {
        return parseInt(vm.rootEl.dataset['localId']);
    }

    getEditorTypeFromSubVm(vm: ViewportManager) {
        return vm.rootEl.dataset['edType'] as EditorType;
    }

    /**
     * Get parent viewport of the document inside an internal viewport, meaning the viewport of composer document
     * @param vm
     */
    getParentViewport(vm: ViewportManager) {
        return (vm as BaseBoardViewportManager).parentViewport;
    }

    async createDocumentInViewport(viewportId: ViewportId, edType: EditorType) {
        const editor = this.editorByType(edType);

        const globalIdAvailableHook = async (changes: NewDocChangeResult[]) => {
            if (changes[0].expectedChanges[0].globalId) {
                const docCtrl = this.containingDoc.get(viewportId);
                docCtrl.updateInternalDocMapping(
                    viewportId,
                    changes[0].expectedChanges[0].globalId,
                    changes[0].expectedChanges[0].localId
                );
            }

            if (changes[0].editor.operationMode !== 'CLOUD') {
                // update local content state of the initialized doc
                const toBeUpdated: Map<EditorType, DocPrepInfo[]> = new Map();
                toBeUpdated.set(
                    changes[0].editor.editorType,
                    changes[0].expectedChanges.map(ec => ({
                        localId: ec.localId,
                        globalId: ec.globalId,
                    }))
                );

                const vm = this.getViewportManager(changes[0].vmId);
                this.crdFeature.onUpdateLocalContent(toBeUpdated, vm, false);
            }
        };
        // check embed coordinator to see why we need this
        this.crdFeature.registerCRDListener(viewportId, globalIdAvailableHook, 'new-doc');

        const result = await this.crdFeature.createDocument(
            editor,
            {
                vm: this.getViewportManager(viewportId),
            },
            true,
            false // no history, let composer handle it otherwise you will need to redo 2 times to remove the doc
        );

        this.crdFeature.unregisterCRDListener(viewportId, globalIdAvailableHook, 'new-doc');
        return result;
    }

    async insertDocumentInViewport(
        globalId: DocumentId,
        localId: DocLocalId,
        edType: EditorType,
        vm: ViewportManager,
        localContent?: DocLocalContent
    ) {
        const toBeInserted: Map<EditorType, { ids: DocPrepInfo[]; layers: (LayerPrepInfo[] | undefined)[] }> =
            new Map();

        toBeInserted.set(edType, {
            ids: [
                {
                    localId: localId,
                    globalId: globalId,
                    localContent: localContent,
                },
            ],
            layers: [],
        });

        const globalIdAvailableHook = async (changes: NewDocChangeResult[]) => {
            vm.rootEl.setAttribute('data-global-id', changes?.[0]?.expectedChanges?.[0]?.globalId);
        };
        this.crdFeature.registerCRDListener(vm.id, globalIdAvailableHook, 'new-doc');

        const result = await this.crdFeature.insertDocument(toBeInserted, vm, false); // no history, let composer handle it

        this.crdFeature.unregisterCRDListener(vm.id, globalIdAvailableHook, 'new-doc');
        return result;
    }

    async loadDocumentInViewport(globalId: DocumentId, localId: DocLocalId, edType: EditorType, vm: ViewportManager) {
        if (!globalId || isNaN(localId)) return;
        const ed = this.editorByType(edType);
        await ed.loadDocumentByLocalId(localId, {
            vm: vm,
            connector: new ComposerCoordBackendConnector(
                this.conf.edLookups.find(v => v.editorType == edType).settings['apiUri'],
                globalId,
                localId
            ),
            zIndexes: [],
        });
    }

    async loadDocumentFromLocalContent(
        vm: ViewportManager,
        localId: DocLocalId,
        edType: EditorType,
        localContent: DocLocalContent
    ) {
        if (isNaN(localId)) return undefined;
        const ed = this.editorByType(edType);
        return await ed.loadDocumentByLocalContent(vm, localId, localContent);
    }

    compareAndUpdateLocalContent(
        edType: EditorType,
        vm: ViewportManager,
        localId: number,
        localContent: DocLocalContent
    ) {
        if (!localContent || !vm) return;

        const editor = this.editorByType(edType);
        const docCtrl = editor.findDocumentByLocalId(vm.id, localId);
        if (!docCtrl) return;

        const curLocalContent = editor.getLocalContent(vm.id, localId);
        if (!curLocalContent || localContent.version > curLocalContent.version) {
            const channel = this._cmdGateway.registerChannel(editor.id);
            try {
                channel.pause();
                docCtrl.updateByLocalContent(localContent);
            } catch (e) {
                console.error(e);
            } finally {
                channel.start();
            }
        }
    }

    async duplicateDocuments(docGlobalIds: DocumentId[], edType: EditorType): Promise<Map<DocumentId, DocumentId>> {
        if (!docGlobalIds?.length) return new Map();

        const editor = this.editorByType(edType);
        return await editor.duplicateDoc(docGlobalIds);
    }

    async focusEditor(edType: EditorType, vm: ViewportManager) {
        const e: EditorFocusCES = {
            vmId: vm.id,
            editor: this.editorByType(edType),
        };
        await this.coordEventEmitter.emit({
            eventType: 'editor-focus',
            source: this,
            state: e,
        });
    }

    async blurEditor(edType: EditorType, vm: ViewportManager) {
        const e: EditorBlurCES = {
            vmId: vm.id,
            editor: this.editorByType(edType),
        };
        await this.coordEventEmitter.emit({
            eventType: 'editor-blur',
            source: this,
            state: e,
        });
    }

    async unfocusViewport(vm: ViewportManager): Promise<void> {
        this.switchViewportMode(vm.id, 'ViewMode');
        const eS: ViewportFocusOutCES = {
            vmId: vm.id,
        };

        await this.coordEventEmitter.emit({
            eventType: 'viewport-focusout',
            source: this,
            state: eS,
        });

        if (vm instanceof BaseBoardViewportManager) {
            const l = this.curZoomPanListenerBySubVmId.get(vm.id);

            if (l) {
                vm.zoomEventEmitter().unregisterListener(l as VEventListener<ZoomEventData>);
                vm.panEventEmitter().unregisterListener(l as VEventListener<PanEventData>);
                this.curZoomPanListenerBySubVmId.delete(vm.id);
            } else {
                // throw new Error(`Inconsistency detected. A previously focused viewport didn't have any zoom pan listener attached.`)
            }
        }
        vm.blur();
    }

    async focusViewport(ctrl: ComposerDocCtrl, vm: ViewportManager): Promise<void> {
        const curVm = this.curFocusVmByDoc.get(ctrl.state.globalId);

        if (curVm == vm) return;

        if (curVm) await this.unfocusViewport(curVm);

        const eS: ViewportFocusInCES = {
            vmId: vm.id,
        };

        await this.coordEventEmitter.emit({
            eventType: 'viewport-focusin',
            source: this,
            state: eS,
        });

        this.curFocusVmByDoc.set(ctrl.state.globalId, vm);

        // must switch after viewport-focusin event as only then the viewport is attached to toolbar
        // TODO: here, implement chosing edit mode for sub-viewport
        const vpMode: ViewportMode = 'EditMode'; // ctrl.composerLib.isEditable() ? 'EditMode' : 'ViewMode';
        this.switchViewportMode(vm.id, vpMode);
        vm.focus();

        if (vm instanceof BaseBoardViewportManager) {
            const l = ctrl.createZoomPanListener();
            this.curZoomPanListenerBySubVmId.set(vm.id, l);
            vm.zoomEventEmitter().registerListener(l as VEventListener<ZoomEventData>);
            vm.panEventEmitter().registerListener(l as VEventListener<PanEventData>);
        }
    }

    getCurrentFocusedViewport(ctrl: ComposerDocCtrl): ViewportManager {
        return this.curFocusVmByDoc.get(ctrl.state.globalId);
    }

    async clearInternalViewportFocus(ctrl: ComposerDocCtrl): Promise<void> {
        const vm = this.curFocusVmByDoc.get(ctrl.state.globalId);
        if (vm) {
            await this.unfocusViewport(vm);
            this.curFocusVmByDoc.delete(ctrl.state.globalId);
        }
    }

    async start(): Promise<void> {
        await this._cmdGateway.start();

        // start all sub editor
        for (const editor of this.editors.values()) {
            await editor.start();
        }
    }

    async stop(): Promise<void> {}

    /**
     * listen to the localContent changes to update the attribute in SubViewportNode.
     * No need to send further command to sync localContent state as it will be sync via lexical and yjs
     */
    private _onLocalContentChanges(changes: LocalContentUpdateResult[]) {
        for (const change of changes) {
            const vm = this.getViewportManager(change.vmId);
            const docCtrl: ComposerDocCtrl = this.getContainingDocFromChildViewport(vm);

            for (const ec of change.expectedChanges) {
                const content: DocLocalContent = change.editor.getLocalContent(change.vmId, ec.localId);
                if (content) {
                    docCtrl.updateSubVpLocalContent(change.vmId, ec.localId, content);
                }
            }
        }
    }

    private async _onDocRemoved(changes: CRUDChangeResult[]): Promise<void> {
        if (!changes.length) return;
        const vms = changes.map(c => this.getViewportManager(c.vmId));
        const docCtrl: ComposerDocCtrl = this.getContainingDocFromChildViewport(vms[0]);
        if (docCtrl) {
            // docCtrl.composerLib.dispatchCommand(
            //     DELETE_SUBVIEWPORTS_COMMAND,
            //     vms.map(vm => vm.id)
            // );
        }
    }
}
