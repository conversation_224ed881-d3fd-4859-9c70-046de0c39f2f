import {
    BoundaryRectangle,
    <PERSON><PERSON>ultVDocCtrl,
    DocLocalContent,
    DocLocalId,
    DocumentId,
    DocumentViewMode,
    DOMElementLayerCtrl,
    EditorType,
    HasBoundaryCtrl,
    KeyboardEventData,
    KeyboardEventListener,
    LoadingContext,
    MouseEventData,
    PanEventData,
    SelectHitContext,
    VDocLayerCtrl,
    VEventListener,
    ViewportId,
    ViewportManager,
    ZoomEventData,
} from '@viclass/editor.core';
import { BehaviorSubject, Subject, Subscription, tap, throttleTime } from 'rxjs';
import { ComposerEditor } from '../composer.editor';
import { loadCSS } from '../composer.utils';
import { ComposerEditorConfig } from '../config';
import { ComposerDoc, ComposerDocSettings, ComposerLayer, DEFAULT_SETTINGS, FetchDocResponse } from '../model';

export class ComposerDocCtrl extends DefaultVDocCtrl implements HasBoundaryCtrl {
    // keyboard listener that listens for all keyboard events originating from the Composer document body
    keyboardListener: KeyboardEventListener<any> = this.createKeyboardEventListener(this);

    layer: DOMElementLayerCtrl;

    root: HTMLDivElement;

    settings: ComposerDocSettings;

    readonly processingCount$ = new BehaviorSubject(0);

    private focusHandler = this.processFocus.bind(this);

    private docViewMode: DocumentViewMode;

    // --------------------

    constructor(
        override editor: ComposerEditor,
        public override state: ComposerDoc,
        viewport: ViewportManager,
        private loadingContext?: LoadingContext
    ) {
        super(state, editor, viewport);
        this.docViewMode = editor.conf.docViewMode || 'full-viewport';
    }

    /**
     * Add a layer to the document control. In composer, we only use 1 layer
     */
    override addLayer(layer: VDocLayerCtrl): void {
        if (!(layer instanceof DOMElementLayerCtrl)) return;

        super.addLayer(layer);

        this.layer = layer;
        this.state.addLayer(layer.state as ComposerLayer);
    }

    /**
     * Apply settings of the composer document. To be called after the document is created
     * or the setting changes
     */
    applySettings(settings: ComposerDocSettings) {
        this.settings = { ...settings };

        const root = this.root;
        root.style.paddingLeft = `${settings.padding}px`;
        root.style.paddingRight = `${settings.padding}px`;
    }

    /**
     * Clean up the composer document on remove
     */
    onRemove(): void {
        this.deregisterInputEvent();
        this.root.remove();
        delete this.root;

        this.removeLayer(this.layer);
        this.viewport.removeLayer(this.layer);
        this.state.layer = undefined;
    }

    /**
     * Check if the mouse event hits the composer document for the selection feature
     */
    checkHit(event: MouseEventData<any>, l: DOMElementLayerCtrl): SelectHitContext {
        const rect = l.domEl.getBoundingClientRect();
        const posX = event.nativeEvent.clientX;
        const posY = event.nativeEvent.clientY;

        if (rect.left < posX && posX < rect.right && rect.top < posY && posY < rect.bottom) {
            return {
                doc: this,
                hitDetails: undefined,
            };
        }
        return undefined;
    }

    /**
     * Disable the composer document edit mode
     */
    disableEditMode() {
        if (!this.root) return;

        // TODO: implement disable
        throw 'Disable edit mode not implemented';
    }

    /**
     * Enable the composer document edit mode
     */
    enableEditMode() {
        if (!this.root) return;

        // TODO: implement enable
        throw 'Enable edit mode not implemented';
    }

    /**
     * Register the input event for the composer document.
     * Currently use to handle focus and blur events on the root element
     */
    registerInputEvent() {
        this.root.addEventListener('focus', this.focusHandler);
        this.root.addEventListener('blur', this.focusHandler);
    }

    /**
     * Deregister the input event for the composer document on doc removal
     */
    deregisterInputEvent() {
        this.root.removeEventListener('focus', this.focusHandler);
        this.root.removeEventListener('blur', this.focusHandler);
    }

    /**
     * Process focus and blur events on the root element.
     * We will clear the internal viewport focus so when user focus into the composer doc again,
     * the composer toolbar will show up instead of the previous sub-editor toolbar
     */
    processFocus(event: Event) {
        switch (event.type) {
            case 'focus': {
                this.editor.composerCoord.clearInternalViewportFocus(this);
                break;
            }
            case 'blur': {
                this.editor.composerCoord.clearInternalViewportFocus(this);
                break;
            }
        }
    }

    /**
     * Trigger this method to insert a new embed viewport at the current selection position
     */
    processInsertViewportEvent(edType: EditorType) {
        const viewportId = this.viewportId(this.state.globalId, edType as EditorType);
        // TODO: here
        // this.composerLib.insertViewport(viewportId, edType);

        // TODO: here
        throw 'Insert viewport not implemented: ' + viewportId;
    }

    /**
     * Generate viewport id for the sub-viewport
     * @param parentId id of the parent composer doc
     * @param edType editor type of sub-viewport
     * @returns the randomly generated viewport id
     */
    viewportId(parentId: DocumentId, edType: EditorType) {
        return `${parentId}_${edType}_${new Date().getTime()}${Math.floor(Math.random() * 10000)}`;
    }

    /**
     * Insert a new viewport into the provided DOM element,
     * which is the DOM generated by the SubViewportNode
     *
     * @param element the root element of the viewport
     * @param edType the editor type of the sub-viewport
     * @returns the viewport manager for the new viewport
     */
    insertViewport(element: HTMLElement, edType: EditorType): ViewportManager {
        const coord = this.editor.composerCoord;
        return coord.createViewport(this, element, edType);
    }

    /**
     * Get the content of the composer document from server.
     * The content format is yjs binary encoded in base64
     */
    async getComposerDocContent(): Promise<FetchDocResponse | null> {
        if (!this.loadingContext) return null;

        return await this.editor.getDocumentContentByGlobalId(this.state.globalId, this.loadingContext);
    }

    /**
     * Listen to keyboard event to block some key events. Because composer editor will handle all keys
     * so we block them from polute the parent viewport (ex: classroom)
     */
    private createKeyboardEventListener(_p: ComposerDocCtrl): KeyboardEventListener<any> {
        return new (class implements KeyboardEventListener<any> {
            onEvent(eventData: KeyboardEventData<any>): KeyboardEventData<any> {
                // TODO: implement if needed
                return eventData;
            }
        })();
    }

    /**
     * Listen to zoom and pan events of sub-viewports to update the viewport state
     */
    createZoomPanListener(): VEventListener<ZoomEventData | PanEventData> {
        return new (class implements VEventListener<ZoomEventData | PanEventData> {
            throttler$ = new Subject<ZoomEventData | PanEventData>();
            subscription: Subscription;

            constructor(docCtrl: ComposerDocCtrl) {
                this.subscription = this.throttler$
                    .pipe(
                        throttleTime(100),
                        tap(data => {
                            if (docCtrl.viewport.mode === 'EditMode') {
                                const internalVmId = data.source.id;
                                const center = data.source.currentLookAt;
                                const zoom = data.source.zoomLevel;

                                // TODO: here
                                throw 'Update viewport zoom/pan not implemented: ' + internalVmId;
                            }
                        })
                    )
                    .subscribe();
            }

            onEvent(eventData: ZoomEventData | PanEventData): any {
                // only trigger if this is has uiEventSource (event comming from ZoomTool or PanTool).
                // otherwise it will be an infinite loop
                if (eventData.uiEventSource) {
                    this.throttler$.next(eventData);
                }
            }

            onUnregister? = () => {
                if (!this.subscription.closed) this.subscription.unsubscribe();
            };
        })(this);
    }

    /**
     * Handle unselect the composer document, we will clear selection and disable editing here
     */
    unselect() {
        this.root.classList.remove('selected');
        // TODO: here
        // this.composerLib.clearNodeSelection();
        this.disableEditMode();
        if (this.layer) this.viewport.sink(this.layer);
    }

    /**
     * Handle select the composer document
     */
    select() {
        this.root.classList.add('selected');
        if (this.layer) this.viewport.float(this.layer);
    }

    /**
     * To be called form the cmd processor to update boundary of the doc ctrl
     */
    updateBoundary(boundary: BoundaryRectangle) {
        if (this.docViewMode == 'bounded') {
            this.state.layer.boundary = boundary;

            if (this.layer) this.layer.updateBoundary(boundary);
        }
    }

    /**
     * Init the composer document content, optionally with an initial state
     */
    private initComposerDocContent(content: any) {
        const composerTheme = (this.editor.conf as ComposerEditorConfig).composerTheme;
        loadCSS(composerTheme, this.root.ownerDocument);

        const encapsulation = document.createElement('div');
        encapsulation.classList.add('composer-encapsulation');
        encapsulation.dataset['globalId'] = this.state.globalId;

        this.root.appendChild(encapsulation);

        const container = this.createComposerContainer();
        encapsulation.appendChild(container);

        // TODO: init content here
        throw new Error('Not implemented init content');
    }

    /**
     * Create the container for the composer document that will be use for lexical
     */
    private createComposerContainer(): HTMLDivElement {
        const container = document.createElement('div');
        container.classList.add('vi-composer-doc', 'vi-theme');
        container.contentEditable = 'true';

        // Restore scroll position on layout shift
        let scrollVal: number | undefined = undefined;
        // Restore focus on the container as it will lose focus on layout shift
        let refocus = false;
        this.layer.beforeLayoutShift = () => {
            scrollVal = container.scrollTop;
            refocus = document.activeElement === container;
        };

        this.layer.afterLayoutShift = () => {
            if (Number.isFinite(scrollVal)) {
                container.scrollTop = scrollVal;
            }
            scrollVal = undefined;

            if (refocus) {
                container.focus({ preventScroll: true });
            }
            refocus = false;
        };

        return container;
    }

    /**
     * Update doc mapping of the sub-viewports
     */
    updateInternalDocMapping(vmId: string, globalId: DocumentId, localId: number) {
        // TODO: here
        // this.composerLib.updateSubViewportMapping(vmId, globalId, localId);
    }

    /**
     * Initialize the composer document content
     */
    initializeLayerContent() {
        this.setContent(this.state.content);
    }

    /**
     * Replace the content of the composer document with the given content
     * @param content composer content as binary encoded in base64
     * @param settingJSON settings of the composer document
     */
    setContent(content: any, settingJSON?: string) {
        // TODO: here
        // if (!this.composerLib) {
        //             this.createRootElement();
        //             this.layer.domEl.appendChild(this.root);
        //
        //             this.initComposerDocContent(content);
        //         } else if (!!content) {
        //             // TODO: here
        //             // this.composerLib.setContent(content);
        //         }

        let settings = { ...DEFAULT_SETTINGS };
        if (settingJSON) {
            settings = { ...settings, ...JSON.parse(settingJSON) };
        }
        // TODO: here
        // this.composerLib.loadPendingSubViewportNodes();
        this.applySettings(settings);
    }

    /**
     * Update local content of the sub-viewport in LOCAL mode (i.e. Math viewport)
     */
    updateSubVpLocalContent(vmId: ViewportId, localId: DocLocalId, content: DocLocalContent) {
        // TODO: here
        throw 'Update sub-viewport local content not implemented: ' + vmId + ' ' + localId;
    }

    /**
     * Create the root element for the composer document
     */
    private createRootElement() {
        this.root = document.createElement('div') as HTMLDivElement;
        this.root.classList.add('composer-root');
        Object.assign(this.root.style, {
            padding: '0',
            margin: '0',
            width: '100%',
            height: '100%',
        });

        /**
         * We want the click & mousedown event to only limited inside the composer doc
         * and not be processed by the parent viewport. Otherwise the SelectTool of parent viewport
         * will be triggered and accidentally de-select the composer doc.
         */
        const handleRootElementEvent = (e: MouseEvent) => {
            e.stopPropagation();
            if (e.button === 1) {
                e.preventDefault(); // prevent middle mouse browser cursor
            }
        };
        this.root.addEventListener('click', handleRootElementEvent);
        this.root.addEventListener('mousedown', handleRootElementEvent);

        this.layer.domEl.appendChild(this.root);
    }

    get viewportElClass(): string {
        return this.state.viewportElClass;
    }

    /**
     * Start a processing on the document that require blocking the user input.
     * Ex: inserting a document into sub-viewport
     */
    startProcessing() {
        this.processingCount$.next(this.processingCount$.value + 1);
    }

    /**
     * Finishes a processing on the document that require blocking the user input.
     */
    stopProcessing() {
        this.processingCount$.next(this.processingCount$.value - 1);
    }

    getBoundary(): BoundaryRectangle {
        return this.state.layer?.boundary;
    }
}
