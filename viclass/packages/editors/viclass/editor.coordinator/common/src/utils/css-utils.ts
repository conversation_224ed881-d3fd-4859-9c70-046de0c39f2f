function makeMathFontFace(name: string, source: string, descriptors: Record<string, string> = {}): FontFace {
    return new FontFace(name, `url(${source}.woff2) format('woff2')`, descriptors);
}

let mathFontLoad = false;

async function loadMathFonts(fontsDirectory: string, targetDoc: Document): Promise<void> {
    if (mathFontLoad) return;

    // Locate the `fonts` folder relative to the script URL
    const fontsFolder = new URL(fontsDirectory).href;
    if (!fontsFolder) {
        return;
    }

    // block multiple editors loading the same math fonts. Must do before actual load because the editors are all init at the same time
    mathFontLoad = true;

    const fonts = (
        [
            ['KaTeX_Main-Regular'],
            ['KaTeX_Main-BoldItalic', { style: 'italic', weight: 'bold' }],
            ['KaTeX_Main-Bold', { weight: 'bold' }],
            ['KaTeX_Main-Italic', { style: 'italic' }],
            ['KaTeX_Math-Italic', { style: 'italic' }],
            ['KaTeX_Math-BoldItalic', { style: 'italic', weight: 'bold' }],
            ['KaTeX_AMS-Regular'],
            ['KaTeX_Caligraphic-Regular'],
            ['KaTeX_Caligraphic-Bold', { weight: 'bold' }],
            ['KaTeX_Fraktur-Regular'],
            ['KaTeX_Fraktur-Bold', { weight: 'bold' }],
            ['KaTeX_SansSerif-Regular'],
            ['KaTeX_SansSerif-Bold', { weight: 'bold' }],
            ['KaTeX_SansSerif-Italic', { style: 'italic' }],
            ['KaTeX_Script-Regular'],
            ['KaTeX_Typewriter-Regular'],
            ['KaTeX_Size1-Regular'],
            ['KaTeX_Size2-Regular'],
            ['KaTeX_Size3-Regular'],
            ['KaTeX_Size4-Regular'],
        ] as [string, Record<string, string>][]
    ).map(x => makeMathFontFace(x[0].replace(/-[a-zA-Z]+$/, ''), `${fontsFolder}/${x[0]}`, x[1]));
    try {
        const loadedFonts = (await Promise.all(
            fonts.map(x => {
                try {
                    return x.load();
                } catch {}

                return undefined;
            })
        )) as unknown as FontFace[];
        // Render them at the same time
        loadedFonts.forEach(font => targetDoc.fonts.add(font));
        return;
    } catch (error: unknown) {
        console.error(`The fonts could not be loaded from "${fontsFolder}"`, {
            cause: error,
        });
    }
}

export function loadCSS(url: string, targetDoc: Document): Promise<void> {
    // Check if the CSS has already been loaded
    const existingStylesheet = Array.from(targetDoc.styleSheets).find(sheet => sheet.href === url);
    if (!existingStylesheet) {
        // Create a <link> tag
        const link = targetDoc.createElement('link');
        link.rel = 'stylesheet';
        link.type = 'text/css';
        link.href = url;

        // Append the <link> tag to the document head
        targetDoc.head.appendChild(link);

        return new Promise<void>((rs, rj) => {
            // Listen for the load event
            link.addEventListener('load', () => rs());
            link.addEventListener('error', e => rj(e));
        });
    } else {
        return Promise.resolve();
    }
}

export async function loadMathCss(targetDoc?: Document, mathOrigin?: string) {
    if (!mathOrigin) {
        // if not provide -> fallback to current location origin or the production domain
        const url = new URL(location.href);
        const isViclassDomain = url.hostname === 'viclass.vn' || url.hostname.endsWith('.viclass.vn');
        mathOrigin = isViclassDomain ? url.origin : 'https://viclass.vn';
    }

    return await loadCSS(`${mathOrigin}/modules/themes/vi.theme.math-static.css`, targetDoc || document);
}
