import {
    BaseCoordinator,
    WrappingBoardViewportManager,
    WrappingInlineViewportManager,
} from '@viclass/editor.coordinator/common';
import {
    AwarenessFeature,
    AwarenessTool,
    BaseViewportManager,
    CommonToolbar,
    ContextMenuFeature,
    ContextMenuTool,
    DefaultViewportEventManager,
    EditorCoordinatorConfig,
    EditorEventManager,
    EditorLookup,
    EditorType,
    LoadingContext,
    PanTool,
    ROBFeature,
    SelectionFeature,
    SelectTool,
    ToolBar,
    ViewportFocusInCES,
    ViewportFocusOutCES,
    ViewportId,
    ViewportManager,
    ZoomTool,
} from '@viclass/editor.core';
import { DirectBackendConnector } from './backend.connector';
import { HandlerAttachmentManager } from './handler.attachment.manager';

/**
 * Configurations for the doc loader coordinator
 */
export interface DocLoaderCoordinatorConfig extends EditorCoordinatorConfig {
    /**
     * List of all editor to be loaded
     */
    edLookups: EditorLookup[];
}

/**
 * The configuration set for a HTML element which will be used by the coordinator
 * to initialize correct viewport. Some parameters here will be passed on
 * to the editor for further customization of the document.
 *
 * The positioning of document's layer can be annotated using this config so that user
 * can control
 *
 */
export type BoardType = 'board' | 'inline';

export interface ViewportConfig {
    bType: BoardType; // type of the board
    viewportBoundingEl: HTMLElement;
}

export interface DocLoadingConfig {
    viewportConf: Partial<ViewportConfig>;
    gId: string; // global id
    edType: EditorType; // editor type
    docAttr: string; // json encoded attribute to be passed to document editor when loading the doc
}

/**
 * DocLoaderCoordinator is responsible for loading document on a html page.
 */
export class DocLoaderCoordinator extends BaseCoordinator {
    vms: Map<HTMLElement, ViewportManager> = new Map();
    vmsById: Map<string, ViewportManager> = new Map();
    em: EditorEventManager;
    ham: HandlerAttachmentManager;

    contextmenuFeature: ContextMenuFeature;

    private toolbarStacks: Map<
        string,
        {
            common: CommonToolbar;
            editorToolbar: Map<EditorType, ToolBar<any, any>>;
        }
    > = new Map();

    private readonly selectionFeature: SelectionFeature;
    private readonly robFeature: ROBFeature;
    private readonly awarenessFeature: AwarenessFeature;

    constructor(public override conf: DocLoaderCoordinatorConfig) {
        super(conf);
        this.contextmenuFeature = new ContextMenuFeature(this);
        this.em = new DefaultViewportEventManager(this);
        this.ham = new HandlerAttachmentManager(this);
        this.selectionFeature = new SelectionFeature(this);
        this.robFeature = new ROBFeature(this);
        this.awarenessFeature = new AwarenessFeature(this);
    }

    override createToolbar(): CommonToolbar {
        const selectTool = new SelectTool(this, this.selectionFeature, this.robFeature);
        const tb = new CommonToolbar(this);
        tb.addTool('contextmenu', new ContextMenuTool(this, this.selectionFeature, this.contextmenuFeature));
        tb.addTool('zoom', new ZoomTool(true, false));
        tb.addTool('pan', new PanTool(true, false));
        tb.addTool('select', selectTool);
        tb.addTool('awareness', new AwarenessTool(this.awarenessFeature));

        return tb;
    }

    override getCommonToolbar(viewportId: string): CommonToolbar {
        return this.toolbarStacks.get(viewportId).common;
    }

    override getEditorToolbar(viewportId: string, editorType: EditorType): ToolBar<any, any> {
        return this.toolbarStacks.get(viewportId).editorToolbar.get(editorType);
    }

    getViewportManager(viewportId: string): ViewportManager {
        return this.vmsById.get(viewportId);
    }

    async removeViewport(vpId: ViewportId): Promise<void> {
        const vm = this.getViewportManager(vpId);
        if (!vm) return;

        // detach toolbar
        const tbStack = this.toolbarStacks.get(vpId);
        if (tbStack) {
            tbStack.common.detachViewport(vm);
            tbStack.editorToolbar.forEach(edTb => edTb.detachViewport(vm));

            this.toolbarStacks.delete(vpId);
        }

        // clean up events
        this.em.detachViewport(vm);
        this._cmdGateway.unregisterViewport(vm.id);

        // clean local state
        this.vmsById.delete(vpId);
        this.vms.delete(vm.rootEl as HTMLElement);

        // emit event for the editors to handle
        this.coordEventEmitter.emit({
            eventType: 'viewport-removed',
            source: this,
            state: { vmId: vpId },
        });

        await vm.destroy();
    }

    /**
     * Load all the editor implementation from the editor mapping
     * @return
     */
    async initialize(): Promise<void> {
        const promises: Promise<any>[] = [];

        for (const edType of Object.keys(this.conf.editorTypeMapping)) {
            const edLookup: EditorLookup = this.conf.edLookups.find(e => e.editorType == edType) as EditorLookup;
            if (edLookup) {
                const promise = this.addEditor(edLookup);
                promises.push(promise);
            }
        }

        await Promise.all(promises);
    }

    /**
     * When calling start, all documents specified inside the
     * the HTML page of certain class will be retrieved and loaded
     */
    start(): Promise<void> {
        // TODO: needs to change to scan the document to find all element to be rendered
        return Promise.resolve();
    }

    stop(): Promise<void> {
        return Promise.resolve();
    }

    createViewport(el: HTMLElement, vmConfig?: Partial<ViewportConfig>): ViewportManager {
        if (!vmConfig)
            vmConfig = {
                bType: 'board',
            };
        if (!vmConfig.viewportBoundingEl) vmConfig.viewportBoundingEl = el;

        return this.generateVM(el, vmConfig as ViewportConfig); // casting because we have set all required parameters
    }

    override async addEditor(lookup: EditorLookup): Promise<EditorType> {
        this.conf.edLookups.push(lookup);
        return super.addEditor(lookup);
    }

    loadDocOnViewport(vm: ViewportManager, config: Partial<DocLoadingConfig>): Promise<any> {
        const editor = this.editorByType(config.edType);
        const lookup = this.conf.edLookups.filter(l => l.editorType == config.edType)[0];

        if (!lookup || !lookup.settings || !lookup.settings['apiUri'])
            throw new Error(
                'Unable to load document specified since the endpoint for document editor is not provided.'
            );

        const context: LoadingContext = {
            vm: vm,
            connector: new DirectBackendConnector(lookup.settings['apiUri']),
            zIndexes: [],
        };

        return editor.loadDocumentByGlobalId(config.gId, context);
    }

    /**
     * Load a document configured on a particular element
     */
    loadDoc(el: HTMLElement, config: Partial<DocLoadingConfig>): Promise<any> {
        const data = el.dataset as Partial<DocLoadingConfig>;
        config = this.mergeConfig(this.setDefaultConfig(config), data);

        if (!config.edType) {
            throw new Error(`Unable to load document. Document ID is not provided`);
        }

        if (!config.gId) {
            throw new Error(`Unable to load document ${config.gId} since editor type is unknown`);
        }

        const vm: ViewportManager = this.createViewport(el, config.viewportConf);

        return this.loadDocOnViewport(vm, config);
    }

    /**
     * Generate correct VM based on the VM Type
     */
    private generateVM(rootEl: HTMLElement, config: ViewportConfig): ViewportManager {
        let vm = this.vms.get(rootEl);

        if (vm) return vm;

        switch (config.bType) {
            case 'board': {
                vm = new WrappingBoardViewportManager(rootEl);
                config.viewportBoundingEl.addEventListener('mouseenter', (event: MouseEvent) =>
                    this.mouseEnterViewport(vm)
                );
                config.viewportBoundingEl.addEventListener('mouseleave', (event: MouseEvent) =>
                    this.mouseLeaveViewport(vm)
                );

                const commonToolbar = this.createToolbar();
                commonToolbar.attachViewport(vm);

                const stack = {
                    common: commonToolbar,
                    editorToolbar: new Map<EditorType, ToolBar<any, any>>(),
                };

                // create editor toolbar in this viewport
                for (const k of this.editors.keys()) {
                    const ed = this.editors.get(k);
                    const edToolbar = ed.createToolbar();

                    edToolbar.attachViewport(vm);

                    stack.editorToolbar.set(k, edToolbar);
                }

                this.toolbarStacks.set(vm.id, stack);
                break;
            }
            case 'inline': {
                vm = new WrappingInlineViewportManager(rootEl);
                break;
            }
            default:
                break;
        }

        this.vms.set(rootEl, vm);
        this.vmsById.set(vm.id, vm);

        this.em.attachViewport(vm);

        (vm as BaseViewportManager).eventManager = this.em;
        vm.mode = 'Disabled';

        return vm;
    }

    mouseLeaveViewport(vm: ViewportManager): any {
        const eS: ViewportFocusOutCES = {
            vmId: vm.id,
        };

        this.coordEventEmitter.emit({
            eventType: 'viewport-focusout',
            source: this,
            state: eS,
        });
    }

    mouseEnterViewport(vm: ViewportManager): any {
        const eS: ViewportFocusInCES = {
            vmId: vm.id,
        };

        this.coordEventEmitter.emit({
            eventType: 'viewport-focusin',
            source: this,
            state: eS,
        });
    }

    /**
     * Merge configuration of a programmatically provided config with configuration provided declarative in element tag
     */
    private mergeConfig(config: Partial<DocLoadingConfig>, data: Partial<DocLoadingConfig>): Partial<DocLoadingConfig> {
        for (const k in data) config[k] = data[k];

        return config;
    }

    /**
     * Some value of a doc loading config must be initialized if not provided by user
     */
    private setDefaultConfig(config: Partial<DocLoadingConfig>): Partial<DocLoadingConfig> {
        if (!config.viewportConf) config.viewportConf = {};

        if (!config.viewportConf.bType) config.viewportConf.bType = 'board';

        return config;
    }
}
