import {
    ContentVisibilityCheckFeature,
    CoordinatorEvent,
    DefaultVDocCtrl,
    DocumentEditor,
    EditorBase,
    FEATURE_CONTENT_VISIBILITY,
    FEATURE_SELECTION,
    MuitlDocInfoCES,
    NewDocChangeCES,
    SelectionEvent,
    SupportContentVisibilityCheckFeature,
    ToolState,
    VDocCtrl,
    VEventListener,
    ViewportContentEvent,
} from '@viclass/editor.core';
import {
    ApplyDocSettingState,
    CLASSROOM_VP_EVENT,
    ClassroomCoordinator,
    ClassroomDocLoadedState,
} from '../classroom.coordinator';
import { DefaultSetting, DocSetting } from '../data.model';
import { ClassroomTool, ClassroomToolType } from './classroom.tool';

export const DOC_LAYER_BORDER_CLASS_PREFIX = 'doc-layer-border-';
export const DOC_LAYER_SHADOW_CLASS_PREFIX = 'doc-layer-shadow-';

export type SettingDto = DocSetting & {
    docCtrl: VDocCtrl;
    editor: DocumentEditor;
};

export class ClassroomDocSettingState implements ToolState {
    focusedDocSettings: SettingDto[];
    defaultSetting: DefaultSetting;
}

const INITIAL_DEFAULT_SETTING = {
    border: false,
    borderType: '',
    borderColor: '',
    shadow: false,
    shadowType: '',
    background: false,
    backgroundColor: '',
};
export class ClassroomDocSettingTool extends ClassroomTool {
    override toolType: ClassroomToolType = 'docsettingtool';

    override toolState?: ClassroomDocSettingState = {
        focusedDocSettings: [],
        defaultSetting: { ...INITIAL_DEFAULT_SETTING },
    };

    constructor(
        public coord: ClassroomCoordinator,
        public contentVisibilityFeature: ContentVisibilityCheckFeature
    ) {
        super();

        this.contentVisibilityFeature.addNotifier(this.onContentVisibilityChanged.bind(this));

        this.coord.registerCoordEventListener(new this._COORD_HANDLER(this));
    }

    async adjustSettings(changedFields: Record<string, any>): Promise<void> {
        if (this.toolState.focusedDocSettings.length > 0) {
            // update doc settings
            const newSettings = this.toolState.focusedDocSettings.map(setting => ({
                ...setting,
                ...changedFields,
            }));

            this.toolState.focusedDocSettings = newSettings;
            this.toolbar.update('docsettingtool', {
                ...this.toolState,
                focusedDocSettings: newSettings,
            });

            await this.syncCurrentDocSettings();

            for (const dto of this.toolState.focusedDocSettings) {
                this.applyDocSetting(dto.docCtrl);
            }
        } else {
            // update default settings
            const defaultSetting = { ...this.getDefaultDocSetting() };
            for (const key of Object.keys(changedFields)) {
                defaultSetting[key] = changedFields[key];
            }

            const coordId = this.toolbar.viewport.id;
            this.coord.setDefaultDocSetting(coordId, defaultSetting);

            this.toolbar.update('docsettingtool', {
                ...this.toolState,
                defaultSetting: { ...defaultSetting },
            });

            await this.coord.syncDefaultDocSetting(coordId);
        }
    }

    /**
     * Aet focused doc settings to local and send sync cmd to peers
     */
    private async syncCurrentDocSettings(): Promise<void> {
        const settingDtos = this.toolState?.focusedDocSettings || [];
        if (!settingDtos.length) return;

        for (const dto of settingDtos) {
            const newSetting: DocSetting = { ...dto };
            // remove unwanted properties from dto
            newSetting['docCtrl'] = undefined;
            newSetting['editor'] = undefined;

            this.setDocSetting(dto.docCtrl, newSetting);
        }

        await this.coord.syncDocSetting(this.toolbar.viewport.id, settingDtos);
    }

    /**
     * Apply the setting to the docCtrl, so it can take effect
     */
    applyDocSetting(docCtrl: VDocCtrl) {
        if (!this.toolbar.viewport) return;
        // ignore full-viewport docs (ex: freedrawing) because it will hide other docs when change background
        if ((docCtrl.editor as EditorBase<any>).docViewMode === 'full-viewport') return;

        const setting = this.getDocSetting(docCtrl);
        const settingDto = this.createSettingDto(docCtrl, setting);

        const layers = (docCtrl as DefaultVDocCtrl).layers;

        for (const layer of layers) {
            const wrapper = layer.nativeEl;
            this.applyBorder(wrapper, settingDto);
            this.applyShadow(wrapper, settingDto);
            this.applyBackground(wrapper, settingDto);
        }
    }

    getDefaultDocSetting(): DefaultSetting {
        return this.coord.getDefaultDocSetting(this.toolbar.viewport.id);
    }

    private createSettingDto(docCtrl: VDocCtrl, setting?: DocSetting): SettingDto {
        if (!setting) {
            const defaultSetting = this.coord.getDefaultDocSetting(this.toolbar.viewport.id);
            setting = {
                ...defaultSetting,
                docLocalId: docCtrl.state.id,
                channelCode: docCtrl.editor.id,
            };
        }
        return {
            ...setting,
            docCtrl,
            editor: docCtrl.editor,
        };
    }

    private onContentVisibilityChanged(docCtrl: VDocCtrl): void {
        this.applyDocSetting(docCtrl);
    }

    private applyBorder(wrapper: HTMLElement | SVGElement, setting: SettingDto) {
        if (setting.border && setting.borderType) {
            this.applyBorderType(wrapper, setting.borderType);
        } else {
            const editor = setting.editor;
            let contentVisible = true;
            if (editor.isSupportFeature(FEATURE_CONTENT_VISIBILITY)) {
                const supporter =
                    editor.featureSupporter<SupportContentVisibilityCheckFeature>(FEATURE_CONTENT_VISIBILITY);
                contentVisible = supporter.isContentVisible(setting.docCtrl);
            }

            if (contentVisible) {
                this.applyBorderType(wrapper, 'none');
            } else {
                this.applyBorderType(wrapper, 'no-content');
            }
        }

        if (setting.borderColor) {
            this.applyBorderColor(wrapper, setting.borderColor);
        }
    }

    private applyShadow(wrapper: HTMLElement | SVGElement, setting: SettingDto) {
        if (setting.shadow && setting.shadowType) {
            this.applyShadowType(wrapper, setting.shadowType);
        } else {
            this.applyShadowType(wrapper, 'none');
        }
    }

    private applyBackground(wrapper: HTMLElement | SVGElement, setting: SettingDto) {
        if (setting.background && setting.backgroundColor) {
            this.applyBackgroundColor(wrapper, setting.backgroundColor);
        } else {
            this.applyBackgroundColor(wrapper, 'transparent');
        }
    }

    private applyBorderType(wrapper: HTMLElement | SVGElement, borderClassSuffix: string) {
        for (const cls of Array.from(wrapper.classList).filter(cls => cls.startsWith(DOC_LAYER_BORDER_CLASS_PREFIX))) {
            wrapper.classList.remove(cls);
        }

        wrapper.classList.add(DOC_LAYER_BORDER_CLASS_PREFIX + borderClassSuffix);
    }

    private applyBorderColor(wrapper: HTMLElement | SVGElement, color: string) {
        wrapper.style.outlineColor = color;
    }

    private applyShadowType(wrapper: HTMLElement | SVGElement, shadowClassSuffix: string) {
        for (const cls of Array.from(wrapper.classList).filter(cls => cls.startsWith(DOC_LAYER_SHADOW_CLASS_PREFIX))) {
            wrapper.classList.remove(cls);
        }

        wrapper.classList.add(DOC_LAYER_SHADOW_CLASS_PREFIX + shadowClassSuffix);
    }

    private applyBackgroundColor(wrapper: HTMLElement | SVGElement, color: string) {
        if (wrapper instanceof SVGElement) {
            const root = wrapper.querySelector('foreignObject');
            if (root) {
                root.style.background = color;
                return;
            }
        }

        wrapper.style.background = color;
    }

    private getDocSetting(doc: VDocCtrl): DocSetting | undefined {
        return this.coord.getDocSetting(this.toolbar.viewport.id, doc.editor.id, doc.state.id);
    }

    private setDocSetting(doc: VDocCtrl, setting: DocSetting): void {
        this.coord.setDocSetting(this.toolbar.viewport.id, doc.editor.id, doc.state.id, setting);
    }

    /**
     * Listen to VP events (doc-focused, doc-blured) to update tool state with focused doc settings
     * -> setting UI will use this to update the view
     */
    readonly viewportContentEventListener = new (class VpContentListener
        implements VEventListener<ViewportContentEvent>
    {
        constructor(public tool: ClassroomDocSettingTool) {}

        onEvent(eventData: ViewportContentEvent): ViewportContentEvent | Promise<ViewportContentEvent> {
            const viewportId = this.tool.toolbar.viewport.id;
            if (viewportId !== eventData.source.id) return eventData;

            switch (eventData.state.source) {
                case FEATURE_SELECTION: {
                    this.handleSelectionEvent(eventData);
                    break;
                }
                case CLASSROOM_VP_EVENT: {
                    this.handleClassroomVpEvent(eventData);
                }
            }

            return eventData;
        }

        handleSelectionEvent(eventData: ViewportContentEvent) {
            const selectionEvent = eventData.state.originalEvent as SelectionEvent;
            const selectedCtxs = selectionEvent.source.curDocSelection;
            const docSettings: SettingDto[] = selectedCtxs.map(ctx => {
                const setting = this.tool.getDocSetting(ctx.doc);
                return this.tool.createSettingDto(ctx.doc, setting);
            });

            this.tool.toolState.focusedDocSettings = docSettings;
            this.tool.toolState.defaultSetting = this.tool.getDefaultDocSetting();

            this.tool.toolbar.update('docsettingtool', { ...this.tool.toolState });
        }

        handleClassroomVpEvent(eventData: ViewportContentEvent) {
            const origin = eventData.state.originalEvent;
            switch (origin.eventType) {
                case 'doc-loaded': {
                    const state = origin.state as ClassroomDocLoadedState;
                    for (const doc of state.docs) {
                        const docCtrl = doc.editor.findDocumentByLocalId(state.vm.id, doc.localId);
                        if (docCtrl) {
                            this.tool.applyDocSetting(docCtrl);
                        }
                    }
                    break;
                }
                case 'apply-doc-setting': {
                    const state = origin.state as ApplyDocSettingState;
                    const docCtrl = state.editor.findDocumentByLocalId(state.vm.id, state.localId);
                    if (docCtrl) {
                        this.tool.applyDocSetting(docCtrl);
                    }
                    break;
                }
            }
        }
    })(this);

    override onAttachViewport() {
        if (!this.toolbar.viewport) throw new Error('Viewport is not attached to the toolbar');

        this.toolbar.viewport.registerContentEventListener(this.viewportContentEventListener);
    }

    override onDetachViewport() {
        if (this.toolbar.viewport) {
            this.toolbar.viewport.unregisterContentEventListener(this.viewportContentEventListener);
        }
    }

    private _COORD_HANDLER = class implements VEventListener<CoordinatorEvent> {
        constructor(private tool: ClassroomDocSettingTool) {}

        onEvent(eventData: CoordinatorEvent): CoordinatorEvent | Promise<CoordinatorEvent> {
            if (!this.tool.toolbar.viewport) {
                // console.error('Viewport is not attached to the toolbar');
                return eventData;
            }

            const viewportId = this.tool.toolbar.viewport.id;
            if (viewportId !== eventData.state.vmId) return eventData;

            switch (eventData.eventType) {
                case 'viewport-selected': {
                    this.tool.toolState.defaultSetting = {
                        ...this.tool.getDefaultDocSetting(),
                    };
                    this.tool.toolbar.update('docsettingtool', {
                        ...this.tool.toolState,
                    });
                    break;
                }
                case 'load-doc': {
                    const state = eventData.state as MuitlDocInfoCES;
                    state.docInfos.forEach(doc => {
                        const editor = this.tool.coord.editorByType(doc.editorType);
                        const docCtrl = editor.findDocumentByLocalId(viewportId, doc.docLocalId);
                        if (docCtrl) this.tool.applyDocSetting(docCtrl);
                    });
                    break;
                }
                case 'new-doc': {
                    const state = eventData.state as NewDocChangeCES;
                    state.docs.forEach(doc => {
                        const docCtrl = doc.editor.findDocumentByLocalId(viewportId, doc.expectedChanges[0].localId);
                        if (docCtrl) this.tool.applyDocSetting(docCtrl);
                    });
                    break;
                }
                default:
                    break;
            }
            return eventData;
        }
    };
}
