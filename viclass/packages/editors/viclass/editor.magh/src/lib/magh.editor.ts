/// <reference types="@viclass/ww/typings"/>
import { BoundaryDelegator } from '@viclass/editor.coordinator/classroom';
import {
    AwarenessFeature,
    BoundaryRectangle,
    BoundedGraphicLayerCtrl,
    Cmd,
    CmdChannel,
    ContentVisibilityCheckFeature,
    ContextMenuFeature,
    CRUDChangeResult,
    CRUDDelegator,
    DocCRDFeature,
    DocLocalContent,
    DocLocalId,
    DocumentEditor,
    DocumentId,
    EditorBase,
    EditorConfig,
    EditorCoordinator,
    EditorId,
    EditorType,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FEATURE_AWARENESS,
    FEATURE_CONTENT_VISIBILITY,
    FEATURE_CRD_DOC,
    FEATURE_CRUD,
    FEATURE_HISTORY,
    FEATURE_PAN,
    FEATURE_ROB,
    FEATURE_SELECTION,
    FEATURE_ZOOM,
    GraphicLayerCtrl,
    HasSelectionFeature,
    HistoryFeature,
    InsertDocCtrlDelegator,
    LayerOptions,
    LoadingContext,
    OperationMode,
    PanFeature,
    ROBFeature,
    SelectContext,
    SelectDelegator,
    SelectionFeature,
    SupportContentVisibilityCheckFeature,
    SupportFeatureHistory,
    SupportSelectFeature,
    ThrottleCombinator,
    ToolBar,
    UnboundedGraphicLayerCtrl,
    ViewportId,
    ViewportManager,
    ZoomFeature,
} from '@viclass/editor.core';
import { MathGraphCmdTypeProto } from '@viclass/proto/editor.magh';
import { FCCmdTypeProto } from '@viclass/proto/feature.common';
import { deserializer } from './cmds/magh.cmd';
import { MathGraphCmdProcessor } from './cmds/magh.cmd.processor';
import { MathGraphDocCtrl } from './docs/magh.doc.ctrl';
import { MathGraphHistoryItem } from './history';
import { MathGraphGateway } from './magh.gateway';
import { MathGraphDocInitData } from './magh.models';
import { FetchDocResponse, MathGraphDoc, MathGraphLayer } from './model';
import {
    defaultDocRenderProp,
    MathGraphContentTool,
    MathGraphCreateDocumentTool,
    MathGraphPanTool,
    MathGraphSettingsTool,
    MathGraphToolBar,
    MathGraphToolType,
    MathGraphZoomTool,
    UpdateEquationsTool,
} from './tools';

export class MathGraphEditor
    extends EditorBase<MathGraphDocCtrl>
    implements DocumentEditor, SupportContentVisibilityCheckFeature, HasSelectionFeature, SupportFeatureHistory
{
    readonly id: EditorId;
    readonly editorType: EditorType;

    private readonly _cmdChannel: CmdChannel;
    private readonly _cmdProcessor: MathGraphCmdProcessor;
    private readonly _maghGateway: MathGraphGateway;

    toolbars: Map<ViewportId, MathGraphToolBar> = new Map();
    contextMenuFeature: ContextMenuFeature;

    selectionFeature: SelectionFeature;
    historyFeature: HistoryFeature;
    robFeature: ROBFeature;
    crdFeature: DocCRDFeature;
    contentVisibilityFeature: ContentVisibilityCheckFeature;
    panFeature: PanFeature;
    zoomFeature: ZoomFeature;
    awarenessFeature: AwarenessFeature;

    crudDelegator: CRUDDelegator;

    readonly selectDelegator = new SelectDelegator<MathGraphDocCtrl>(this, {
        onSelect: this.onSelectDocCtrl.bind(this),
    });

    readonly insertDocDelegator = new InsertDocCtrlDelegator<MathGraphDocCtrl, MathGraphDoc>(
        this,
        (vp, state) => new MathGraphDocCtrl(this, state, vp)
    );

    // TODO:  here
    private readonly _operationMode: OperationMode;

    get operationMode(): OperationMode {
        return this._operationMode;
    }

    getLocalContent(vpId: ViewportId, localId: DocLocalId): DocLocalContent | undefined {
        return undefined;
    }

    static readonly cmdChannelThrottle = 300;

    readonly boundaryDelegator = new BoundaryDelegator(this);

    constructor(
        public conf: EditorConfig,
        private _coordinator: EditorCoordinator
    ) {
        super(conf);

        this.id = conf.id;
        this.editorType = conf.editorType;
        this._operationMode = conf.operationMode || OperationMode.CLOUD;

        this._maghGateway = new MathGraphGateway(conf.apiUri);

        this._cmdProcessor = new MathGraphCmdProcessor(this);
        this._cmdChannel = _coordinator.registerCmdChannel(this.id);
        this._cmdChannel.registerCmdListener(this._cmdProcessor);
        this._cmdChannel.registerCombinator(FCCmdTypeProto.PREVIEW_BOUNDARY, new ThrottleCombinator());
        this._cmdChannel.registerCombinator(
            FCCmdTypeProto.UPDATE_BOUNDARY,
            new ThrottleCombinator(MathGraphEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(
            MathGraphCmdTypeProto.UPDATE_DOC_STATE,
            new ThrottleCombinator(MathGraphEditor.cmdChannelThrottle)
        );
        this._cmdChannel.registerCombinator(
            MathGraphCmdTypeProto.UPDATE_CONTENT,
            new ThrottleCombinator(MathGraphEditor.cmdChannelThrottle)
        );

        this._cmdChannel.registerDeserializer(deserializer);

        this.crudDelegator = new CRUDDelegator(
            this,
            this.cmdChannel,
            this.regMan,
            { doc: this.docReg, layer: this.layerReg },
            this.generateInitDocData.bind(this)
        );
    }

    onSelectDocCtrl(docCtrl: MathGraphDocCtrl): void {
        const viewPortId = docCtrl.viewport.id;
        const selectFeature = this.selectionFeature;

        const contentTool = this.toolbars.get(viewPortId).getTool('ContentEditorTool') as MathGraphContentTool;
        contentTool.checkFocusedDocEditable(viewPortId, docCtrl, selectFeature);

        (docCtrl as MathGraphDocCtrl).select();
    }

    isContentVisible(_docCtrl: MathGraphDocCtrl): boolean {
        // TODO: update when there is more settings for the editor
        return true;
    }

    async initialize(): Promise<void> {
        // do nothing
    }

    async duplicateDoc(docGlobalIds: DocumentId[]): Promise<Map<DocumentId, DocumentId>> {
        return await this.maghGateway.duplicateDoc(docGlobalIds);
    }

    async undo(item: MathGraphHistoryItem) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
        const docCtrl = docRegistry.getEntity(item.docId);
        if (!docCtrl) return;

        docCtrl.setEquations(item.prevSnapshot || []);
        docCtrl.maghLib.reloadChart();
        switch (item.type) {
            case 'update':
                docCtrl.sendContentUpdateCmd(item.index);
                break;
            case 'add':
                docCtrl.sendDeleteEquationCmd(item.index);
                break;
            case 'delete':
                docCtrl.sendContentUpdateCmd(item.index, true); // insert true
                break;
            default:
                break;
        }
    }

    async redo(item: MathGraphHistoryItem) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(item.viewportId);
        const docCtrl = docRegistry.getEntity(item.docId);
        if (!docCtrl) return;

        docCtrl.setEquations(item.snapshot || []);
        docCtrl.maghLib.reloadChart();
        switch (item.type) {
            case 'update':
                docCtrl.sendContentUpdateCmd(item.index);
                break;
            case 'add':
                docCtrl.sendContentUpdateCmd(item.index, true); // insert true
                break;
            case 'delete':
                docCtrl.sendDeleteEquationCmd(item.index);
                break;
            default:
                break;
        }
    }

    async clearHistory(viewportId: ViewportId): Promise<void> {
        this.historyFeature?.clear(this, viewportId);
    }

    /**
     * remove document internally, meaning just remove doc without sync cmd
     *
     * @param vpId
     * @param docId
     */
    async internalRemoveDoc(vpId: ViewportId, docId: DocLocalId) {
        const docRegistry = this.regDelegator.getOrCreateDocReg(vpId);
        const layerRegistry = this.regDelegator.getOrCreateLayerReg(vpId, docId);
        const docCtrl = docRegistry.getEntity(docId);
        if (docCtrl) {
            // because the deselect method doesn't wait for blur, cannot use it directly
            await this.selectDelegator.doBlurDocCtrl(docCtrl);
            const context: SelectContext = {
                doc: docCtrl,
                supporter: this.featureSupporter<SupportSelectFeature>(FEATURE_SELECTION),
                selectDetails: {},
            };

            this.selectionFeature.onDeselect(context);
            docRegistry.removeEntity(docId);
            layerRegistry.removeEntity(docCtrl.layer.state.id);
            docCtrl.onRemove();
        }
    }

    get coordinator(): EditorCoordinator {
        return this._coordinator;
    }

    get cmdChannel(): CmdChannel {
        return this._cmdChannel;
    }

    get maghGateway(): MathGraphGateway {
        return this._maghGateway;
    }

    sendCommand(cmd: Cmd<any>) {
        this._cmdChannel.receive(cmd);
    }

    async loadDocumentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByGlobalId(
            this._cmdChannel.channelCode,
            globalId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext);
    }

    async loadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        const response = await loadingContext.connector.loadDocumentByLocalId(
            this._cmdChannel.channelCode,
            localId,
            'json'
        );
        await this.createDocumentCtrlFromResponseData(response, loadingContext, localId);
    }

    async reloadDocumentByLocalId(localId: DocLocalId, loadingContext: LoadingContext) {
        await this.loadDocumentByLocalId(localId, loadingContext);
    }

    getDocumentContentByGlobalId(globalId: DocumentId, loadingContext: LoadingContext): Promise<FetchDocResponse> {
        return loadingContext.connector.loadDocumentByGlobalId(this._cmdChannel.channelCode, globalId, 'json');
    }

    requestLayer(viewport: ViewportManager, add: boolean, options: LayerOptions): GraphicLayerCtrl {
        if (this.conf.docViewMode === 'full-viewport')
            return viewport.requestLayer(UnboundedGraphicLayerCtrl, add, options) as GraphicLayerCtrl;
        return viewport.requestLayer(BoundedGraphicLayerCtrl, add, options) as GraphicLayerCtrl;
    }

    getLayerBoundaryFromCtrl(layerCtrl: GraphicLayerCtrl): BoundaryRectangle {
        if (layerCtrl instanceof UnboundedGraphicLayerCtrl) return { start: { x: 0, y: 0 }, end: { x: 0, y: 0 } };
        if (layerCtrl instanceof BoundedGraphicLayerCtrl) return layerCtrl.boundary;
        throw new Error('Layer Ctrl of Undefined type');
    }

    private async createDocumentCtrlFromResponseData(
        response: FetchDocResponse,
        loadingContext: LoadingContext,
        localId?: DocLocalId
    ): Promise<MathGraphDocCtrl> {
        const docRegistry = this.regDelegator.getOrCreateDocReg(loadingContext.vm.id);

        // in scenarios where documents are not loaded by localId but only
        // global id is provided, we generate a local id for usage, it doesn't matter anyway
        const resolvedLocalId = localId ?? docRegistry.getAndIncrementId();

        const doc = new MathGraphDoc(
            resolvedLocalId,
            response.id,
            response.equations,
            response.docRenderProps,
            response.version
        );

        const layerId = 1; // only one layer per document, so layer id is always 1

        const state = new MathGraphLayer(layerId, resolvedLocalId); // initially, the layer state doesn't have a boundary

        /**
         * Request the layer used by the document controller from the viewport
         * Provide what we know about the layer. The actual positioning of the layer
         * on the board is determined by the viewport.
         *
         * In the case of the classroom, the viewport manager will get the position from the
         * doc mapping, in other case, the layer will be created in a default position with
         * the provided width / height
         */
        const layerCtrl = this.requestLayer(loadingContext.vm, true, {
            docLocalId: resolvedLocalId,
            docGlobalId: response.id,
            viewport: loadingContext.vm,
            editor: this,
            state: state,
            domElType: 'div',
        });

        state.boundary = this.getLayerBoundaryFromCtrl(layerCtrl);

        // let's start creating the doc controller
        const docCtrl = new MathGraphDocCtrl(this, doc, loadingContext.vm, loadingContext);

        docRegistry.addEntity(doc.id, docCtrl);

        // link the layer with the doc controller
        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);
        // Create overlay layer for DOM elements (LaTeX labels)
        docCtrl.createOverlayLayer();

        docCtrl.setEquations(response.equations);

        const layerRegistry = this.regDelegator.getOrCreateLayerReg(loadingContext.vm.id, resolvedLocalId);
        layerRegistry.addEntity(1, layerCtrl);

        return docCtrl;
    }

    async start() {
        await this._cmdProcessor.start();
        await this._cmdChannel.start();
    }

    featureSupporter<T>(featureKey: string): T {
        if ([FEATURE_CONTENT_VISIBILITY, FEATURE_HISTORY, FEATURE_PAN, FEATURE_ZOOM].includes(featureKey))
            return this as unknown as T;

        if (featureKey === FEATURE_CRUD) return this.crudDelegator as T;
        if (featureKey === FEATURE_SELECTION) return this.selectDelegator as T;

        throw new Error(`MathGraph Editor doesn't support feature ${featureKey}`);
    }

    isSupportFeature(featureKey: string): boolean {
        if (
            [
                FEATURE_SELECTION,
                FEATURE_ROB,
                FEATURE_HISTORY,
                FEATURE_CRD_DOC,
                FEATURE_CRUD,
                FEATURE_CONTENT_VISIBILITY,
                FEATURE_PAN,
                FEATURE_ZOOM,
                FEATURE_AWARENESS,
            ].includes(featureKey)
        ) {
            return true;
        }

        return false;
    }

    onFeatureInitialization(featureKey: string, feature: any): Promise<void> {
        switch (featureKey) {
            case FEATURE_SELECTION: {
                this.selectionFeature = feature as SelectionFeature;
                this.boundaryDelegator.setSelectionFeature(this.selectionFeature);
                break;
            }
            case FEATURE_HISTORY: {
                this.historyFeature = feature as HistoryFeature;
                break;
            }
            case FEATURE_ROB: {
                this.robFeature = feature as ROBFeature;
                break;
            }
            case FEATURE_CRD_DOC:
                this.crdFeature = feature as DocCRDFeature;
                this.crdFeature.registerEditor(this, this.cmdChannel);
                break;
            case FEATURE_CONTENT_VISIBILITY: {
                this.contentVisibilityFeature = feature as ContentVisibilityCheckFeature;
                break;
            }
            case FEATURE_PAN: {
                this.panFeature = feature as PanFeature;
                break;
            }
            case FEATURE_ZOOM: {
                this.zoomFeature = feature as ZoomFeature;
                break;
            }
            case FEATURE_AWARENESS: {
                this.awarenessFeature = feature as AwarenessFeature;
                break;
            }
            default:
                break;
        }
        return Promise.resolve();
    }

    initCreateDocTool(viewport: ViewportManager) {
        const createToolType: MathGraphToolType = 'CreateMathGraphDocumentTool';
        const createTool = this.crdFeature.generateCRDToolForViewport(
            viewport.id,
            createToolType,
            this,
            this.cmdChannel,
            this.robFeature,
            this.selectionFeature
        );

        return createTool;
    }

    createToolbar(): ToolBar<any, any> {
        const tb = new MathGraphToolBar(this);

        // floating ui is only available for bounded document
        if (this.conf.docViewMode === 'bounded') {
            tb.addTool('CreateMathGraphDocumentTool', new MathGraphCreateDocumentTool(this));
        }

        tb.addTool('ContentEditorTool', new MathGraphContentTool(this));
        tb.addTool('UpdateEquationsTool', new UpdateEquationsTool(this));
        tb.addTool('MaghSettingsTool', new MathGraphSettingsTool(this));

        if (this.conf.docViewMode !== 'full-viewport') {
            const maghPanTool = new MathGraphPanTool(this, this.panFeature);
            tb.addTool('MaghPanTool', maghPanTool);

            const maghZoomTool = new MathGraphZoomTool(this, this.zoomFeature);
            tb.addTool('MaghZoomTool', maghZoomTool);
        }

        return tb;
    }

    async generateInitDocData(curChanges: CRUDChangeResult, insertDoc: FCInsertDocCmd, insertLayer: FCInsertLayerCmd) {
        const response = await this.maghGateway.createDoc({
            docRenderProps: defaultDocRenderProp(),
        });
        const initData: MathGraphDocInitData = {
            equations: response.equations,
            docRenderProp: response.docRenderProps,
        };

        const encoder = new TextEncoder();
        const bytes = encoder.encode(JSON.stringify(initData));
        insertDoc.state.setInitdata(bytes);
        insertDoc.state.setGlobalId(response.id);
    }

    addHistoryItem(item: MathGraphHistoryItem) {
        if (!this.historyFeature) return; // if history feature is not initialize, we simply ignore
        const manager = this.historyFeature.getHistoryManager(item.viewportId);

        manager.push(item);
    }

    notifyContentVisibilityChange(docCtrl: MathGraphDocCtrl) {
        this.contentVisibilityFeature?.onContentVisibilityChange(docCtrl);
    }
}
