import { interpolate, InterpolationHandler } from '@viclass/editor.coordinator/common';
import {
    AbstractCommand,
    BoundaryRectangle,
    Cmd,
    CmdMeta,
    CmdOriginType,
    CmdProcessor,
    fcConvertProtoToBoundary,
    FCInsertDocCmd,
    FCInsertLayerCmd,
    FCPreviewBoundaryCmd,
    FCReloadDocCmd,
    FCRemoveDocCmd,
    FCUpdateDocCmd,
    GraphicLayerCtrl,
    ViewportManager,
} from '@viclass/editor.core';
import { MathGraphCmdTypeProto } from '@viclass/proto/editor.magh';
import { DocInfo, FCCmdTypeProto } from '@viclass/proto/feature.common';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphEditor } from '../magh.editor';
import { MathGraphDocInitData } from '../magh.models';
import { maghDocReg, maghLayerReg } from '../magh.utils';
import { MaghDocRenderProp, MathGraphDoc, MathGraphLayer } from '../model';
import { DeleteEquationCmd, UpdateContentCmd, UpdateDocStateCmd } from './magh.cmd';
import { convertProtoToDocRenderProp } from './proto.utils';

type UpdateBoundaryData = {
    id: DocInfo;
    b: BoundaryRectangle;
}[];

export class MathGraphCmdProcessor extends CmdProcessor {
    constructor(private editor: MathGraphEditor) {
        super();
    }

    async processCmd(
        cmd: AbstractCommand<MathGraphCmdTypeProto | FCCmdTypeProto>
    ): Promise<AbstractCommand<MathGraphCmdTypeProto | FCCmdTypeProto>> {
        switch (cmd.cmdType) {
            case FCCmdTypeProto.INSERT_DOC: {
                return this.processInsertDocCmd(cmd as FCInsertDocCmd);
            }
            case FCCmdTypeProto.PREVIEW_BOUNDARY: {
                this.processNewDocBoundaryCmd(cmd as FCPreviewBoundaryCmd);
                break;
            }
            case FCCmdTypeProto.INSERT_LAYER: {
                this.processInsertLayerCmd(cmd as FCInsertLayerCmd);
                break;
            }
            case FCCmdTypeProto.REMOVE_DOC: {
                await this.processRemoveDocCmd(cmd as FCRemoveDocCmd);
                break;
            }
            case FCCmdTypeProto.RELOAD_DOC: {
                return this.processReloadCmd(cmd as FCReloadDocCmd);
            }
            case FCCmdTypeProto.UPDATE_BOUNDARY: {
                return this.processUpdateBoundaryCmd(cmd as FCUpdateDocCmd);
            }

            case MathGraphCmdTypeProto.UPDATE_CONTENT: {
                this.processUpdateContentCmd(cmd as UpdateContentCmd);
                break;
            }
            case MathGraphCmdTypeProto.UPDATE_DOC_STATE: {
                this.processUpdateDocStateCmd(cmd as UpdateDocStateCmd);
                break;
            }
            case MathGraphCmdTypeProto.DELETE_EQUATION: {
                this.processDeleteEquationCmd(cmd as DeleteEquationCmd);
                break;
            }
            default:
                break;
        }

        return cmd;
    }

    private processNewDocBoundaryCmd(cmd: FCPreviewBoundaryCmd): FCPreviewBoundaryCmd {
        const result = this.editor.crdFeature.processDocCreationPreviewCmd(cmd, this.editor);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return result;
    }

    private async processInsertDocCmd(cmd: FCInsertDocCmd) {
        const initData = cmd.state.getInitdata() as Uint8Array;
        const decoder = new TextDecoder();
        const initObj: MathGraphDocInitData = JSON.parse(decoder.decode(initData));

        if (cmd.meta.origin === CmdOriginType.remote && !cmd.state.getGlobalId())
            throw new Error('State data for new magh document is not complete!');

        const doc = new MathGraphDoc(
            cmd.meta.targetId,
            cmd.state.getGlobalId(),
            initObj.equations,
            initObj.docRenderProp
        );

        this.editor.insertDocDelegator.insertDocCtrl(cmd.meta.viewport, doc);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);

        return cmd;
    }

    private processInsertLayerCmd(cmd: FCInsertLayerCmd) {
        const layerId = cmd.meta.targetId;
        const docId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;
        const zindex = cmd.state.getZIndex();

        const docRegistry = this.editor.regMan.registry<MathGraphDocCtrl>(maghDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(docId);

        const layerState = new MathGraphLayer(layerId, docId, fcConvertProtoToBoundary(cmd.state.getBoundary()));
        layerState.zindex = zindex;

        const layerCtrl = this.editor.requestLayer(docCtrl.viewport, true, {
            boundary: layerState.boundary,
            docLocalId: docId,
            docGlobalId: docCtrl.state.globalId,
            viewport: docCtrl.viewport,
            editor: this.editor,
            state: layerState,
            domElType: 'div',
        });

        layerCtrl.doc = docCtrl;
        docCtrl.addLayer(layerCtrl);
        docCtrl.setEquations(docCtrl.state.equations);

        // Create overlay layer for DOM elements (LaTeX labels)
        docCtrl.createOverlayLayer();

        const layerRegistry = this.editor.regMan.registry<GraphicLayerCtrl>(maghLayerReg(cmd.meta.viewport.id, docId));
        layerRegistry.addEntity(layerId, layerCtrl);

        this.editor.insertDocDelegator.notifyDocCreation(vm, docId);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private async processRemoveDocCmd(cmd: FCRemoveDocCmd) {
        const l = cmd.state.getIdsList();
        for (const id of l) {
            await this.editor.internalRemoveDoc(cmd.meta.viewport.id, id.getLocalId());
        }
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    async processReloadCmd(cmd: FCReloadDocCmd): Promise<FCReloadDocCmd> {
        const promises = cmd.state
            .getLocalidList()
            .map(localId => this.editor.crdFeature.reloadLocalDoc(this.editor, cmd.meta.viewport.id, localId));
        await Promise.all(promises);

        this.editor.insertDocDelegator.notifyDocCreation(cmd.meta.viewport, ...cmd.state.getLocalidList());

        return cmd;
    }

    private async processUpdateBoundaryCmd(cmd: FCUpdateDocCmd) {
        this.editor.boundaryDelegator.processUpdateBoundaryCmd(cmd, MathGraphEditor.cmdChannelThrottle);
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
        return cmd;
    }

    private processUpdateContentCmd(cmd: UpdateContentCmd) {
        const localId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;

        const docRegistry = this.editor.regMan.registry<MathGraphDocCtrl>(maghDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(localId);

        const cmdState = cmd.state;
        if (cmdState.getVersion() > docCtrl.state.version) {
            docCtrl.state.version = cmdState.getVersion();
            const plotIndex = cmdState.getPlotIndex();
            const serialized = cmd.getSerializedEquation();

            docCtrl.maghLib.updateEquationAt(plotIndex, serialized);
            docCtrl.maghLib.reloadChart();
        }
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private updateDocInterpolationHandler: InterpolationHandler<MaghDocRenderProp> = new InterpolationHandler();

    private processUpdateDocStateCmd(cmd: UpdateDocStateCmd) {
        const localId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;
        const docRegistry = this.editor.regMan.registry<MathGraphDocCtrl>(maghDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(localId);
        const doc = cmd.state;
        const docRenderProp = {
            ...docCtrl.state.docRenderProp,
            ...convertProtoToDocRenderProp(doc.getDocRenderProp()),
        };

        if (this.isLocalCmd(cmd)) {
            docCtrl.state.docRenderProp = docRenderProp;
            docCtrl.maghLib.reloadChart();
        } else {
            this.updateDocInterpolationHandler.runInterpolation(
                docRenderProp,
                (preState, curState, ratio) =>
                    <MaghDocRenderProp>{
                        ...preState,
                        screenUnit: interpolate(preState.screenUnit, curState.screenUnit, ratio),
                        scale: interpolate(preState.scale, curState.scale, ratio),
                        translation: [
                            interpolate(preState.translation[0], curState.translation[0], ratio),
                            interpolate(preState.translation[1], curState.translation[1], ratio),
                        ],
                        rotation: [
                            interpolate(preState.rotation[0], curState.rotation[0], ratio),
                            interpolate(preState.rotation[1], curState.rotation[1], ratio),
                        ],
                    },
                state => {
                    docCtrl.state.docRenderProp = state;
                    docCtrl.maghLib.reloadChart();
                },
                () => structuredClone(docCtrl.state.docRenderProp),
                MathGraphEditor.cmdChannelThrottle
            );
        }
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    private processDeleteEquationCmd(cmd: DeleteEquationCmd) {
        const localId = cmd.meta.versionable;
        const vm: ViewportManager = cmd.meta.viewport;

        const docRegistry = this.editor.regMan.registry<MathGraphDocCtrl>(maghDocReg(vm.id));
        const docCtrl = docRegistry.getEntity(localId);

        const cmdState = cmd.state;
        if (cmdState.getVersion() > docCtrl.state.version) {
            const plotIndex = cmd.state.getPlotIndex();
            const deleted = docCtrl.maghLib.deleteEquation(plotIndex);
            if (deleted) {
                docCtrl.maghLib.reloadChart();
            }
        }
        this.clearCurrentViewportHistoryIfRemoteCmd(cmd.meta);
    }

    isLocalCmd(cmd: Cmd<any>) {
        return cmd.meta.origin === CmdOriginType.local;
    }

    private clearCurrentViewportHistoryIfRemoteCmd(meta: CmdMeta) {
        if (meta.origin === CmdOriginType.remote) this.editor.clearHistory(meta.viewport.id);
    }
}
