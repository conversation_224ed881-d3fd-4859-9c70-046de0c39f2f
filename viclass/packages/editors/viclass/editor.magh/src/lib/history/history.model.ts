import { DocLocalId, HistoryItem, SupportFeatureHistory, ViewportId } from '@viclass/editor.core';
import { SerializedEquation } from '../magh-lib';

export type MathGraphHistoryType = 'add' | 'update' | 'delete';

export class MathGraphHistoryItem implements HistoryItem {
    constructor(supporter: SupportFeatureHistory) {
        this.supporter = supporter;
    }

    supporter: SupportFeatureHistory;
    viewportId: ViewportId;
    docId: DocLocalId;

    type: MathGraphHistoryType = 'update';
    index: number = -1;

    snapshot: SerializedEquation[];
    prevSnapshot: SerializedEquation[] = [];
}
