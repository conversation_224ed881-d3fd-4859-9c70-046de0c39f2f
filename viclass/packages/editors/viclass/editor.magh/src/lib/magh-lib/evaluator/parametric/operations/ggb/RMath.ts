import { round } from 'interval-arithmetic';

/**
 * Utility class to determine the previous/next numbers
 * for algebra functions.
 *
 * <AUTHOR>
 */
export class RMath {
    /**
     *
     * @param v reference number
     * @returns previous number of v
     */
    public static prev = (v: number): number => {
        if (v === Infinity) {
            return v;
        }
        return round.prev(v);
    };

    /**
     *
     * @param v reference number
     * @return next number of v
     */
    public static next = (v: number): number => {
        if (v === -Infinity) {
            return v;
        }
        return round.next(v);
    };

    /**
     *
     * @param m nominator
     * @param n denominator
     * @return the previous number of m/n
     */
    public static divLow = (m: number, n: number): number => {
        return RMath.prev(m / n);
    };

    /**
     *
     * @param m nominator
     * @param n denominator
     * @return the next number of m/n
     */
    public static divHigh = (m: number, n: number): number => {
        return RMath.next(m / n);
    };

    /**
     *
     * @param m argument
     * @param n argument
     * @return the previous number of m * n
     */
    public static mulLow = (m: number, n: number): number => {
        return RMath.prev(m * n);
    };

    /**
     *
     * @param m argument
     * @param n argument
     * @return the next number of m * n
     */
    public static mulHigh = (m: number, n: number): number => {
        return RMath.next(m * n);
    };

    /**
     *
     * @param n any number.
     * @param power to raise of.
     * @return the previous number of n^{power}
     */
    public static powLow = (n: number, power: number): number => {
        return RMath.prev(Math.pow(n, power));
    };

    /**
     *
     * @param n any number.
     * @param power to raise of.
     * @return the next number of n^{power}
     */
    public static powHigh = (n: number, power: number): number => {
        return RMath.next(Math.pow(n, power));
    };

    public static subHigh = (m: number, n: number): number => {
        return RMath.next(m - n);
    };

    public static cosLow = (x: number): number => {
        return RMath.prev(Math.cos(x));
    };

    public static cosHigh = (x: number): number => {
        return RMath.next(Math.cos(x));
    };

    public static tanLow = (x: number): number => {
        return RMath.prev(Math.tan(x));
    };

    public static tanHigh = (x: number): number => {
        return RMath.next(Math.tan(x));
    };

    public static asinLow = (x: number): number => {
        return RMath.prev(Math.asin(x));
    };

    public static asinHigh = (x: number): number => {
        return RMath.prev(Math.asin(x));
    };

    public static acosLow = (x: number): number => {
        return RMath.prev(Math.acos(x));
    };

    public static acosHigh = (x: number): number => {
        return RMath.next(Math.acos(x));
    };

    public static atanLow = (x: number): number => {
        return RMath.prev(Math.atan(x));
    };

    public static atanHigh = (x: number): number => {
        return RMath.next(Math.atan(x));
    };

    public static sinhLow = (x: number): number => {
        return RMath.prev(Math.sinh(x));
    };

    public static sinhHigh = (x: number): number => {
        return RMath.next(Math.sinh(x));
    };

    public static coshLow = (x: number): number => {
        return RMath.prev(Math.cosh(x));
    };

    public static coshHigh = (x: number): number => {
        return RMath.next(Math.cosh(x));
    };

    public static tanhLow = (x: number): number => {
        return RMath.prev(Math.tanh(x));
    };

    public static tanhHigh = (x: number): number => {
        return RMath.next(Math.tanh(x));
    };

    public static expLow = (x: number): number => {
        return RMath.prev(Math.exp(x));
    };

    public static expHigh = (x: number): number => {
        return RMath.next(Math.exp(x));
    };

    public static logLow = (x: number): number => {
        return RMath.prev(Math.log(x));
    };

    public static logHigh = (x: number): number => {
        return RMath.next(Math.log(x));
    };

    public static secLow = (x: number): number => {
        return RMath.prev(1 / Math.cos(x));
    };

    public static secHigh = (x: number): number => {
        return RMath.next(1 / Math.cos(x));
    };

    public static nrootLow = (x: number, n: number): number => {
        return RMath.powLow(x, 1 / n);
    };

    public static nrootHigh = (x: number, n: number): number => {
        return RMath.powHigh(x, 1 / n);
    };
}
