import { EuclidianView } from '../../../chart';
import { DoubleUtil, MyPoint, SegmentType } from '../../../common';
import { Gap } from './Gap';
import { PathPlotter } from './PathPlotter';

/**
 * General path clipped with methods for CurvePlotter
 */
export class GeneralPathClippedForCurvePlotter implements PathPlotter {
    public static readonly MIN_PIXEL_DISTANCE: number = 2; // pixels
    private static readonly EPSILON: number = 0.0001;

    private lineDrawn: boolean = false;

    private points: MyPoint[] = [];

    /**
     * constructor
     *
     * @param view
     *            Euclidian view
     */
    public constructor(private view: EuclidianView) {}

    public reset(): void {
        this.lineDrawn = false;
        this.points = [];
    }

    public getPoints(): MyPoint[] {
        return this.points;
    }

    public lineTo = (pos: number[]): void => {
        this.drawTo(pos[0], pos[1], true);
    };

    public moveTo = (pos: number[]): void => {
        this.drawTo(pos[0], pos[1], false);
    };

    /**
     *
     * @param x coordinate.
     * @param y coordinate.
     * @param lineTo type for drawing the segment
     */
    drawTo(x: number, y: number, lineTo: boolean): void {
        this.drawToType(x, y, lineTo ? SegmentType.LINE_TO : SegmentType.MOVE_TO);
    }

    /**
     *
     * @param x coordinate.
     * @param y coordinate.
     * @param lineTo type for drawing the segment
     */
    drawToType(x: number, y: number, segmentType: SegmentType): void {
        const point = this.getCurrentPoint()!;

        // no points in path yet
        if (point === null) {
            this.points.push(new MyPoint(x, y, SegmentType.MOVE_TO));
            this.lineDrawn = false;
            return;
        }

        let distant: boolean =
            !DoubleUtil.isEqual(
                x,
                point.getX(),
                GeneralPathClippedForCurvePlotter.MIN_PIXEL_DISTANCE / this.view.zoomUnit
            ) ||
            !DoubleUtil.isEqual(
                y,
                point.getY(),
                GeneralPathClippedForCurvePlotter.MIN_PIXEL_DISTANCE / this.view.zoomUnit
            );
        if (
            segmentType === SegmentType.CONTROL ||
            segmentType === SegmentType.CURVE_TO ||
            segmentType === SegmentType.ARC_TO ||
            segmentType === SegmentType.AUXILIARY
        ) {
            distant = true;
        }

        // only add points that are more than MIN_PIXEL_DISTANCE
        // from current location]
        const isLine: boolean = segmentType !== SegmentType.MOVE_TO;
        if (!distant && isLine === this.lineDrawn) {
            return;
        }

        if (segmentType === SegmentType.CONTROL || segmentType === SegmentType.CURVE_TO) {
            this.addPoint(x, y, segmentType);
            this.lineDrawn = true;
            return;
        }

        if (isLine) {
            // Safari does not draw points with moveTo lineTo
            if (x === point.getX() && y === point.getY()) {
                this.addPoint(x + GeneralPathClippedForCurvePlotter.EPSILON, y, segmentType);
            } else {
                this.addPoint(x, y, segmentType);
            }
            this.lineDrawn = true;
        } else {
            this.points.push(new MyPoint(x, y, SegmentType.MOVE_TO));
            this.lineDrawn = false;
        }
    }

    private addPoint(x: number, y: number, segmentType: SegmentType): void {
        this.points.push(new MyPoint(x, y, segmentType));
    }

    public corner(): void;

    public corner(pos: number[]): void;

    public corner(x0: number, y0: number): void;

    public corner(...args: unknown[]): void {
        switch (args.length) {
            case 0: {
                const fp = this.getFirstPoint();
                if (fp !== null) {
                    this.corner(fp.x, fp.y);
                    this.closePath();
                }

                break;
            }

            case 1: {
                const [pos] = args as [number[]];

                const x: number = pos[0];
                const y: number = pos[1];
                this.corner(x, y);

                break;
            }

            case 2: {
                const [x0, y0] = args as [number, number];

                const minX: number = this.view.getXmin();
                const maxX: number = this.view.getXmax();
                const minY: number = this.view.getYmin();
                const maxY: number = this.view.getYmax();
                const pt: MyPoint = this.getCurrentPoint()!;
                if (pt === null) {
                    return;
                }
                const x: number = pt.getX();
                const y: number = pt.getY();

                if ((x < minX && x0 > maxX) || (x > maxX && x0 < minX)) {
                    this.drawTo(x, -10 / this.view.zoomUnit, true);
                    this.drawTo(x0, -10 / this.view.zoomUnit, true);
                    return;
                }

                if ((y < minY && y0 > maxY) || (y > maxY && y0 < minY)) {
                    this.drawTo(-10 / this.view.zoomUnit, y, true);
                    this.drawTo(-10 / this.view.zoomUnit, y0, true);
                    return;
                }

                if ((x > maxX || x < minX) && (y0 < minY || y0 > maxY)) {
                    this.drawTo(x, y0, true);
                    return;
                }

                if ((x0 > maxX || x0 < minX) && (y < minY || y > maxY)) {
                    this.drawTo(x0, y, true);
                }

                break;
            }

            default: {
                throw `Invalid number of arguments`;
            }
        }
    }

    getFirstPoint(): MyPoint | null {
        return this.points[0] ?? null;
    }

    getCurrentPoint(): MyPoint | null {
        return this.points[this.points.length - 1] ?? null;
    }

    public firstPoint = (pos: number[], moveToAllowed: Gap): void => {
        const x0: number = pos[0];
        const y0: number = pos[1];

        // FIRST POINT
        // c(t1) and c(t2) are defined, lets go ahead and move to our first
        // point (x0, y0)
        // note: lineTo will automatically do a moveTo if this is the first gp
        // point
        if (moveToAllowed === Gap.MOVE_TO) {
            this.drawTo(x0, y0, false);
        } else {
            if (moveToAllowed === Gap.LINE_TO || moveToAllowed === Gap.CORNER) {
                this.drawTo(x0, y0, true);
            } else {
                if (moveToAllowed === Gap.RESET_XMIN) {
                    const d: number = this.getCurrentPoint()!.getY();
                    if (!DoubleUtil.isEqual(d, y0)) {
                        this.drawTo(-10, d, true);
                        this.drawTo(-10, y0, true);
                    }
                    this.drawTo(x0, y0, true);
                } else {
                    if (moveToAllowed === Gap.RESET_XMAX) {
                        const d: number = this.getCurrentPoint()!.getY();
                        if (!DoubleUtil.isEqual(d, y0)) {
                            this.drawTo(this.view.getChartWidth() + 10, d, true);
                            this.drawTo(this.view.getChartWidth() + 10, y0, true);
                        }
                        this.drawTo(x0, y0, true);
                    } else {
                        if (moveToAllowed === Gap.RESET_YMIN) {
                            const d: number = this.getCurrentPoint()!.getX();
                            if (!DoubleUtil.isEqual(d, x0)) {
                                this.drawTo(d, -10, true);
                                this.drawTo(x0, -10, true);
                            }
                            this.drawTo(x0, y0, true);
                        } else {
                            if (moveToAllowed === Gap.RESET_YMAX) {
                                const d: number = this.getCurrentPoint()!.getX();
                                if (!DoubleUtil.isEqual(d, x0)) {
                                    this.drawTo(this.getCurrentPoint()!.getX(), this.view.getChartHeight() + 10, true);
                                    this.drawTo(x0, this.view.getChartHeight() + 10, true);
                                }
                                this.drawTo(x0, y0, true);
                            }
                        }
                    }
                }
            }
        }
    };

    public newDoubleArray = (): number[] => {
        return [];
    };

    public copyCoords = (point: MyPoint, ret: number[]): boolean => {
        ret[0] = point.x;
        ret[1] = point.y;
        return true;
    };

    public endPlot = (): void => {
        // TODO Auto-generated method stub
    };

    public closePath() {
        this.getFirstPoint()?.setLineTo(true);
    }
}
