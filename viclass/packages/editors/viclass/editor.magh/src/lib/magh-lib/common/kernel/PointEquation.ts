import { Engine, EqType, Equation, GlobalScopeGetter, MathJsonExpr, PointEquationStyle } from '..';
import { MaghDocRenderProp } from '../../../model';
import { EuclidianView } from '../../chart/EuclidianView';
import { drawLabel, drawLine } from '../draw/utils';
import { PointEquationAnalyzer } from './analyzers';

/**
 * Represents a point on the chart with coordinates and visual properties
 */
export class PointEquation extends Equation<PointEquationStyle> {
    private _analyzer: PointEquationAnalyzer;

    override get equationType(): EqType {
        return EqType.Point;
    }

    override get isValid(): boolean {
        return this._analyzer.valid;
    }

    public override get defaultExpression(): string {
        return '\\placeholder{}=\\left(\\placeholder{},\\placeholder{}\\right)';
    }

    get x() {
        return this._analyzer.valid ? this.evaluateLhs({}) : NaN;
    }

    get y() {
        return this._analyzer.valid ? this.evaluateRhs({}) : NaN;
    }

    get pointName() {
        return this._analyzer.valid ? this._analyzer.pointName : '';
    }

    override styles: PointEquationStyle = {
        size: 4,
        color: '#121414', // the black color in pallete
        lineWidth: 2,
        lineStyle: 'dashed',
        showAxisLines: true,
        showLabel: true,
    };

    constructor(engine: Engine, scopeGetter: GlobalScopeGetter) {
        super(engine, scopeGetter);
        this._analyzer = new PointEquationAnalyzer(this, this.engine);
        this.setExpression(this.defaultExpression);

        this._analyzer.analyze();
    }

    public override setExpression(rawExpr: string): boolean {
        this._raw = rawExpr;
        if (!rawExpr) return false;

        try {
            // analyze and apply transformation if necessary
            const mathJson = this._analyzer.analyze();
            if (!this.isValid) return false;

            this.compileEquation(mathJson);
            return true;
        } catch (e) {
            this.reset();

            console.warn('compile failed', e);
            return false;
        }
    }

    /**
     * Compile the math expression for function evaluation.
     * Here left-hand-sign (lhs) will be `x` and right-hand-sign (rhs) will be `y`
     */
    private compileEquation(mathJson: MathJsonExpr): void {
        if (!this.isValid) return;

        let lhs: MathJsonExpr;
        let rhs: MathJsonExpr;

        if (!Array.isArray(mathJson)) throw new Error('invalid equation for simplify');

        lhs = mathJson[1] as MathJsonExpr;
        rhs = mathJson[2] as MathJsonExpr;

        this._lhs = this.engine.box(lhs);
        this._rhs = this.engine.box(rhs);

        this._compiledLhs = this.lhs?.compile() as any;
        this._compiledRhs = this.rhs?.compile() as any;
    }

    protected override reset(): void {
        super.reset();
        this._analyzer?.reset();
    }

    override updatePath(view: EuclidianView): void {
        // nothing to update
    }

    /**
     * Draw the point on the canvas
     */
    override draw(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp): void {
        if (this.hidden || !this.isValid) return;

        const vpScale = view.getViewportScale();
        const x = view.chartToLayerX(this.x);
        const y = view.chartToLayerY(this.y);
        const radius = (this.styles.size ?? 5) * vpScale;

        // Draw axis lines if enabled
        if (this.styles.showAxisLines) {
            const axisX = view.chartToLayerX(0);
            const axisY = view.chartToLayerY(0);
            const lineWeight = (this.styles.lineWidth || 1) * vpScale;
            const lineDash = this.getLineDashes(this.styles.lineStyle, lineWeight);

            // Vertical line from point to X-axis
            drawLine(ctx, x, y, x, axisY, {
                color: this.styles.color,
                lineWeight,
                lineDash,
            });

            // Horizontal line from point to Y-axis
            drawLine(ctx, axisX, y, x, y, {
                color: this.styles.color,
                lineWeight,
                lineDash,
            });
        }

        // Draw the point with white stroke
        ctx.beginPath();
        ctx.fillStyle = this.styles.color;
        ctx.strokeStyle = '#fff';
        ctx.lineWidth = 1 * vpScale;
        ctx.arc(x, y, radius, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
    }

    override drawLabels(ctx: CanvasRenderingContext2D, view: EuclidianView, drp: MaghDocRenderProp) {
        const vpScale = view.getViewportScale();
        const x = view.chartToLayerX(this.x);
        const y = view.chartToLayerY(this.y);
        const radius = (this.styles.size ?? 5) * vpScale;

        // Draw label if provided
        if (this.styles.showLabel && this.pointName) {
            drawLabel(
                ctx,
                this.pointName,
                { x: x + 2 * radius + 2 * vpScale, y: y - 2 * radius - 2 * vpScale },
                {
                    fontSize: 14 * vpScale,
                    textColor: this.styles.color,
                    strokeText: false,
                }
            );
        }
    }

    private getLineDashes(key: string, scale: number): number[] {
        const dash = 2 * scale;
        const gap = 2 * scale;
        const dot = 0; // will be draw as a dot because linecap is round

        switch (key) {
            case 'dashed':
                return [dash, gap];
            case 'dotted':
                return [dot, gap];
            case 'dashed-dotted':
                return [dash, gap, dot, gap];
            default:
                return [];
        }
    }
}
