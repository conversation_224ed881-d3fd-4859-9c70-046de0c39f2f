import { constants, Interval, isEmpty } from 'interval-arithmetic';
import { EuclidianView } from '../../chart';
import { MyPoint } from '../../common';
import { isGreaterThan } from '../../evaluator';
import { IntervalPathPlotter } from './IntervalPathPlotter';

export class DrawInterval {
    static readonly CLAMPED_INFINITY = Number.MAX_SAFE_INTEGER;

    private joinToPrevious: boolean = false;

    private plotter = new IntervalPathPlotter();

    public setJoinToPrevious = (joinToPrevious: boolean): void => {
        this.joinToPrevious = joinToPrevious;
    };

    /**
     * Draw interval (x, y) joined to the last y value
     * @param lastY the last y value to join to.
     * @param x interval
     * @param y interval
     */
    public drawJoined = (lastY: Interval, x: Interval, y: Interval): MyPoint[] => {
        if (isGreaterThan(y, lastY)) {
            return this.drawUp(x, y);
        } else {
            return this.drawDown(x, y);
        }
    };

    protected drawUp = (x: Interval, y: Interval): MyPoint[] => {
        const res = [];
        if (this.joinToPrevious) {
            res.push(this.lineTo(x.lo, y.lo));
        } else {
            res.push(this.moveTo(x.lo, y.lo));
        }

        res.push(this.lineTo(x.hi, y.hi));
        return res;
    };

    protected drawDown = (x: Interval, y: Interval): MyPoint[] => {
        const res = [];
        if (this.joinToPrevious) {
            res.push(this.lineTo(x.lo, y.hi));
        } else {
            res.push(this.moveTo(x.lo, y.hi));
        }

        res.push(this.lineTo(x.hi, y.lo));
        return res;
    };

    private moveTo = (low: number, high: number): MyPoint => {
        return this.plotter.moveTo(this.clamp(low), this.clamp(high));
    };

    protected lineTo = (low: number, high: number): MyPoint => {
        return this.plotter.lineTo(this.clamp(low), this.clamp(high));
    };

    /**
     * Draws a whole interval (from screen top to bottom) at x.low
     * @param x interval
     */
    public drawWhole = (view: EuclidianView, x: Interval): MyPoint[] => {
        return this.plotter.segmentWithBounds(view, x.lo, view.getYmin(), x.lo, view.getYmax());
    };

    public drawIndependent = (x: Interval, y: Interval): [Interval, MyPoint[]] => {
        if (isEmpty(y)) {
            return [constants.EMPTY, []];
        } else {
            return [y, this.line(x, y)];
        }
    };

    /**
     * Draws straight vertical line at x.high of interval y.
     *
     * @param x interval
     * @param y interval
     */
    public line = (x: Interval, y: Interval): MyPoint[] => {
        return [this.moveTo(x.hi, y.lo), this.lineTo(x.hi, y.hi)];
    };

    private clamp = (value: number): number => {
        if (value === Infinity) {
            return DrawInterval.CLAMPED_INFINITY;
        }

        if (value === -Infinity) {
            return -DrawInterval.CLAMPED_INFINITY;
        }
        return value;
    };

    public leftToTop(view: EuclidianView, x: Interval, y: Interval): MyPoint[] {
        return this.plotter.leftToTop(view, x, y);
    }

    public leftToBottom(view: EuclidianView, x: Interval, y: Interval): MyPoint[] {
        return this.plotter.leftToBottom(view, x, y);
    }
}
