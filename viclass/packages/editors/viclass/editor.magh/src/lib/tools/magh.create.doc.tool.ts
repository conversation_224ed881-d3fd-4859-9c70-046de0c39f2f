import {
    CRD<PERSON><PERSON>,
    <PERSON><PERSON>or,
    Mouse<PERSON><PERSON>L<PERSON>ener,
    NativeEventTarget,
    newCursor,
    pointerTypeDyn,
    pointerTypeMouse,
    pointerTypePen,
    pointerType<PERSON>enMouse,
    ToolState,
    UIPointerEventData,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { MathGraphEditor } from '../magh.editor';
import { MathGraphMouseEvent } from '../model';
import { MathGraphTool } from './magh.tool';
import { MathGraphToolType } from './models';

export class MathGraphCreateDocumentTool extends MathGraphTool<ToolState> {
    readonly toolType: MathGraphToolType = 'CreateMathGraphDocumentTool';
    private delegator: CRDTool;

    protected readonly defaultCursor = [newCursor('cursor_add')];

    constructor(editor: MathGraphEditor) {
        super(editor);
        this.registerPointerHandling(
            { event: 'pointerdown', button: 0, pointerTypes: pointerType<PERSON><PERSON>Mouse },
            { event: 'pointerup', button: 0, pointerTypes: pointerType<PERSON>enMouse },
            { event: 'pointermove', pressedButtons: 1, pointerTypes: pointerTypeMouse },
            { event: 'pointermove', pointerTypes: pointerTypePen, numPointer: 1 },

            { event: 'pointerdown', pointerTypes: pointerTypeDyn, numTouch: 1 },
            { event: 'pointerup', pointerTypes: pointerTypeDyn, numTouch: 0 },
            { event: 'pointermove', pointerTypes: pointerTypeDyn, numTouch: 1 }
        );
    }

    override mouseHandler = new (class implements MouseEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject<Cursor[] | undefined>([newCursor('crosshair', 0, '', 0, 'system')]);

        constructor(public tool: MathGraphCreateDocumentTool) {}

        onEvent(event: MathGraphMouseEvent): MathGraphMouseEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleMouseEvent(event);
        }
    })(this);

    get toolState(): any {
        return {};
    }

    override onAttachViewport(): void {
        this.delegator = this.editor.initCreateDocTool(this.toolbar.viewport);
        this.delegator.toolbar = this.toolbar;
    }

    override onDetachViewport(): void {
        this.delegator.destroy(this.toolbar.viewport);
    }

    override onBlur(): void {
        this.delegator?.resetState();
    }

    override onDisable(): void {
        this.delegator?.resetState();
    }

    override onFocus(): void {
        this.delegator?.onFocusCreateDocTool();
    }

    override handlePointerEvent(event: UIPointerEventData<any>): UIPointerEventData<any> {
        return this.delegator.handlePointerEvent(event);
    }
}
