import {
    <PERSON>urs<PERSON>,
    <PERSON>L<PERSON>alId,
    InferredPointerEvent,
    KeyboardEventListener,
    KeyboardHandlingItem,
    MouseEventData,
    MouseEventListener,
    MouseHandlingItem,
    NativeEventTarget,
    NumDPointerChange,
    PointerEventData,
    PointerEventListener,
    PointerHandlingItem,
    Tool,
    ToolState,
    UserInputHandlerType,
    ViewportId,
    ViewportManager,
} from '@viclass/editor.core';
import { BehaviorSubject } from 'rxjs';
import { MathGraphDocCtrl } from '../docs/magh.doc.ctrl';
import { MathGraphEditor } from '../magh.editor';
import { maghDocReg } from '../magh.utils';
import { MathGraphKeyboardEvent } from '../model';
import { MathGraphToolBar } from './magh.toolbar';
import { MaghMouseEvent, MathGraphToolEventData, MathGraphToolType } from './models';

export abstract class MathGraphTool<TState extends ToolState> implements Tool {
    readonly type: UserInputHandlerType = 'Tool';
    abstract readonly toolType: MathGraphToolType;
    abstract readonly toolState: TState;
    mouseHandling: MouseHandlingItem[] = [];
    keyboardHandling: KeyboardHandlingItem[] = [];
    pointerHandling?: PointerHandlingItem[] = [];

    toolbar: MathGraphToolBar;

    protected lastPointerMove: MouseEventData<NativeEventTarget<any>>;
    protected requestedFrame: boolean;
    protected started: boolean = false;

    constructor(public editor: MathGraphEditor) {}

    readonly mouseHandler = new (class implements MouseEventListener<NativeEventTarget<any>> {
        constructor(public tool: MathGraphTool<ToolState>) {}

        onEvent(event: MaghMouseEvent): MaghMouseEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            return this.tool.handleMouseEvent(event);
        }
    })(this);

    readonly keyboardHandler = new (class implements KeyboardEventListener<NativeEventTarget<any>> {
        constructor(public tool: MathGraphTool<ToolState>) {}

        onEvent(event: MathGraphKeyboardEvent): MathGraphKeyboardEvent {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;

            return this.tool.handleKeyboardEvent(event);
        }
    })(this);

    readonly pointerHandler = new (class implements PointerEventListener<NativeEventTarget<any>> {
        cursor = new BehaviorSubject<Cursor[] | undefined>(undefined);

        constructor(public tool: MathGraphTool<ToolState>) {}

        onEvent(event: PointerEventData<NativeEventTarget<any>>): PointerEventData<NativeEventTarget<any>> {
            if (this.tool.toolbar.isDisabled() || this.tool.toolbar.isToolDisable(this.tool.toolType)) return event;
            if ('nativeEvent' in event)
                // handle UI event
                return this.tool.handlePointerEvent(event);
            else return this.tool.handleNonUIPointerEvent(event);
        }
    })(this);

    protected getMathGraphDoc(vm: ViewportManager, localId: DocLocalId): MathGraphDocCtrl {
        return this.editor.regMan.registry<MathGraphDocCtrl>(maghDocReg(vm.id))?.getEntity(localId);
    }

    registerMouseHandling(...handling: MouseHandlingItem[]) {
        if (!this.mouseHandling) this.mouseHandling = [];
        this.mouseHandling.push(...handling);
    }

    registerKeyboardHandling(...handling: KeyboardHandlingItem[]) {
        if (!this.keyboardHandling) this.keyboardHandling = [];
        this.keyboardHandling.push(...handling);
    }

    registerPointerHandling(...handling: PointerHandlingItem[]) {
        this.pointerHandling.push(...handling);
    }

    registerToolbar(toolbar: MathGraphToolBar) {
        this.toolbar = toolbar;
    }

    onBlur() {}
    onFocus() {}

    onDisable() {}
    onEnable() {}

    resetState() {}

    onAttachViewport() {}

    onDetachViewport() {}

    focusAble(vpId: ViewportId): boolean {
        if (this.toolbar.isDisabled() || this.toolbar.isToolDisable(this.toolType)) return false;

        return this.editor.selectDelegator.getFocusedDocs(vpId)?.length == 1 && true;
    }

    protected getFocusedMathGraphDocCtrls(): MathGraphDocCtrl[] {
        const vm = this.toolbar.viewport;

        if (!vm) {
            throw new Error('The toolbar of the magh editor has not been attached to any viewport.');
        }

        return this.editor.selectDelegator.getFocusedDocs(vm.id);
    }

    protected executeInFocusedDocCtrl(cb: (doc: MathGraphDocCtrl) => void, refocus = false): void {
        const docCtrls = this.getFocusedMathGraphDocCtrls();
        if (!docCtrls?.length) return;

        cb(docCtrls[0]);

        if (refocus) {
            // after update with using UI, the document might focus on the UI element
            // -> refocus on the viewport to ready for the next hotkeys
            docCtrls[0].viewport.rootEl.focus({ preventScroll: true });
        }
    }

    handleKeyboardEvent(event: MathGraphKeyboardEvent): MathGraphKeyboardEvent {
        return event;
    }

    handlePointerEvent(event: PointerEventData<any>): PointerEventData<any> {
        return event;
    }

    handleNonUIPointerEvent(event: NumDPointerChange | InferredPointerEvent): NumDPointerChange | InferredPointerEvent {
        return event;
    }

    handleMouseEvent(event: MaghMouseEvent): MaghMouseEvent {
        return event;
    }

    handleToolEvent(event: MathGraphToolEventData): MathGraphToolEventData {
        switch (event.eventType) {
            case 'change': {
                this.processChangeToolEvent(event);
                break;
            }
            default:
                break;
        }

        return event;
    }

    protected async processChangeToolEvent(event: MathGraphToolEventData): Promise<MathGraphToolEventData> {
        return event;
    }
}
