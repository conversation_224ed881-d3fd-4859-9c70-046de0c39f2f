import {
    cmdMeta,
    DefaultVDocCtrl,
    mouseLocation,
    newCursor,
    reliableSaveCmdMeta,
    UIPointerEventData,
    ViewportManager,
} from '@viclass/editor.core';
import { CmdTypeProto } from '@viclass/proto/editor.freedrawing';

import { InsertLineObjV2Cmd, StartPreviewCmd, UpdatePreviewCmd } from '../cmd/freedrawing.cmd';
import { FreedrawingEditor } from '../freedrawing.editor';
import {
    CommonToolState,
    FreedrawingHistoryItem,
    FreedrawingToolEventData,
    LineObjV2,
    LineV2ToolState as LineV2ToolState,
    ShapeObj,
} from '../freedrawing.models';
import { FreedrawingToolBar } from '../freedrawing.toolbar';
import { freedrawingObjectReg } from '../freedrawing.util';
import { LineObjV2Ctrl } from '../objects/freedrawing.line.v2.obj.ctrl';
import { FreedrawingObjCtrl } from '../objects/freedrawing.obj.ctrl';
import { ShapeObjCtrl } from '../objects/freedrawing.shape.obj.ctrl';
import { FreedrawingTool } from './freedrawing.tool';

export const LINE_V2_PREVIEW_OBJ_ID = -2;

export class LineV2Tool extends FreedrawingTool<LineV2ToolState, LineObjV2Ctrl> {
    readonly toolType = 'LineV2Tool';

    private lastPointerMove: UIPointerEventData<any>;
    private requestedFrame: boolean;

    constructor(editor: FreedrawingEditor, toolbar: FreedrawingToolBar) {
        super(editor, toolbar);

        this.updateCursor([newCursor('crosshair', 0, '', 0, 'system')]);
    }

    get toolState(): LineV2ToolState {
        const lineStates = this.toolbar.toolState('LineV2Tool') as LineV2ToolState;
        const commonStates = this.toolbar.toolState('CommonPropertiesTool') as CommonToolState;

        // use the common tool state for shared properties, only specific props
        return Object.assign(lineStates, {
            stroke: commonStates.stroke,
            lineWidth: commonStates.lineWidth,
        });
    }

    override onBlur() {
        const vm = this.toolbar.viewport;
        super.onBlur();
        this.endPreviewAndInsert(vm);
    }

    private endPreviewAndInsert(vm: ViewportManager) {
        const previewing = this.curPreviewObjectCtrl(vm);

        if (!previewing) return;

        const docCtrl = this.curDoc(vm);
        const objectRegistry = this.editor.regMan.registry<FreedrawingObjCtrl<any>>(
            freedrawingObjectReg(vm.id, docCtrl.state.id)
        );

        // insert current previewing object
        const obj: LineObjV2 = new LineObjV2(objectRegistry.getAndIncrementId());
        Object.assign(obj, {
            toolType: previewing.state.toolType,
            boundary: previewing.state.boundary,
            toolState: previewing.state.toolState,
        });

        const layer = this.topLayer(previewing.document as DefaultVDocCtrl);

        // end preview so we target id is previewing.id. The id of object to be inserted is inside the state of the command
        const meta = reliableSaveCmdMeta(
            previewing.layer.viewport,
            docCtrl.state,
            docCtrl.state.id,
            previewing.id,
            CmdTypeProto.INSERT_LINE_OBJ_V2
        );
        const insertCmd = new InsertLineObjV2Cmd(meta);
        insertCmd.setState(obj, layer.state.id);

        this.sendCommand(insertCmd);

        this.resetState();

        const items = this.editor.historyItems.get(vm.id);
        let firstObj = false;
        if (items) {
            firstObj = items[0].firstObj;
        }

        // create history item
        const state: ShapeObj = JSON.parse(JSON.stringify(obj));

        const item: FreedrawingHistoryItem = {
            supporter: this.editor,
            viewportId: vm.id,
            docId: docCtrl.state.id,
            layerId: layer.state.id,
            objId: obj.id,
            toolType: this.toolType,
            state: state,
            action: 'INSERT',
            firstObj: firstObj,
        };
        this.editor.addHistoryItem(item);
    }

    override onToolChange(event: FreedrawingToolEventData) {
        const vm = this.toolbar.viewport;

        if (!vm || !this.hasPreviewObjectCtrl(vm)) return;

        if (event.toolType !== 'LineV2Tool' && event.toolType !== 'CommonPropertiesTool') return;

        const previewing = this.curPreviewObjectCtrl(vm);

        const currToolState = this.toolState;
        const newToolState = { ...currToolState, ...event.state } as LineV2ToolState;
        const cmd = new UpdatePreviewCmd(
            cmdMeta(previewing.layer.viewport, previewing.id, CmdTypeProto.UPDATE_TOOL_STATE_PREVIEW)
        );
        cmd.updateToolState(this.toolType, newToolState);
        this.sendCommand(cmd);

        const layer = this.topLayer(previewing.document as DefaultVDocCtrl);

        // add history item
        const state: LineObjV2 = JSON.parse(JSON.stringify(previewing.state));
        state.toolState = JSON.parse(JSON.stringify(newToolState));

        const item: FreedrawingHistoryItem = {
            supporter: this.editor,
            viewportId: vm.id,
            docId: previewing.document.state.id,
            layerId: layer.state.id,
            objId: previewing.id,
            toolType: this.toolType,
            state: state,
            action: 'UPDATE_PROPERTY',
        };
        this.editor.addHistoryItem(item);
    }

    override async onPointerDown(event: UIPointerEventData<any>): Promise<boolean> {
        // end preview and insert current preview object if exist
        if (this.hasPreviewObjectCtrl(event.viewport)) {
            console.log('Going to call endPreviewAndInsert');
            this.endPreviewAndInsert(event.viewport);
        } else {
            if (this.needAsyncCreation(event.viewport)) {
                await this.doCreateAsync(event.viewport);
            }

            // create new preview object
            const pointerDownPos = mouseLocation(event);

            const obj: LineObjV2 = new LineObjV2(LINE_V2_PREVIEW_OBJ_ID);
            Object.assign(obj, {
                toolType: this.toolType,
                toolState: this.toolState,
                boundary: {
                    start: pointerDownPos,
                    end: pointerDownPos,
                },
            });

            // and start preview
            const docCtrl = this.curDoc(event.viewport);
            if (!docCtrl) return false;

            const meta = cmdMeta(event.viewport, obj.id, CmdTypeProto.START_PREVIEW);
            meta.versionable = docCtrl.state.id;

            const startCmd = new StartPreviewCmd(meta);
            startCmd.setState(this.toolType, obj);

            startCmd.setOnCreatePreviewObj(objCtrl => {
                const func = this.onCreatePreviewObj.bind(this);
                func(objCtrl);
            });

            this.sendCommand(startCmd);
        }

        return true;
    }

    private onCreatePreviewObj(previewObj: ShapeObjCtrl) {
        previewObj.boundaryChangeListener = this.boundaryChangeListener();
    }

    override onPointerMove(event: UIPointerEventData<any>): boolean {
        if (!super.onPointerMove(event)) return false;

        this.lastPointerMove = event;
        if (!this.requestedFrame) {
            requestAnimationFrame(() => {
                this.requestedFrame = false;
                console.log('Update preview on pointer move');
                this.updatePreview(this.lastPointerMove, false);
            });
            this.requestedFrame = true;
        }

        return true;
    }

    private updatePreview(event: UIPointerEventData<any>, endPoint: boolean = false) {
        // update preview object's state
        const previewing = this.curPreviewObjectCtrl(event.viewport);
        const pointerdownPos = mouseLocation(event);

        const obj: LineObjV2 = new LineObjV2(previewing.id);
        Object.assign(obj, {
            toolType: this.toolType,
            toolState: this.toolState,
            boundary: {
                start: previewing.state.boundary.start,
                end: pointerdownPos,
            },
        });

        const cmd = new UpdatePreviewCmd(cmdMeta(event.viewport, obj.id, CmdTypeProto.UPDATE_BOUNDARY_PREVIEW));
        cmd.updateBoundary(this.toolType, obj.boundary, endPoint);

        this.sendCommand(cmd);

        if (endPoint) {
            const layer = this.topLayer(previewing.document as DefaultVDocCtrl);

            // create history item
            const state: ShapeObj = JSON.parse(JSON.stringify(obj));

            const item: FreedrawingHistoryItem = {
                supporter: this.editor,
                viewportId: event.viewport.id,
                docId: previewing.document.state.id,
                layerId: layer.state.id,
                objId: previewing.id,
                toolType: this.toolType,
                state: state,
                action: 'UPDATE_BOUNDARY',
                firstObj: this.firstObj,
                newPreview: true,
            };
            this.editor.addHistoryItem(item);

            if (this.firstObj) {
                this.firstObj = false;
            }
        }
    }

    private resetState() {
        this.isPointerDown = false;
        this.requestedFrame = false;
        this.lastPointerMove = null;
    }

    override onPointerUp(event: UIPointerEventData<any>): boolean {
        if (this.hasPreviewObjectCtrl(event.viewport) && this.isPointerDown) {
            console.log('Update preview on pointer up');
            this.updatePreview(event, true);
        }

        return true;
    }
}
