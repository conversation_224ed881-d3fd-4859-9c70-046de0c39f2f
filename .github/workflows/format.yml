name: Format

on:
  push:
    branches:
      - main

jobs:
  format:
    runs-on: ubuntu-latest
    steps:
      - name: Check out code using Git
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          # Needs access to push to main
          token: ${{ secrets.FREDKBOT_GITHUB_TOKEN }}
      - uses: pnpm/action-setup@v3
      - uses: actions/setup-node@v4
        with:
          node-version: 18
          cache: 'pnpm'
      - run: pnpm i
      - name: Format with Prettier
        run: pnpm format
      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: '[ci] format'
          branch: ${{ github.head_ref }}
          commit_user_name: fredkbot
          commit_user_email: <EMAIL>
