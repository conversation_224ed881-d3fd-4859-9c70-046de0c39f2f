:host {
  --primary-color: #5898ff;
  --primary-color-dimmed: #c0c0f0;
  --primary-color-dark: var(--blue-500);
  --primary-color-light: var(--blue-100);
  --primary-color-reverse: #ffffff;

  --secondary-color: #ff8a65;
  --secondary-color-dimmed: #f0d5c5;
  --secondary-color-dark: var(--orange-500);
  --secondary-color-light: var(--orange-100);
  --secondary-color-reverse: #ffffff;

  --link-color: #5898ff;
  --link-color-dimmed: #c5c5c5;
  --link-color-dark: #121212;
  --link-color-light: #e2e2e2;
  --link-color-reverse: #ffffff;

  --semantic-blue: var(--blue-700);
  --semantic-red: var(--red-400);
  --semantic-orange: var(--orange-400);
  --semantic-green: var(--green-700);

  --neutral-100: #f5f5f5;
  --neutral-200: #eeeeee;
  --neutral-300: #e0e0e0;
  --neutral-400: #bdbdbd;
  --neutral-500: #9e9e9e;
  --neutral-600: #757575;
  --neutral-700: #616161;
  --neutral-800: #424242;
  --neutral-900: #212121;

  --red-25: #fff8f7;
  --red-50: #fff1ef;
  --red-100: #ffeae6;
  --red-200: #ffcac1;
  --red-300: #ffa495;
  --red-400: #ff7865;
  --red-500: #f21c0d;
  --red-600: #e50018;
  --red-700: #d30024;
  --red-800: #bd002c;
  --red-900: #a1002f;
  --orange-25: #fffbf8;
  --orange-50: #fff7f1;
  --orange-100: #fff3ea;
  --orange-200: #ffe1c9;
  --orange-300: #ffcca2;
  --orange-400: #ffb677;
  --orange-500: #fe9310;
  --orange-600: #f58700;
  --orange-700: #ea7c00;
  --orange-800: #dc6d00;
  --orange-900: #ca5b00;
  --brown-25: #fff8ef;
  --brown-50: #fff1df;
  --brown-100: #ffe9ce;
  --brown-200: #ebcca6;
  --brown-300: #cdaf8a;
  --brown-400: #af936f;
  --brown-500: #856a47;
  --brown-600: #7f5e34;
  --brown-700: #78511f;
  --brown-800: #6e4200;
  --brown-900: #593200;
  --yellow-25: #fffdf9;
  --yellow-50: #fffcf2;
  --yellow-100: #fffaec;
  --yellow-200: #fff2ce;
  --yellow-300: #ffe8ab;
  --yellow-400: #ffdf85;
  --yellow-500: #ffcf33;
  --yellow-600: #f1c000;
  --yellow-700: #dfb200;
  --yellow-800: #c9a000;
  --yellow-900: #ad8a00;
  --lime-25: #f4ffee;
  --lime-50: #e9ffdd;
  --lime-100: #ddffca;
  --lime-200: #a8fb6f;
  --lime-300: #94e659;
  --lime-400: #80d142;
  --lime-500: #63b215;
  --lime-600: #45a000;
  --lime-700: #268e00;
  --lime-800: #007417;
  --lime-900: #005321;
  --green-25: #f5fff5;
  --green-50: #ebffea;
  --green-100: #e0ffdf;
  --green-200: #a7ffa7;
  --green-300: #5afa65;
  --green-400: #45e953;
  --green-500: #17cf36;
  --green-600: #00b944;
  --green-700: #00a34a;
  --green-800: #008749;
  --green-900: #00653e;
  --teal-25: #f3ffff;
  --teal-50: #e6fffe;
  --teal-100: #d9fffe;
  --teal-200: #8dfffe;
  --teal-300: #57f4f4;
  --teal-400: #43e5e5;
  --teal-500: #17cfcf;
  --teal-600: #00c2c0;
  --teal-700: #00b5b1;
  --teal-800: #00a49e;
  --teal-900: #009087;
  --cyan-25: #f7fcff;
  --cyan-50: #eff8ff;
  --cyan-100: #e7f5ff;
  --cyan-200: #c2e6ff;
  --cyan-300: #95d5ff;
  --cyan-400: #61c4ff;
  --cyan-500: #13a7ec;
  --cyan-600: #069eda;
  --cyan-700: #0095c9;
  --cyan-800: #0088b2;
  --cyan-900: #0a7897;
  --blue-25: #f7faff;
  --blue-50: #eef5ff;
  --blue-100: #e5f1ff;
  --blue-200: #bfdbff;
  --blue-300: #92c2ff;
  --blue-400: #63a8ff;
  --blue-500: #0d80f2;
  --blue-600: #0077db;
  --blue-700: #006dc4;
  --blue-800: #0060a7;
  --blue-900: #005086;
  --indigo-25: #f8f7ff;
  --indigo-50: #f1efff;
  --indigo-100: #eae7ff;
  --indigo-200: #ccc3ff;
  --indigo-300: #ac99ff;
  --indigo-400: #916aff;
  --indigo-500: #63c;
  --indigo-600: #5a21b2;
  --indigo-700: #4e0b99;
  --indigo-800: #3b0071;
  --indigo-900: #220040;
  --purple-25: #fbf7ff;
  --purple-50: #f8f0ff;
  --purple-100: #f4e8ff;
  --purple-200: #e4c4ff;
  --purple-300: #d49aff;
  --purple-400: #c36aff;
  --purple-500: #a219e6;
  --purple-600: #9000c4;
  --purple-700: #7c009f;
  --purple-800: #600073;
  --purple-900: #3d0043;
  --magenta-25: #fff8fb;
  --magenta-50: #fff2f6;
  --magenta-100: #ffebf2;
  --magenta-200: #ffcddf;
  --magenta-300: #ffa8cb;
  --magenta-400: #ff7fb7;
  --magenta-500: #eb4799;
  --magenta-600: #da3689;
  --magenta-700: #c82179;
  --magenta-800: #b00065;
  --magenta-900: #8a004c;
}

@media (prefers-color-scheme: dark) {
  :host {
    --semantic-blue: var(--blue-700);
    --semantic-red: var(--red-400);
    --semantic-orange: var(--orange-400);
    --semantic-green: var(--green-700);

    --semantic-bg-blue: var(--blue-25);
    --semantic-bg-red: var(--red-25);
    --semantic-bg-orange: var(--orange-25);
    --semantic-bg-green: var(--green-25);

    --neutral-100: #121212;
    --neutral-200: #424242;
    --neutral-300: #616161;
    --neutral-400: #757575;
    --neutral-500: #9e9e9e;
    --neutral-600: #bdbdbd;
    --neutral-700: #e0e0e0;
    --neutral-800: #eeeeee;
    --neutral-900: #f5f5f5;
  }
}

:host([theme='dark']) {
  --semantic-blue: var(--blue-700);
  --semantic-red: var(--red-400);
  --semantic-orange: var(--orange-400);
  --semantic-green: var(--green-700);

  --semantic-bg-blue: var(--blue-25);
  --semantic-bg-red: var(--red-25);
  --semantic-bg-orange: var(--orange-25);
  --semantic-bg-green: var(--green-25);

  --neutral-100: #121212;
  --neutral-200: #424242;
  --neutral-300: #616161;
  --neutral-400: #757575;
  --neutral-500: #9e9e9e;
  --neutral-600: #bdbdbd;
  --neutral-700: #e0e0e0;
  --neutral-800: #eeeeee;
  --neutral-900: #f5f5f5;
}

/* @media (prefers-color-scheme: dark) {
  :host {
      --label-color: #fff;
      --active-label-color: #000;
      --menu-bg: #525252;
      --active-bg: #5898ff;
      --active-bg-dimmed: #5c5c5c;
  }
} */
